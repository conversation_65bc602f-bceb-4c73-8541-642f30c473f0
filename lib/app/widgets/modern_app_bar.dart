import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';

class ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool isTransparent;
  final bool hasGradient;
  final Gradient? customGradient;
  final bool showShadow;

  const ModernAppBar({
    Key? key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.showBackButton = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.isTransparent = false,
    this.hasGradient = false,
    this.customGradient,
    this.showShadow = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: _buildDecoration(),
      child: AppBar(
        title: titleWidget ?? (title != null ? _buildTitle() : null),
        centerTitle: centerTitle,
        leading: _buildLeading(context),
        actions: actions,
        backgroundColor: Colors.transparent,
        foregroundColor: foregroundColor ?? 
            (isTransparent || hasGradient ? AppColors.white : AppColors.textPrimary),
        elevation: 0,
        scrolledUnderElevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isTransparent || hasGradient 
              ? Brightness.light 
              : Brightness.dark,
          statusBarBrightness: isTransparent || hasGradient 
              ? Brightness.dark 
              : Brightness.light,
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: -0.5, end: 0);
  }

  Widget _buildTitle() {
    return Text(
      title!,
      style: AppTextStyles.titleLarge.copyWith(
        color: foregroundColor ?? 
            (isTransparent || hasGradient ? AppColors.white : AppColors.textPrimary),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) return leading;
    
    if (showBackButton && Navigator.of(context).canPop()) {
      return IconButton(
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back_ios_new_rounded,
          color: foregroundColor ?? 
              (isTransparent || hasGradient ? AppColors.white : AppColors.textPrimary),
        ),
        style: IconButton.styleFrom(
          backgroundColor: isTransparent || hasGradient 
              ? AppColors.white.withOpacity(0.1)
              : Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
      );
    }
    
    return null;
  }

  BoxDecoration? _buildDecoration() {
    if (isTransparent) {
      return null;
    }
    
    if (hasGradient) {
      return BoxDecoration(
        gradient: customGradient ?? AppColors.primaryGradient,
        boxShadow: showShadow ? [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      );
    }
    
    return BoxDecoration(
      color: backgroundColor ?? AppColors.surface,
      boxShadow: showShadow ? [
        BoxShadow(
          color: AppColors.shadowLight,
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ] : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(AppDimensions.appBarHeight);
}

// Specialized app bars for common use cases
class ModernGradientAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;

  const ModernGradientAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernAppBar(
      title: title,
      actions: actions,
      onBackPressed: onBackPressed,
      hasGradient: true,
      showShadow: true,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(AppDimensions.appBarHeight);
}

class ModernTransparentAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;

  const ModernTransparentAppBar({
    Key? key,
    this.title,
    this.actions,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernAppBar(
      title: title,
      actions: actions,
      onBackPressed: onBackPressed,
      isTransparent: true,
      showShadow: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(AppDimensions.appBarHeight);
}

class ModernSearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String hintText;
  final Function(String)? onSearchChanged;
  final VoidCallback? onSearchSubmitted;
  final List<Widget>? actions;

  const ModernSearchAppBar({
    Key? key,
    required this.hintText,
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.actions,
  }) : super(key: key);

  @override
  State<ModernSearchAppBar> createState() => _ModernSearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(AppDimensions.appBarHeight);
}

class _ModernSearchAppBarState extends State<ModernSearchAppBar> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.surface,
      elevation: 2,
      shadowColor: AppColors.shadowLight,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(Icons.arrow_back_ios_new_rounded),
      ),
      title: Container(
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.grey100,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: TextField(
          controller: _searchController,
          onChanged: widget.onSearchChanged,
          onSubmitted: (_) => widget.onSearchSubmitted?.call(),
          decoration: InputDecoration(
            hintText: widget.hintText,
            hintStyle: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
            prefixIcon: const Icon(
              Icons.search,
              color: AppColors.textSecondary,
            ),
            suffixIcon: _isSearching
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      widget.onSearchChanged?.call('');
                      setState(() => _isSearching = false);
                    },
                    icon: const Icon(
                      Icons.clear,
                      color: AppColors.textSecondary,
                    ),
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
          ),
          style: AppTextStyles.bodyMedium,
          onTap: () => setState(() => _isSearching = true),
        ),
      ),
      actions: widget.actions,
    ).animate().fadeIn(duration: 300.ms);
  }
}
