import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:ui';
import 'package:flutter_animate/flutter_animate.dart';

class TripCancellationDialog extends StatefulWidget {
  final Function(String) onCancellationConfirmed;
  final bool isDriver;

  const TripCancellationDialog({
    Key? key,
    required this.onCancellationConfirmed,
    this.isDriver = false,
  }) : super(key: key);

  @override
  State<TripCancellationDialog> createState() => _TripCancellationDialogState();
}

class _TripCancellationDialogState extends State<TripCancellationDialog> {
  String? selectedReason;
  String? otherReason;
  final TextEditingController _otherReasonController = TextEditingController();

  // Predefined cancellation reasons for users
  final List<String> userReasons = [
    'تغيير الخطط',
    'وقت انتظار طويل',
    'حجزت عن طريق الخطأ',
    'وجدت وسيلة نقل أخرى',
    'السعر مرتفع',
    'سبب آخر',
  ];

  // Predefined cancellation reasons for drivers
  final List<String> driverReasons = [
    'مشكلة في السيارة',
    'حركة مرور كثيفة',
    'موقع الراكب بعيد جداً',
    'ظروف طارئة',
    'الراكب لا يستجيب',
    'سبب آخر',
  ];

  @override
  void dispose() {
    _otherReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final reasons = widget.isDriver ? driverReasons : userReasons;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        title: Text(
          'لماذا تريد إلغاء الرحلة؟',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Container(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'يرجى اختيار سبب الإلغاء:',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 16),
              Container(
                height: 250,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: reasons.length,
                  itemBuilder: (context, index) {
                    final reason = reasons[index];
                    return RadioListTile<String>(
                      title: Text(reason),
                      value: reason,
                      groupValue: selectedReason,
                      onChanged: (value) {
                        setState(() {
                          selectedReason = value;
                          if (value != 'سبب آخر') {
                            otherReason = null;
                            _otherReasonController.clear();
                          }
                        });
                      },
                    );
                  },
                ),
              ),
              if (selectedReason == 'سبب آخر') ...[
                SizedBox(height: 8),
                TextField(
                  controller: _otherReasonController,
                  decoration: InputDecoration(
                    hintText: 'يرجى ذكر السبب',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  maxLines: 2,
                  onChanged: (value) {
                    setState(() {
                      otherReason = value;
                    });
                  },
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: selectedReason == null
                ? null
                : () {
                    final finalReason = selectedReason == 'سبب آخر' &&
                            otherReason != null &&
                            otherReason!.isNotEmpty
                        ? otherReason!
                        : selectedReason!;
                    widget.onCancellationConfirmed(finalReason);
                    Get.back();
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(
              'تأكيد الإلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
