import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';

enum ModernButtonType { primary, secondary, outline, text, gradient }
enum ModernButtonSize { small, medium, large }

class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ModernButtonType type;
  final ModernButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? customColor;
  final Widget? child;

  const ModernButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = ModernButtonType.primary,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  }) : super(key: key);

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildButton(context),
        );
      },
    );
  }

  Widget _buildButton(BuildContext context) {
    final theme = Theme.of(context);
    
    switch (widget.type) {
      case ModernButtonType.primary:
        return _buildPrimaryButton(theme);
      case ModernButtonType.secondary:
        return _buildSecondaryButton(theme);
      case ModernButtonType.outline:
        return _buildOutlineButton(theme);
      case ModernButtonType.text:
        return _buildTextButton(theme);
      case ModernButtonType.gradient:
        return _buildGradientButton(theme);
    }
  }

  Widget _buildPrimaryButton(ThemeData theme) {
    return Container(
      width: widget.isFullWidth ? double.infinity : null,
      height: _getButtonHeight(),
      decoration: BoxDecoration(
        color: widget.customColor ?? AppColors.primary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.isLoading ? null : widget.onPressed,
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getHorizontalPadding(),
              vertical: _getVerticalPadding(),
            ),
            child: _buildButtonContent(AppColors.textOnPrimary),
          ),
        ),
      ),
    );
  }

  Widget _buildSecondaryButton(ThemeData theme) {
    return Container(
      width: widget.isFullWidth ? double.infinity : null,
      height: _getButtonHeight(),
      decoration: BoxDecoration(
        color: AppColors.secondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.isLoading ? null : widget.onPressed,
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getHorizontalPadding(),
              vertical: _getVerticalPadding(),
            ),
            child: _buildButtonContent(AppColors.textOnPrimary),
          ),
        ),
      ),
    );
  }

  Widget _buildOutlineButton(ThemeData theme) {
    return Container(
      width: widget.isFullWidth ? double.infinity : null,
      height: _getButtonHeight(),
      decoration: BoxDecoration(
        border: Border.all(
          color: widget.customColor ?? AppColors.primary,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.isLoading ? null : widget.onPressed,
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getHorizontalPadding(),
              vertical: _getVerticalPadding(),
            ),
            child: _buildButtonContent(
              widget.customColor ?? AppColors.primary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextButton(ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.isLoading ? null : widget.onPressed,
        onTapDown: _onTapDown,
        onTapUp: _onTapUp,
        onTapCancel: _onTapCancel,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Container(
          width: widget.isFullWidth ? double.infinity : null,
          height: _getButtonHeight(),
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(),
            vertical: _getVerticalPadding(),
          ),
          child: _buildButtonContent(
            widget.customColor ?? AppColors.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildGradientButton(ThemeData theme) {
    return Container(
      width: widget.isFullWidth ? double.infinity : null,
      height: _getButtonHeight(),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.isLoading ? null : widget.onPressed,
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getHorizontalPadding(),
              vertical: _getVerticalPadding(),
            ),
            child: _buildButtonContent(AppColors.textOnPrimary),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonContent(Color textColor) {
    if (widget.child != null) {
      return Center(child: widget.child!);
    }

    if (widget.isLoading) {
      return Center(
        child: SizedBox(
          width: _getIconSize(),
          height: _getIconSize(),
          child: CircularProgressIndicator(
            color: textColor,
            strokeWidth: 2,
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            color: textColor,
            size: _getIconSize(),
          ),
          const SizedBox(width: AppDimensions.spacing8),
        ],
        Text(
          widget.text,
          style: _getTextStyle().copyWith(color: textColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return AppDimensions.buttonHeightS;
      case ModernButtonSize.medium:
        return AppDimensions.buttonHeightM;
      case ModernButtonSize.large:
        return AppDimensions.buttonHeightL;
    }
  }

  double _getHorizontalPadding() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return AppDimensions.paddingM;
      case ModernButtonSize.medium:
        return AppDimensions.paddingL;
      case ModernButtonSize.large:
        return AppDimensions.paddingXL;
    }
  }

  double _getVerticalPadding() {
    return AppDimensions.paddingS;
  }

  double _getIconSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return AppDimensions.iconS;
      case ModernButtonSize.medium:
        return AppDimensions.iconM;
      case ModernButtonSize.large:
        return AppDimensions.iconL;
    }
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return AppTextStyles.buttonSmall;
      case ModernButtonSize.medium:
        return AppTextStyles.buttonMedium;
      case ModernButtonSize.large:
        return AppTextStyles.buttonLarge;
    }
  }
}
