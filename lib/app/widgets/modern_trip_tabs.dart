import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';
import '../widgets/modern_card.dart';

class ModernTripTabs extends StatefulWidget {
  final TabController tabController;
  final Function(int) onTabChanged;
  final List<Map<String, dynamic>> activeTrips;
  final List<Map<String, dynamic>> scheduledTrips;
  final List<Map<String, dynamic>> pastTrips;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback onRefresh;

  const ModernTripTabs({
    Key? key,
    required this.tabController,
    required this.onTabChanged,
    required this.activeTrips,
    required this.scheduledTrips,
    required this.pastTrips,
    this.isLoading = false,
    this.errorMessage,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<ModernTripTabs> createState() => _ModernTripTabsState();
}

class _ModernTripTabsState extends State<ModernTripTabs>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Modern Tab Bar
        _buildModernTabBar(),
        
        // Tab Content
        Expanded(
          child: AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: _buildTabContent(),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildModernTabBar() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.marginM),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TabBar(
        controller: widget.tabController,
        onTap: (index) {
          widget.onTabChanged(index);
          _animationController.reset();
          _animationController.forward();
        },
        indicator: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        indicatorPadding: const EdgeInsets.all(AppDimensions.paddingXS),
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.white.withOpacity(0.8),
        labelStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.labelMedium,
        dividerColor: Colors.transparent,
        tabs: [
          _buildModernTab(
            'النشطة',
            widget.activeTrips.length,
            Icons.directions_car,
            AppColors.success,
          ),
          _buildModernTab(
            'المجدولة',
            widget.scheduledTrips.length,
            Icons.schedule,
            AppColors.warning,
          ),
          _buildModernTab(
            'السابقة',
            widget.pastTrips.length,
            Icons.history,
            AppColors.grey500,
          ),
        ],
      ),
    ).animate().slideY(begin: -0.5, end: 0, duration: 300.ms);
  }

  Widget _buildModernTab(String title, int count, IconData icon, Color color) {
    return Tab(
      height: 60,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: AppDimensions.iconS),
          const SizedBox(width: AppDimensions.spacing4),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(title),
              if (count > 0)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingXS,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: Text(
                    count.toString(),
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    if (widget.errorMessage != null) {
      return _buildErrorState();
    }

    return TabBarView(
      controller: widget.tabController,
      children: [
        _buildTripsList(widget.activeTrips, 0),
        _buildTripsList(widget.scheduledTrips, 1),
        _buildTripsList(widget.pastTrips, 2),
      ],
    );
  }

  Widget _buildTripsList(List<Map<String, dynamic>> trips, int tabIndex) {
    if (trips.isEmpty) {
      return _buildEmptyState(tabIndex);
    }

    return RefreshIndicator(
      onRefresh: () async => widget.onRefresh(),
      color: AppColors.primary,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        itemCount: trips.length,
        itemBuilder: (context, index) {
          return _buildModernTripCard(trips[index], tabIndex)
              .animate(delay: (index * 100).ms)
              .fadeIn(duration: 300.ms)
              .slideX(begin: 0.3, end: 0);
        },
      ),
    );
  }

  Widget _buildModernTripCard(Map<String, dynamic> trip, int tabIndex) {
    final status = trip['status'] ?? '';
    final isActive = tabIndex == 0;
    final isScheduled = tabIndex == 1;
    final isPast = tabIndex == 2;

    return ModernCard(
      type: ModernCardType.elevated,
      margin: const EdgeInsets.only(bottom: AppDimensions.marginM),
      isInteractive: true,
      onTap: () => _viewTripDetails(trip),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trip Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: _getStatusColor(status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  _getStatusIcon(status),
                  color: _getStatusColor(status),
                  size: AppDimensions.iconM,
                ),
              ),
              const SizedBox(width: AppDimensions.spacing12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      trip['directionTitle'] ?? 'رحلة',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppDimensions.spacing4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: Text(
                        _getStatusText(status),
                        style: AppTextStyles.labelSmall.copyWith(
                          color: _getStatusColor(status),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${trip['totalPrice']?.toStringAsFixed(2) ?? '0.00'} ريال',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.spacing16),
          
          // Trip Details
          _buildTripDetailRow(
            Icons.location_on_outlined,
            'من: ${trip['pickupAddress'] ?? 'غير محدد'}',
          ),
          const SizedBox(height: AppDimensions.spacing8),
          _buildTripDetailRow(
            Icons.location_on,
            'إلى: ${trip['directionTitle'] ?? 'غير محدد'}',
          ),
          const SizedBox(height: AppDimensions.spacing8),
          _buildTripDetailRow(
            Icons.access_time,
            'التاريخ: ${_formatDate(trip['createdAt'])}',
          ),
          
          // Action Buttons
          if (isActive || isScheduled) ...[
            const SizedBox(height: AppDimensions.spacing16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (isActive)
                  TextButton.icon(
                    onPressed: () => _navigateToChat(trip['id']),
                    icon: const Icon(Icons.chat_outlined, size: AppDimensions.iconS),
                    label: const Text('محادثة'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                    ),
                  ),
                const SizedBox(width: AppDimensions.spacing8),
                TextButton.icon(
                  onPressed: () => _cancelTrip(trip['id']),
                  icon: const Icon(Icons.cancel_outlined, size: AppDimensions.iconS),
                  label: const Text('إلغاء'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTripDetailRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: AppDimensions.iconS,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: AppDimensions.spacing8),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppDimensions.spacing16),
          Text('جاري التحميل...'),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Text(
            widget.errorMessage!,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          ElevatedButton.icon(
            onPressed: widget.onRefresh,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(int tabIndex) {
    String message;
    IconData icon;

    switch (tabIndex) {
      case 0:
        message = 'لا توجد رحلات نشطة';
        icon = Icons.directions_car_outlined;
        break;
      case 1:
        message = 'لا توجد رحلات مجدولة';
        icon = Icons.schedule_outlined;
        break;
      case 2:
        message = 'لا توجد رحلات سابقة';
        icon = Icons.history_outlined;
        break;
      default:
        message = 'لا توجد رحلات';
        icon = Icons.info_outline;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Text(
            message,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing24),
          if (tabIndex == 0 || tabIndex == 1)
            ElevatedButton.icon(
              onPressed: () => Get.back(),
              icon: const Icon(Icons.add),
              label: const Text('طلب رحلة جديدة'),
            ),
        ],
      ),
    );
  }

  // Utility methods
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'searching':
        return AppColors.warning;
      case 'assigned':
      case 'enroutetopickup':
        return AppColors.info;
      case 'arrivedatpickup':
      case 'ongoing':
        return AppColors.success;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
      case 'cancelledbydriver':
      case 'cancelledbyuser':
        return AppColors.error;
      default:
        return AppColors.grey500;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'searching':
        return Icons.search;
      case 'assigned':
        return Icons.person_pin_circle;
      case 'enroutetopickup':
        return Icons.directions_car;
      case 'arrivedatpickup':
        return Icons.location_on;
      case 'ongoing':
        return Icons.navigation;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
      case 'cancelledbydriver':
      case 'cancelledbyuser':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'searching':
        return 'البحث عن سائق';
      case 'assigned':
        return 'تم تعيين سائق';
      case 'enroutetopickup':
        return 'السائق في الطريق';
      case 'arrivedatpickup':
        return 'السائق وصل';
      case 'ongoing':
        return 'جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'cancelledbydriver':
        return 'ملغية من السائق';
      case 'cancelledbyuser':
        return 'ملغية من المستخدم';
      default:
        return 'غير معروف';
    }
  }

  String _formatDate(dynamic date) {
    if (date == null) return 'غير محدد';

    try {
      DateTime dateTime;
      if (date is DateTime) {
        dateTime = date;
      } else if (date is String) {
        dateTime = DateTime.parse(date);
      } else {
        return 'غير محدد';
      }

      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  // Navigation methods
  void _viewTripDetails(Map<String, dynamic> trip) {
    // Implementation for viewing trip details
    print('View trip details: ${trip['id']}');
  }

  void _navigateToChat(String tripId) {
    // Implementation for navigating to chat
    print('Navigate to chat: $tripId');
  }

  void _cancelTrip(String tripId) {
    // Implementation for canceling trip
    print('Cancel trip: $tripId');
  }
}
