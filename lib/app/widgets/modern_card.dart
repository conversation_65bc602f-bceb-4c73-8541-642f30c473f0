import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';

enum ModernCardType { elevated, glass, gradient, outline }

class ModernCard extends StatefulWidget {
  final Widget child;
  final ModernCardType type;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final bool isInteractive;
  final Color? customColor;
  final Gradient? customGradient;
  final double? borderRadius;
  final double? elevation;

  const ModernCard({
    Key? key,
    required this.child,
    this.type = ModernCardType.elevated,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.onTap,
    this.isInteractive = false,
    this.customColor,
    this.customGradient,
    this.borderRadius,
    this.elevation,
  }) : super(key: key);

  @override
  State<ModernCard> createState() => _ModernCardState();
}

class _ModernCardState extends State<ModernCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _elevationAnimation = Tween<double>(
      begin: widget.elevation ?? 2.0,
      end: (widget.elevation ?? 2.0) + 4.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() => _isHovered = isHovered);
    if (widget.isInteractive) {
      if (isHovered) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildCard(context),
        );
      },
    );
  }

  Widget _buildCard(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget cardContent = Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin ?? const EdgeInsets.all(AppDimensions.marginS),
      child: _buildCardByType(theme),
    );

    if (widget.onTap != null || widget.isInteractive) {
      return MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: GestureDetector(
          onTap: widget.onTap,
          child: cardContent,
        ),
      );
    }

    return cardContent;
  }

  Widget _buildCardByType(ThemeData theme) {
    switch (widget.type) {
      case ModernCardType.elevated:
        return _buildElevatedCard(theme);
      case ModernCardType.glass:
        return _buildGlassCard(theme);
      case ModernCardType.gradient:
        return _buildGradientCard(theme);
      case ModernCardType.outline:
        return _buildOutlineCard(theme);
    }
  }

  Widget _buildElevatedCard(ThemeData theme) {
    return Material(
      color: widget.customColor ?? theme.cardColor,
      elevation: _elevationAnimation.value,
      shadowColor: AppColors.shadowMedium,
      borderRadius: BorderRadius.circular(
        widget.borderRadius ?? AppDimensions.cardRadius,
      ),
      child: Container(
        padding: widget.padding ?? 
            const EdgeInsets.all(AppDimensions.cardPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppDimensions.cardRadius,
          ),
        ),
        child: widget.child,
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildGlassCard(ThemeData theme) {
    return GlassmorphicContainer(
      width: widget.width ?? double.infinity,
      // height: widget.height,
      borderRadius: widget.borderRadius ?? AppDimensions.cardRadius,
      blur: 20,
      alignment: Alignment.bottomCenter,
      border: 2,
      linearGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.glassBackground,
          AppColors.glassBackground.withOpacity(0.05),
        ],
      ),
      borderGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.glassBorder,
          AppColors.glassBorder.withOpacity(0.1),
        ],
      ),
      height: 100,
      child: Container(
        padding: widget.padding ?? 
            const EdgeInsets.all(AppDimensions.cardPadding),
        child: widget.child,
      ),
    ).animate().fadeIn(duration: 400.ms).scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildGradientCard(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: widget.customGradient ?? AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(
          widget.borderRadius ?? AppDimensions.cardRadius,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: _elevationAnimation.value * 2,
            offset: Offset(0, _elevationAnimation.value),
          ),
        ],
      ),
      child: Container(
        padding: widget.padding ?? 
            const EdgeInsets.all(AppDimensions.cardPadding),
        child: widget.child,
      ),
    ).animate().fadeIn(duration: 300.ms).shimmer(
      duration: 2000.ms,
      color: Colors.white.withOpacity(0.3),
    );
  }

  Widget _buildOutlineCard(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: widget.customColor ?? theme.cardColor,
        border: Border.all(
          color: AppColors.grey300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(
          widget.borderRadius ?? AppDimensions.cardRadius,
        ),
        boxShadow: _isHovered ? [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Container(
        padding: widget.padding ?? 
            const EdgeInsets.all(AppDimensions.cardPadding),
        child: widget.child,
      ),
    ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.1, end: 0);
  }
}

// Specialized card widgets for common use cases
class ModernTripCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final String price;
  final String status;
  final VoidCallback? onTap;
  final Widget? leading;
  final Widget? trailing;

  const ModernTripCard({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.price,
    required this.status,
    this.onTap,
    this.leading,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      type: ModernCardType.elevated,
      isInteractive: true,
      onTap: onTap,
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: AppDimensions.spacing12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppDimensions.spacing4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppDimensions.spacing8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      price,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                      child: Text(
                        status,
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: _getStatusColor(status),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: AppDimensions.spacing12),
            trailing!,
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppColors.pending;
      case 'confirmed':
        return AppColors.confirmed;
      case 'in_progress':
        return AppColors.inProgress;
      case 'delivered':
        return AppColors.delivered;
      case 'cancelled':
        return AppColors.cancelled;
      default:
        return AppColors.grey500;
    }
  }
}
