import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';

class ModernBottomSheet extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool showHandle;
  final bool isScrollable;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final bool showCloseButton;
  final VoidCallback? onClose;
  final Color? backgroundColor;
  final bool hasGradient;
  final Gradient? customGradient;

  const ModernBottomSheet({
    Key? key,
    required this.child,
    this.title,
    this.showHandle = true,
    this.isScrollable = true,
    this.height,
    this.padding,
    this.showCloseButton = false,
    this.onClose,
    this.backgroundColor,
    this.hasGradient = false,
    this.customGradient,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = height ?? screenHeight * 0.9;

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      decoration: BoxDecoration(
        color: hasGradient ? null : (backgroundColor ?? AppColors.surface),
        gradient: hasGradient ? (customGradient ?? AppColors.backgroundGradient) : null,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.bottomSheetRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          if (showHandle) _buildHandle(),
          
          // Header
          if (title != null || showCloseButton) _buildHeader(context),
          
          // Content
          if (isScrollable)
            Flexible(
              child: SingleChildScrollView(
                padding: padding ?? const EdgeInsets.all(AppDimensions.paddingL),
                child: child,
              ),
            )
          else
            Padding(
              padding: padding ?? const EdgeInsets.all(AppDimensions.paddingL),
              child: child,
            ),
        ],
      ),
    ).animate().slideY(begin: 1, end: 0, duration: 300.ms, curve: Curves.easeOutCubic);
  }

  Widget _buildHandle() {
    return Container(
      margin: const EdgeInsets.only(top: AppDimensions.paddingS),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppColors.grey300,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingL,
        vertical: AppDimensions.paddingM,
      ),
      child: Row(
        children: [
          if (title != null)
            Expanded(
              child: Text(
                title!,
                style: AppTextStyles.titleLarge,
              ),
            ),
          if (showCloseButton)
            IconButton(
              onPressed: onClose ?? () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
              style: IconButton.styleFrom(
                backgroundColor: AppColors.grey100,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Static methods for showing bottom sheets
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    bool showHandle = true,
    bool isScrollable = true,
    double? height,
    EdgeInsetsGeometry? padding,
    bool showCloseButton = false,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    bool hasGradient = false,
    Gradient? customGradient,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: Colors.transparent,
      builder: (context) => ModernBottomSheet(
        title: title,
        showHandle: showHandle,
        isScrollable: isScrollable,
        height: height,
        padding: padding,
        showCloseButton: showCloseButton,
        backgroundColor: backgroundColor,
        hasGradient: hasGradient,
        customGradient: customGradient,
        child: child,
      ),
    );
  }

  static Future<T?> showScrollable<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    bool showCloseButton = true,
    bool isDismissible = true,
  }) {
    return show<T>(
      context: context,
      child: child,
      title: title,
      showCloseButton: showCloseButton,
      isDismissible: isDismissible,
      isScrollable: true,
      height: MediaQuery.of(context).size.height * 0.9,
    );
  }

  static Future<T?> showFixed<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    double? height,
    bool showCloseButton = false,
    bool isDismissible = true,
  }) {
    return show<T>(
      context: context,
      child: child,
      title: title,
      height: height,
      showCloseButton: showCloseButton,
      isDismissible: isDismissible,
      isScrollable: false,
    );
  }
}

// Specialized bottom sheets for common use cases
class ModernActionBottomSheet extends StatelessWidget {
  final String title;
  final List<ModernActionItem> actions;
  final bool showCancel;
  final String? cancelText;

  const ModernActionBottomSheet({
    Key? key,
    required this.title,
    required this.actions,
    this.showCancel = true,
    this.cancelText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernBottomSheet(
      title: title,
      isScrollable: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ...actions.map((action) => _buildActionItem(context, action)),
          if (showCancel) ...[
            const SizedBox(height: AppDimensions.spacing8),
            _buildCancelButton(context),
          ],
        ],
      ),
    );
  }

  Widget _buildActionItem(BuildContext context, ModernActionItem action) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacing8),
      child: ListTile(
        leading: action.icon != null
            ? Icon(
                action.icon,
                color: action.isDestructive ? AppColors.error : AppColors.primary,
              )
            : null,
        title: Text(
          action.title,
          style: AppTextStyles.bodyLarge.copyWith(
            color: action.isDestructive ? AppColors.error : AppColors.textPrimary,
          ),
        ),
        subtitle: action.subtitle != null
            ? Text(
                action.subtitle!,
                style: AppTextStyles.bodySmall,
              )
            : null,
        onTap: () {
          Navigator.of(context).pop();
          action.onTap?.call();
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        tileColor: AppColors.grey50,
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: () => Navigator.of(context).pop(),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Text(
          cancelText ?? 'إلغاء',
          style: AppTextStyles.buttonMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ),
    );
  }

  static Future<void> show({
    required BuildContext context,
    required String title,
    required List<ModernActionItem> actions,
    bool showCancel = true,
    String? cancelText,
  }) {
    return ModernBottomSheet.show(
      context: context,
      child: ModernActionBottomSheet(
        title: title,
        actions: actions,
        showCancel: showCancel,
        cancelText: cancelText,
      ),
    );
  }
}

class ModernActionItem {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final VoidCallback? onTap;
  final bool isDestructive;

  const ModernActionItem({
    required this.title,
    this.subtitle,
    this.icon,
    this.onTap,
    this.isDestructive = false,
  });
}
