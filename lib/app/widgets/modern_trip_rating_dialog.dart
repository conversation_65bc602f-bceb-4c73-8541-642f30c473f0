import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'dart:async';

// Custom clipper for wave effect
class CurveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height * 0.8);
    
    final firstControlPoint = Offset(size.width * 0.25, size.height);
    final firstEndPoint = Offset(size.width * 0.5, size.height * 0.85);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );
    
    final secondControlPoint = Offset(size.width * 0.75, size.height * 0.7);
    final secondEndPoint = Offset(size.width, size.height * 0.85);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );
    
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

// Widget for text shine effect
class ShineEffect extends StatefulWidget {
  final Widget child;
  final Duration duration;
  
  const ShineEffect({
    Key? key,
    required this.child,
    this.duration = const Duration(seconds: 3),
  }) : super(key: key);

  @override
  State<ShineEffect> createState() => _ShineEffectState();
}

class _ShineEffectState extends State<ShineEffect> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(_controller);
    
    // Start the animation after a delay
    _timer = Timer(const Duration(milliseconds: 500), () {
      _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) {
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.transparent,
            Colors.white.withOpacity(0.9),
            Colors.transparent,
          ],
          stops: [
            0.0,
            0.5,
            1.0,
          ],
          transform: GradientRotation(_animation.value * 3.14),
        ).createShader(bounds);
      },
      blendMode: BlendMode.srcIn,
      child: widget.child,
    );
  }
}

class ModernTripRatingDialog extends StatefulWidget {
  final String name;
  final String? photoUrl;
  final Function(double, String?) onRatingSubmitted;
  final bool isDriver;

  const ModernTripRatingDialog({
    super.key,
    required this.name,
    this.photoUrl,
    required this.onRatingSubmitted,
    this.isDriver = false,
  });

  @override
  State<ModernTripRatingDialog> createState() => _ModernTripRatingDialogState();
}

class _ModernTripRatingDialogState extends State<ModernTripRatingDialog> {
  double rating = 5.0;
  final TextEditingController _commentController = TextEditingController();

  // Predefined rating comments for users rating drivers
  final List<String> userComments = [
    'سائق ممتاز ومحترف',
    'قيادة آمنة ومريحة',
    'وصلت في الوقت المحدد',
    'سيارة نظيفة ومرتبة',
    'سائق ودود ومهذب',
  ];

  // Predefined rating comments for drivers rating users
  final List<String> driverComments = [
    'راكب محترم ومهذب',
    'كان في الموقع المحدد',
    'تواصل جيد',
    'رحلة سهلة وممتعة',
    'راكب ملتزم بقواعد السلامة',
  ];

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
  
  // Get feedback text based on rating
  String _getRatingFeedback() {
    if (rating >= 5) {
      return 'ممتاز! 🌟';
    } else if (rating >= 4) {
      return 'جيد جداً 😊';
    } else if (rating >= 3) {
      return 'جيد 👍';
    } else if (rating >= 2) {
      return 'مقبول 😐';
    } else {
      return 'سيء 😞';
    }
  }
  
  // Get color based on rating
  Color _getRatingColor(ThemeData theme) {
    if (rating >= 4.5) {
      return Colors.green.shade700;
    } else if (rating >= 3.5) {
      return Colors.green.shade500;
    } else if (rating >= 2.5) {
      return Colors.amber.shade700;
    } else if (rating >= 1.5) {
      return Colors.orange.shade700;
    } else {
      return Colors.red.shade700;
    }
  }

  @override
  Widget build(BuildContext context) {
    final comments = widget.isDriver ? driverComments : userComments;
    final ratingTarget = widget.isDriver ? 'الراكب' : 'السائق';
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.zero,
        alignment: Alignment.bottomCenter,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with app bar style
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Column(
                    children: [
                      // Handle bar for dragging
                      Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      // Title and subtitle
                      Row(
                        children: [
                          // Avatar
                          CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.white,
                            backgroundImage: widget.photoUrl != null && widget.photoUrl!.isNotEmpty
                                ? NetworkImage(widget.photoUrl!)
                                : null,
                            child: widget.photoUrl == null || widget.photoUrl!.isEmpty
                                ? Icon(
                                    widget.isDriver ? Icons.person : Icons.drive_eta,
                                    size: 20,
                                    color: theme.colorScheme.primary,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 12),
                          // Name and rating title
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'قيّم تجربتك',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white.withOpacity(0.8),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Close button
                          IconButton(
                            onPressed: () => Get.back(),
                            icon: const Icon(Icons.close, color: Colors.white),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                const SizedBox(height: 16),
                
                // Rating stars - simple and clean
                Column(
                  children: [
                    RatingBar.builder(
                      initialRating: rating,
                      minRating: 1,
                      direction: Axis.horizontal,
                      allowHalfRating: true,
                      itemCount: 5,
                      itemSize: 32,
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => Icon(
                        Icons.star,
                        color: Colors.amber,
                      ),
                      onRatingUpdate: (newRating) {
                        setState(() {
                          rating = newRating;
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    // Rating feedback text - simple
                    Text(
                      _getRatingFeedback(),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Comment field - clean and simple
                TextField(
                  controller: _commentController,
                  decoration: InputDecoration(
                    hintText: 'أضف تعليقًا (اختياري)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: theme.colorScheme.primary),
                    ),
                    contentPadding: const EdgeInsets.all(12),
                    prefixIcon: Icon(
                      Icons.comment_outlined,
                      color: Colors.grey[500],
                      size: 20,
                    ),
                  ),
                  maxLines: 2,
                ),
                
                const SizedBox(height: 20),
                
                // Quick comment chips title
                Padding(
                  padding: const EdgeInsets.only(bottom: 8, top: 8),
                  child: Text(
                    'اختر تعليقًا سريعًا',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                
                // Quick comment chips - clean and simple
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  alignment: WrapAlignment.center,
                  children: comments.map((comment) {
                    return InkWell(
                      onTap: () {
                        setState(() {
                          _commentController.text = comment;
                        });
                      },
                      child: Chip(
                        label: Text(comment),
                        backgroundColor: _commentController.text == comment
                            ? theme.colorScheme.primary.withOpacity(0.1)
                            : Colors.grey[100],
                        side: BorderSide(
                          color: _commentController.text == comment
                              ? theme.colorScheme.primary
                              : Colors.grey[300]!,
                          width: 1,
                        ),
                        labelStyle: TextStyle(
                          color: _commentController.text == comment
                              ? theme.colorScheme.primary
                              : Colors.grey[800],
                          fontSize: 12,
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                        elevation: 0,
                      ),
                    );
                  }).toList(),
                ),
                
                const SizedBox(height: 24),
                
                // Action buttons - clean and simple
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Skip button
                    Expanded(
                      child: TextButton(
                        onPressed: () => Get.back(),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('تخطي'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Submit button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          final comment = _commentController.text.trim().isNotEmpty
                              ? _commentController.text.trim()
                              : null;
                          widget.onRatingSubmitted(rating, comment);
                          Get.back();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                        ),
                        child: const Text('إرسال'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ).animate().fadeIn(duration: const Duration(milliseconds: 300)).scale(begin: const Offset(0.8, 0.8) , end: const Offset(1.0, 1.0), duration: const Duration(milliseconds: 300)),
    );
  }
}
