import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';
import '../modules/home/<USER>/home_controller.dart';
import '../routes/app_pages.dart';

class ModernDrawer extends GetView<HomeController> {
  const ModernDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: SafeArea(
        child: Container(
          width: AppDimensions.drawerWidth,
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
          child: Column(
            children: [
              // Modern Header with Glass Effect
              _buildModernHeader(context),
              
              // Main Content with Glass Background
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppDimensions.marginS),
                  child: GlassmorphicContainer(
                    width: double.infinity,
                    height: double.infinity,
                    borderRadius: AppDimensions.radiusL,
                    blur: 20,
                    alignment: Alignment.bottomCenter,
                    border: 2,
                    linearGradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.glassBackground,
                        AppColors.glassBackground.withOpacity(0.05),
                      ],
                    ),
                    borderGradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.glassBorder,
                        AppColors.glassBorder.withOpacity(0.1),
                      ],
                    ),
                    child: ListView(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      children: [
                        // Quick Stats Cards
                        _buildQuickStatsRow(context),
                        const SizedBox(height: AppDimensions.spacing20),
                        
                        // Profile Section
                        _buildModernSection(
                          context,
                          'الحساب الشخصي',
                          Icons.person_outline,
                          [
                            _buildModernMenuItem(
                              context,
                              icon: Icons.admin_panel_settings_outlined,
                              title: 'لوحة الإدارة',
                              subtitle: 'إدارة النظام',
                              onTap: () => _navigateTo(Routes.ADMIN_DASHBOARD),
                              color: AppColors.warning,
                            ),
                            _buildModernMenuItem(
                              context,
                              icon: Icons.person_outline,
                              title: 'الملف الشخصي',
                              subtitle: 'عرض وتعديل البيانات',
                              onTap: () => _navigateTo(Routes.PROFILE),
                            ),
                            _buildModernMenuItem(
                              context,
                              icon: Icons.directions_car_outlined,
                              title: 'رحلاتي',
                              subtitle: 'عرض جميع الرحلات',
                              onTap: () => _navigateTo(Routes.USER_TRIPS),
                              badge: '3',
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppDimensions.spacing16),
                        
                        // Wallet Section
                        _buildModernSection(
                          context,
                          'المحفظة المالية',
                          Icons.account_balance_wallet_outlined,
                          [
                            _buildWalletCard(context),
                            _buildModernMenuItem(
                              context,
                              icon: Icons.add_card_outlined,
                              title: 'إضافة رصيد',
                              subtitle: 'شحن المحفظة',
                              onTap: () => _navigateTo(Routes.ADD_FUNDS),
                              color: AppColors.success,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppDimensions.spacing16),
                        
                        // Support Section
                        _buildModernSection(
                          context,
                          'الدعم والمساعدة',
                          Icons.support_agent_outlined,
                          [
                            _buildModernMenuItem(
                              context,
                              icon: Icons.chat_bubble_outline,
                              title: 'التحدث مع الدعم',
                              subtitle: 'دردشة مباشرة',
                              onTap: () => _navigateTo(Routes.SUPPORT_CHAT),
                              badge: '1',
                              color: AppColors.info,
                            ),
                            _buildModernMenuItem(
                              context,
                              icon: Icons.help_outline,
                              title: 'مركز المساعدة',
                              subtitle: 'الأسئلة الشائعة',
                              onTap: () => _navigateTo(Routes.HELP_CENTER),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppDimensions.spacing16),
                        
                        // Settings Section
                        _buildModernSection(
                          context,
                          'الإعدادات',
                          Icons.settings_outlined,
                          [
                            _buildModernMenuItem(
                              context,
                              icon: Icons.language_outlined,
                              title: 'اللغة',
                              subtitle: 'العربية',
                              onTap: () => _showLanguageSelector(context),
                            ),
                            _buildModernMenuItem(
                              context,
                              icon: Icons.logout_outlined,
                              title: 'تسجيل الخروج',
                              subtitle: 'إنهاء الجلسة',
                              onTap: () => _logout(),
                              color: AppColors.error,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppDimensions.spacing32),
                      ],
                    ),
                  ),
                ),
              ),
              
              // Modern Footer
              _buildModernFooter(context),
            ],
          ),
        ),
      ),
    ).animate().slideX(begin: -1, end: 0, duration: 300.ms, curve: Curves.easeOutCubic);
  }

  Widget _buildModernHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
          children: [
            // Profile Avatar with Glow Effect
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.white.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 40,
                backgroundColor: AppColors.white.withOpacity(0.2),
                child: Icon(
                  Icons.person,
                  size: 40,
                  color: AppColors.white,
                ),
              ),
            ),
            const SizedBox(height: AppDimensions.spacing12),
            
            // User Name
            Text(
              'مستخدم',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.spacing4),

            // User Email/Phone
            Text(
              '<EMAIL>',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: -0.3, end: 0);
  }

  Widget _buildQuickStatsRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            icon: Icons.directions_car,
            value: '12',
            label: 'رحلة',
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: AppDimensions.spacing8),
        Expanded(
          child: _buildStatCard(
            context,
            icon: Icons.account_balance_wallet,
            value: '250',
            label: 'ريال',
            color: AppColors.success,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppDimensions.iconL),
          const SizedBox(height: AppDimensions.spacing4),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.white.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppColors.white.withOpacity(0.8),
              size: AppDimensions.iconM,
            ),
            const SizedBox(width: AppDimensions.spacing8),
            Text(
              title,
              style: AppTextStyles.titleSmall.copyWith(
                color: AppColors.white.withOpacity(0.9),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacing12),
        ...children,
      ],
    );
  }

  Widget _buildModernMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? color,
    String? badge,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacing8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppDimensions.paddingS),
                  decoration: BoxDecoration(
                    color: (color ?? AppColors.primary).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: color ?? AppColors.white,
                    size: AppDimensions.iconM,
                  ),
                ),
                const SizedBox(width: AppDimensions.spacing12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.white.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                if (badge != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingS,
                      vertical: AppDimensions.paddingXS,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    ),
                    child: Text(
                      badge,
                      style: AppTextStyles.labelSmall.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const SizedBox(width: AppDimensions.spacing8),
                Icon(
                  Icons.chevron_left,
                  color: AppColors.white.withOpacity(0.5),
                  size: AppDimensions.iconS,
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.3, end: 0);
  }

  Widget _buildWalletCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacing8),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        gradient: AppColors.secondaryGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: AppColors.white,
                size: AppDimensions.iconM,
              ),
              const SizedBox(width: AppDimensions.spacing8),
              Text(
                'رصيد المحفظة',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            '250.00 ريال',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.white.withOpacity(0.7),
                  size: AppDimensions.iconS,
                ),
                const SizedBox(width: AppDimensions.spacing8),
                Text(
                  'الإصدار 1.0.0',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.white.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Navigation and utility methods
  void _navigateTo(String route) {
    Get.back();
    // Get.toNamed(route); // Commented out until routes are properly defined
  }

  void _showLanguageSelector(BuildContext context) {
    // Implementation for language selector
    Get.back();
    // Show language selection dialog
  }

  void _logout() {
    Get.back();
    // controller.logout(); // Will be implemented when controller is properly connected
  }
}
