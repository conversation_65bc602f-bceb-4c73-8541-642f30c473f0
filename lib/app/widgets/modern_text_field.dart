import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';

enum ModernTextFieldType { filled, outlined, underlined, glass }

class ModernTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final TextInputType keyboardType;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final void Function(String)? onSubmitted;
  final ModernTextFieldType type;
  final bool isFloatingLabel;
  final Color? customColor;
  final double? borderRadius;

  const ModernTextField({
    Key? key,
    this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.keyboardType = TextInputType.text,
    this.textInputAction,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.type = ModernTextFieldType.filled,
    this.isFloatingLabel = true,
    this.customColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  State<ModernTextField> createState() => _ModernTextFieldState();
}

class _ModernTextFieldState extends State<ModernTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _focusAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _focusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
      if (_isFocused) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });

    _hasError = widget.errorText != null;
  }

  @override
  void didUpdateWidget(ModernTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.errorText != oldWidget.errorText) {
      setState(() {
        _hasError = widget.errorText != null;
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedBuilder(
          animation: _focusAnimation,
          builder: (context, child) {
            return _buildTextField(context);
          },
        ),
        if (widget.helperText != null || widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(
              top: AppDimensions.spacing4,
              left: AppDimensions.spacing12,
            ),
            child: Text(
              widget.errorText ?? widget.helperText!,
              style: AppTextStyles.caption.copyWith(
                color: _hasError ? AppColors.error : AppColors.textSecondary,
              ),
            ),
          ),
      ],
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildTextField(BuildContext context) {
    switch (widget.type) {
      case ModernTextFieldType.filled:
        return _buildFilledTextField(context);
      case ModernTextFieldType.outlined:
        return _buildOutlinedTextField(context);
      case ModernTextFieldType.underlined:
        return _buildUnderlinedTextField(context);
      case ModernTextFieldType.glass:
        return _buildGlassTextField(context);
    }
  }

  Widget _buildFilledTextField(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: _isFocused 
            ? AppColors.grey50 
            : AppColors.grey100,
        borderRadius: BorderRadius.circular(
          widget.borderRadius ?? AppDimensions.radiusM,
        ),
        border: Border.all(
          color: _getBorderColor(),
          width: _isFocused ? 2 : 1,
        ),
        boxShadow: _isFocused ? [
          BoxShadow(
            color: (widget.customColor ?? AppColors.primary).withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        obscureText: widget.obscureText,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        maxLines: widget.maxLines,
        maxLength: widget.maxLength,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        inputFormatters: widget.inputFormatters,
        validator: widget.validator,
        onChanged: widget.onChanged,
        onTap: widget.onTap,
        onFieldSubmitted: widget.onSubmitted,
        style: AppTextStyles.bodyMedium,
        decoration: InputDecoration(
          labelText: widget.isFloatingLabel ? widget.labelText : null,
          hintText: widget.hintText,
          prefixIcon: widget.prefixIcon != null 
              ? Icon(
                  widget.prefixIcon,
                  color: _getIconColor(),
                  size: AppDimensions.iconM,
                )
              : null,
          suffixIcon: widget.suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingM,
          ),
          labelStyle: AppTextStyles.labelMedium.copyWith(
            color: _getLabelColor(),
          ),
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textTertiary,
          ),
          floatingLabelStyle: AppTextStyles.labelMedium.copyWith(
            color: widget.customColor ?? AppColors.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildOutlinedTextField(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      obscureText: widget.obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      inputFormatters: widget.inputFormatters,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      onFieldSubmitted: widget.onSubmitted,
      style: AppTextStyles.bodyMedium,
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        prefixIcon: widget.prefixIcon != null 
            ? Icon(
                widget.prefixIcon,
                color: _getIconColor(),
                size: AppDimensions.iconM,
              )
            : null,
        suffixIcon: widget.suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppDimensions.radiusM,
          ),
          borderSide: BorderSide(
            color: AppColors.grey300,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppDimensions.radiusM,
          ),
          borderSide: BorderSide(
            color: AppColors.grey300,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppDimensions.radiusM,
          ),
          borderSide: BorderSide(
            color: widget.customColor ?? AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppDimensions.radiusM,
          ),
          borderSide: const BorderSide(
            color: AppColors.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? AppDimensions.radiusM,
          ),
          borderSide: const BorderSide(
            color: AppColors.error,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingM,
        ),
        labelStyle: AppTextStyles.labelMedium.copyWith(
          color: _getLabelColor(),
        ),
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textTertiary,
        ),
        floatingLabelStyle: AppTextStyles.labelMedium.copyWith(
          color: widget.customColor ?? AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildUnderlinedTextField(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      obscureText: widget.obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      inputFormatters: widget.inputFormatters,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      onFieldSubmitted: widget.onSubmitted,
      style: AppTextStyles.bodyMedium,
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        prefixIcon: widget.prefixIcon != null 
            ? Icon(
                widget.prefixIcon,
                color: _getIconColor(),
                size: AppDimensions.iconM,
              )
            : null,
        suffixIcon: widget.suffixIcon,
        border: UnderlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.grey300,
            width: 1,
          ),
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.grey300,
            width: 1,
          ),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: widget.customColor ?? AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.error,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: AppDimensions.paddingM,
        ),
        labelStyle: AppTextStyles.labelMedium.copyWith(
          color: _getLabelColor(),
        ),
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textTertiary,
        ),
        floatingLabelStyle: AppTextStyles.labelMedium.copyWith(
          color: widget.customColor ?? AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildGlassTextField(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.glassBackground,
        borderRadius: BorderRadius.circular(
          widget.borderRadius ?? AppDimensions.radiusM,
        ),
        border: Border.all(
          color: AppColors.glassBorder,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        obscureText: widget.obscureText,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        maxLines: widget.maxLines,
        maxLength: widget.maxLength,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        inputFormatters: widget.inputFormatters,
        validator: widget.validator,
        onChanged: widget.onChanged,
        onTap: widget.onTap,
        onFieldSubmitted: widget.onSubmitted,
        style: AppTextStyles.bodyMedium.copyWith(color: AppColors.white),
        decoration: InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          prefixIcon: widget.prefixIcon != null 
              ? Icon(
                  widget.prefixIcon,
                  color: AppColors.white.withOpacity(0.7),
                  size: AppDimensions.iconM,
                )
              : null,
          suffixIcon: widget.suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingM,
          ),
          labelStyle: AppTextStyles.labelMedium.copyWith(
            color: AppColors.white.withOpacity(0.7),
          ),
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.white.withOpacity(0.5),
          ),
          floatingLabelStyle: AppTextStyles.labelMedium.copyWith(
            color: AppColors.white,
          ),
        ),
      ),
    );
  }

  Color _getBorderColor() {
    if (_hasError) return AppColors.error;
    if (_isFocused) return widget.customColor ?? AppColors.primary;
    return AppColors.grey300;
  }

  Color _getIconColor() {
    if (_hasError) return AppColors.error;
    if (_isFocused) return widget.customColor ?? AppColors.primary;
    return AppColors.textSecondary;
  }

  Color _getLabelColor() {
    if (_hasError) return AppColors.error;
    if (_isFocused) return widget.customColor ?? AppColors.primary;
    return AppColors.textSecondary;
  }
}
