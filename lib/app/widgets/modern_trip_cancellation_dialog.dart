import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_animate/flutter_animate.dart';

class ModernTripCancellationDialog extends StatefulWidget {
  final Function(String) onCancellationConfirmed;
  final bool isDriver;

  const ModernTripCancellationDialog({
    super.key,
    required this.onCancellationConfirmed,
    this.isDriver = false,
  });

  @override
  State<ModernTripCancellationDialog> createState() => _ModernTripCancellationDialogState();
}

class _ModernTripCancellationDialogState extends State<ModernTripCancellationDialog> {
  String? selectedReason;
  String? otherReason;
  final TextEditingController _otherReasonController = TextEditingController();

  // Predefined cancellation reasons for users
  final List<String> userReasons = [
    'تغيير الخطط',
    'وقت انتظار طويل',
    'حجزت عن طريق الخطأ',
    'وجدت وسيلة نقل أخرى',
    'السعر مرتفع',
    'سبب آخر',
  ];

  // Predefined cancellation reasons for drivers
  final List<String> driverReasons = [
    'مشكلة في السيارة',
    'حركة مرور كثيفة',
    'موقع الراكب بعيد جداً',
    'ظروف طارئة',
    'الراكب لا يستجيب',
    'سبب آخر',
  ];

  @override
  void dispose() {
    _otherReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final reasons = widget.isDriver ? driverReasons : userReasons;
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            // Main content container
            Container(
              padding: const EdgeInsets.only(top: 60, left: 20, right: 20, bottom: 20),
              margin: const EdgeInsets.only(top: 40),
              decoration: BoxDecoration(
                shape: BoxShape.rectangle,
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  const Text(
                    'لماذا تريد إلغاء الرحلة؟',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                  // Subtitle
                  Text(
                    'يرجى اختيار سبب الإلغاء:',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Reasons list
                  Container(
                    height: 250,
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: reasons.length,
                      itemBuilder: (context, index) {
                        final reason = reasons[index];
                        final isSelected = selectedReason == reason;
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: Material(
                            color: isSelected ? theme.colorScheme.primary.withOpacity(0.1) : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: () {
                                setState(() {
                                  selectedReason = reason;
                                  if (reason != 'سبب آخر') {
                                    otherReason = null;
                                    _otherReasonController.clear();
                                  }
                                });
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 24,
                                      height: 24,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: isSelected ? theme.colorScheme.primary : Colors.white,
                                        border: Border.all(
                                          color: isSelected ? theme.colorScheme.primary : Colors.grey,
                                          width: 2,
                                        ),
                                      ),
                                      child: isSelected
                                          ? const Icon(
                                              Icons.check,
                                              size: 16,
                                              color: Colors.white,
                                            )
                                          : null,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        reason,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                          color: isSelected ? theme.colorScheme.primary : Colors.black87,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  // Other reason text field
                  if (selectedReason == 'سبب آخر') ...[                
                    const SizedBox(height: 16),
                    TextField(
                      controller: _otherReasonController,
                      decoration: InputDecoration(
                        hintText: 'يرجى ذكر السبب',
                        filled: true,
                        fillColor: Colors.grey[50],
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: theme.colorScheme.primary, width: 1),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      maxLines: 3,
                      onChanged: (value) {
                        setState(() {
                          otherReason = value;
                        });
                      },
                    ),
                  ],
                  const SizedBox(height: 24),
                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Get.back(),
                          style: TextButton.styleFrom(
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: selectedReason == null
                              ? null
                              : () {
                                  final finalReason = selectedReason == 'سبب آخر' && otherReason != null && otherReason!.isNotEmpty
                                      ? otherReason!
                                      : selectedReason!;
                                  widget.onCancellationConfirmed(finalReason);
                                  Get.back();
                                },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            elevation: 0,
                          ),
                          child: const Text(
                            'تأكيد الإلغاء',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Top icon
            Positioned(
              top: -40,
              child: CircleAvatar(
                backgroundColor: Colors.red,
                radius: 40,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.cancel_outlined,
                    color: Colors.white,
                    size: 46,
                  ),
                ),
              ),
            ),
          ],
        ),
      ).animate().fadeIn(duration: const Duration(milliseconds: 300)).scale(begin: Offset(0.5, 0.5),end: Offset(1, 1), duration: const Duration(milliseconds: 300)),
    );
  }
}
