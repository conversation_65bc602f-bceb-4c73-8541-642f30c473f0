import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';
import 'modern_button.dart';
import 'modern_card.dart';

class ModernHomeHeader extends StatelessWidget {
  final String userName;
  final String greeting;
  final VoidCallback? onProfileTap;
  final VoidCallback? onNotificationTap;
  final int notificationCount;

  const ModernHomeHeader({
    Key? key,
    required this.userName,
    required this.greeting,
    this.onProfileTap,
    this.onNotificationTap,
    this.notificationCount = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradient,
      ),
      child: Safe<PERSON>rea(
        bottom: false,
        child: Row(
          children: [
            // Profile Avatar
            GestureDetector(
              onTap: onProfileTap,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.white.withOpacity(0.3),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 25,
                  backgroundColor: AppColors.white.withOpacity(0.2),
                  child: Icon(
                    Icons.person,
                    color: AppColors.white,
                    size: AppDimensions.iconL,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.spacing16),
            
            // Greeting and Name
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    greeting,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.white.withOpacity(0.9),
                    ),
                  ),
                  Text(
                    userName,
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Notification Button
            GestureDetector(
              onTap: onNotificationTap,
              child: Container(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Stack(
                  children: [
                    Icon(
                      Icons.notifications_outlined,
                      color: AppColors.white,
                      size: AppDimensions.iconL,
                    ),
                    if (notificationCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: AppColors.error,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            notificationCount > 9 ? '9+' : notificationCount.toString(),
                            style: AppTextStyles.labelSmall.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: -0.3, end: 0);
  }
}

class ModernQuickActions extends StatelessWidget {
  final VoidCallback? onRequestRide;
  final VoidCallback? onScheduleRide;
  final VoidCallback? onViewTrips;
  final VoidCallback? onWallet;

  const ModernQuickActions({
    Key? key,
    this.onRequestRide,
    this.onScheduleRide,
    this.onViewTrips,
    this.onWallet,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Row(
        children: [
          Expanded(
            child: _buildQuickActionCard(
              context,
              icon: Icons.directions_car,
              title: 'طلب رحلة',
              subtitle: 'الآن',
              color: AppColors.primary,
              onTap: onRequestRide,
            ),
          ),
          const SizedBox(width: AppDimensions.spacing12),
          Expanded(
            child: _buildQuickActionCard(
              context,
              icon: Icons.schedule,
              title: 'جدولة رحلة',
              subtitle: 'لاحقاً',
              color: AppColors.secondary,
              onTap: onScheduleRide,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      type: ModernCardType.gradient,
      customGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [color, color.withOpacity(0.8)],
      ),
      isInteractive: true,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              icon,
              color: AppColors.white,
              size: AppDimensions.iconXL,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing12),
          Text(
            title,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            subtitle,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
}

class ModernSearchBar extends StatefulWidget {
  final String hintText;
  final Function(String)? onChanged;
  final VoidCallback? onCurrentLocation;
  final bool isLoading;

  const ModernSearchBar({
    Key? key,
    required this.hintText,
    this.onChanged,
    this.onCurrentLocation,
    this.isLoading = false,
  }) : super(key: key);

  @override
  State<ModernSearchBar> createState() => _ModernSearchBarState();
}

class _ModernSearchBarState extends State<ModernSearchBar> {
  final TextEditingController _controller = TextEditingController();
  bool _isFocused = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Column(
        children: [
          // Search Input
          GlassmorphicContainer(
            width: double.infinity,
            height: 60,
            borderRadius: AppDimensions.radiusL,
            blur: 20,
            alignment: Alignment.bottomCenter,
            border: 2,
            linearGradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.glassBackground,
                AppColors.glassBackground.withOpacity(0.05),
              ],
            ),
            borderGradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.glassBorder,
                AppColors.glassBorder.withOpacity(0.1),
              ],
            ),
            child: TextField(
              controller: _controller,
              onChanged: widget.onChanged,
              onTap: () => setState(() => _isFocused = true),
              onSubmitted: (_) => setState(() => _isFocused = false),
              style: AppTextStyles.bodyMedium.copyWith(color: AppColors.white),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white.withOpacity(0.7),
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.white.withOpacity(0.8),
                ),
                suffixIcon: widget.isLoading
                    ? Container(
                        padding: const EdgeInsets.all(AppDimensions.paddingS),
                        child: CircularProgressIndicator(
                          color: AppColors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingM,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: AppDimensions.spacing12),
          
          // Current Location Button
          ModernButton(
            text: 'استخدام الموقع الحالي',
            onPressed: widget.onCurrentLocation,
            type: ModernButtonType.outline,
            size: ModernButtonSize.medium,
            icon: Icons.my_location,
            customColor: AppColors.white,
            isFullWidth: true,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 700.ms, delay: 400.ms).slideY(begin: 0.3, end: 0);
  }
}

class ModernStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String? subtitle;
  final VoidCallback? onTap;

  const ModernStatsCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.subtitle,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      type: ModernCardType.glass,
      isInteractive: onTap != null,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: AppDimensions.iconM,
                ),
              ),
              const Spacer(),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondary,
                  size: AppDimensions.iconS,
                ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing12),
          Text(
            value,
            style: AppTextStyles.headlineSmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: AppDimensions.spacing4),
            Text(
              subtitle!,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
