import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TripRatingDialog extends StatefulWidget {
  final String name;
  final String? photoUrl;
  final Function(double, String?) onRatingSubmitted;
  final bool isDriver;

  const TripRatingDialog({
    Key? key,
    required this.name,
    this.photoUrl,
    required this.onRatingSubmitted,
    this.isDriver = false,
  }) : super(key: key);

  @override
  State<TripRatingDialog> createState() => _TripRatingDialogState();
}

class _TripRatingDialogState extends State<TripRatingDialog> {
  double rating = 5.0;
  final TextEditingController _commentController = TextEditingController();

  // Predefined rating comments for users rating drivers
  final List<String> userComments = [
    'سائق ممتاز ومحترف',
    'قيادة آمنة ومريحة',
    'وصلت في الوقت المحدد',
    'سيارة نظيفة ومرتبة',
    'سائق ودود ومهذب',
  ];

  // Predefined rating comments for drivers rating users
  final List<String> driverComments = [
    'راكب محترم ومهذب',
    'كان في الموقع المحدد',
    'تواصل جيد',
    'رحلة سهلة وممتعة',
    'راكب ملتزم بقواعد السلامة',
  ];

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final comments = widget.isDriver ? driverComments : userComments;
    final ratingTarget = widget.isDriver ? 'الراكب' : 'السائق';
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        title: Text(
          'كيف كانت رحلتك؟',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        content: Container(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                radius: 40,
                backgroundImage: widget.photoUrl != null && widget.photoUrl!.isNotEmpty
                    ? NetworkImage(widget.photoUrl!) as ImageProvider
                    : AssetImage('assets/images/default_avatar.png'),
                backgroundColor: Colors.grey[200],
              ),
              SizedBox(height: 8),
              Text(
                widget.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'قيّم $ratingTarget',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    icon: Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      color: index < rating ? Colors.amber : Colors.grey,
                      size: 32,
                    ),
                    onPressed: () {
                      setState(() {
                        rating = index + 1;
                      });
                    },
                  );
                }),
              ),
              SizedBox(height: 16),
              Text(
                'أضف تعليقاً (اختياري)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 8),
              Container(
                height: 100,
                child: TextField(
                  controller: _commentController,
                  decoration: InputDecoration(
                    hintText: 'اكتب تعليقك هنا...',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.all(12),
                  ),
                  maxLines: 3,
                ),
              ),
              SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: comments.map((comment) {
                  return InkWell(
                    onTap: () {
                      setState(() {
                        _commentController.text = comment;
                      });
                    },
                    child: Chip(
                      label: Text(comment),
                      backgroundColor: Colors.grey[200],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('تخطي'),
          ),
          ElevatedButton(
            onPressed: () {
              final comment = _commentController.text.trim().isNotEmpty
                  ? _commentController.text.trim()
                  : null;
              widget.onRatingSubmitted(rating, comment);
              Get.back();
            },
            child: Text('إرسال التقييم'),
          ),
        ],
      ),
    );
  }
}
