import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

import '../data/models/notification_model.dart';

class NotificationService extends GetxService {
  // Dependencies
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // State
  final RxList<AppNotification> notifications = <AppNotification>[].obs;
  final RxInt unreadCount = 0.obs;

  // Listeners
  StreamSubscription? _notificationsSubscription;

  // Initialize the service
  Future<NotificationService> init() async {
    // Initialize local notifications
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      requestAlertPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Start listening for notifications when a user is logged in
    _auth.authStateChanges().listen((User? user) {
      if (user != null) {
        _startListeningToNotifications(user.uid);
        _startListeningToScheduledTrips(user.uid);
      } else {
        _stopListeningToNotifications();
        notifications.clear();
        unreadCount.value = 0;
      }
    });

    return this;
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Parse the payload (tripId) and navigate to the appropriate screen
    if (response.payload != null && response.payload!.isNotEmpty) {
      // Uncomment when trip details route is implemented
      // final tripId = response.payload;
      // Get.toNamed(Routes.TRIP_DETAILS, arguments: tripId);

      // For now, just print the payload
      debugPrint('Notification tapped with payload: ${response.payload}');
    }
  }

  // Start listening to notifications
  void _startListeningToNotifications(String userId) {
    _stopListeningToNotifications();

    _notificationsSubscription = _firestore
        .collection('notifications')
        .where('recipientId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .limit(50) // Limit to recent notifications
        .snapshots()
        .listen((snapshot) {
      final List<AppNotification> newNotifications = snapshot.docs
          .map((doc) => AppNotification.fromFirestore(doc))
          .toList();

      notifications.value = newNotifications;
      _updateUnreadCount();

      // Show local notification for new unread notifications
      _showNewNotifications(newNotifications);
    });
  }

  // Stop listening to notifications
  void _stopListeningToNotifications() {
    if (_notificationsSubscription != null) {
      _notificationsSubscription?.cancel();
      _notificationsSubscription = null;
    }
  }

  // Update unread count
  void _updateUnreadCount() {
    unreadCount.value = notifications.where((n) => !n.isRead).length;
  }

  // Show local notifications for new unread notifications
  void _showNewNotifications(List<AppNotification> newNotifications) {
    // Get only the new unread notifications from the last minute
    final now = Timestamp.now();
    final oneMinuteAgo = Timestamp.fromDate(
      DateTime.now().subtract(const Duration(minutes: 1)),
    );

    final recentUnreadNotifications = newNotifications
        .where((n) =>
            !n.isRead &&
            n.timestamp.compareTo(oneMinuteAgo) >= 0 &&
            n.timestamp.compareTo(now) <= 0)
        .toList();

    // Show local notification for each recent unread notification
    for (final notification in recentUnreadNotifications) {
      _showLocalNotification(notification);
    }
  }

  // Show a local notification
  Future<void> _showLocalNotification(AppNotification notification) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'trip_channel',
      'Trip Notifications',
      channelDescription: 'Notifications related to your trips',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      platformChannelSpecifics,
      payload: notification.tripId,
    );
  }

  // Mark a notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection('notifications')
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final batch = _firestore.batch();

      final unreadNotifications =
          notifications.where((n) => !n.isRead).toList();

      for (final notification in unreadNotifications) {
        batch.update(
          _firestore.collection('notifications').doc(notification.id),
          {'isRead': true},
        );
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // Create a new notification
  Future<void> createNotification({
    required String recipientId,
    String? tripId,
    required String title,
    required String body,
    required NotificationType type,
    String? sound,
    String? priority,
  }) async {
    try {
      final notification = AppNotification(
        id: '',
        recipientId: recipientId,
        tripId: tripId,
        title: title,
        body: body,
        type: type,
        timestamp: Timestamp.now(),
        isRead: false,
        sound: sound ?? 'default',
        priority: priority ?? 'high',
      );

      await _firestore
          .collection('notifications')
          .add(notification.toFirestore());
    } catch (e) {
      debugPrint('Error creating notification: $e');
    }
  }

  // Delete a notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).delete();
    } catch (e) {
      debugPrint('Error deleting notification: $e');
    }
  }

  // Delete all notifications
  Future<void> deleteAllNotifications() async {
    try {
      final batch = _firestore.batch();

      for (final notification in notifications) {
        batch.delete(
            _firestore.collection('notifications').doc(notification.id));
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error deleting all notifications: $e');
    }
  }

  // Listen for scheduled trips and send notifications
  StreamSubscription? _scheduledTripsSubscription;

  void _startListeningToScheduledTrips(String userId) {
    // Cancel any existing subscription
    _scheduledTripsSubscription?.cancel();

    // Get current time
    final now = Timestamp.now();
    // Get time 30 minutes from now
    final thirtyMinutesFromNow = Timestamp.fromDate(
      DateTime.now().add(const Duration(minutes: 30)),
    );

    // Listen for scheduled trips that are coming up in the next 30 minutes
    _scheduledTripsSubscription = _firestore
        .collection('tripRequests')
        .where('userId', isEqualTo: userId)
        .where('isScheduled', isEqualTo: true)
        .where('scheduledTime', isGreaterThanOrEqualTo: now)
        .where('scheduledTime', isLessThanOrEqualTo: thirtyMinutesFromNow)
        .snapshots()
        .listen((snapshot) {
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final scheduledTime = data['scheduledTime'] as Timestamp?;

        if (scheduledTime != null) {
          final tripId = doc.id;
          final directionTitle =
              data['directionTitle'] as String? ?? 'your destination';

          // Calculate time until scheduled trip
          final now = DateTime.now();
          final scheduledDateTime = scheduledTime.toDate();
          final difference = scheduledDateTime.difference(now);

          // Send notification 30 minutes before scheduled trip
          if (difference.inMinutes <= 30 && difference.inMinutes > 25) {
            _sendScheduledTripNotification(
              recipientId: userId,
              tripId: tripId,
              directionTitle: directionTitle,
              minutesUntilTrip: 30,
            );
          }

          // Send notification 15 minutes before scheduled trip
          if (difference.inMinutes <= 15 && difference.inMinutes > 10) {
            _sendScheduledTripNotification(
              recipientId: userId,
              tripId: tripId,
              directionTitle: directionTitle,
              minutesUntilTrip: 15,
            );
          }

          // Send notification 5 minutes before scheduled trip
          if (difference.inMinutes <= 5 && difference.inMinutes > 0) {
            _sendScheduledTripNotification(
              recipientId: userId,
              tripId: tripId,
              directionTitle: directionTitle,
              minutesUntilTrip: 5,
            );
          }
        }
      }
    });
  }

  // Send notification for scheduled trip
  Future<void> _sendScheduledTripNotification({
    required String recipientId,
    required String tripId,
    required String directionTitle,
    required int minutesUntilTrip,
  }) async {
    const title = 'Upcoming Trip Reminder';
    final body =
        'Your trip to $directionTitle is scheduled in $minutesUntilTrip minutes.';

    await createNotification(
      recipientId: recipientId,
      tripId: tripId,
      title: title,
      body: body,
      type: NotificationType.scheduledTrip,
    );
  }
}
