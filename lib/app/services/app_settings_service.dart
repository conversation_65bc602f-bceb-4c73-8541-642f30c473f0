import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import '../data/models/app_settings_model.dart';
import '../routes/app_pages.dart';

class AppSettingsService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Observable state
  final Rx<AppSettings?> appSettings = Rx<AppSettings?>(null);
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  // Currency symbol
  final RxString currencySymbol = 'SAR'.obs;

  Future<AppSettingsService> init() async {
    await loadAppSettings();
    return this;
  }

  Future<void> loadAppSettings() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final docRef = _firestore.collection('settings').doc('app_settings');
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        appSettings.value = AppSettings.fromFirestore(docSnapshot);
      } else {
        // Use default settings if none exist
        appSettings.value = AppSettings.defaultSettings;
      }

      // Update currency symbol
      _updateCurrencySymbol();
    } catch (e) {
      errorMessage.value = 'Error loading app settings: $e';
      print('Error loading app settings: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Check if app is in maintenance mode
  bool get isInMaintenanceMode {
    if (appSettings.value == null) return false;

    if (!appSettings.value!.maintenanceMode) return false;

    // Check if maintenance end time has passed
    if (appSettings.value!.maintenanceEndTime != null) {
      final now = DateTime.now();
      if (now.isAfter(appSettings.value!.maintenanceEndTime!)) {
        return false; // Maintenance period has ended
      }
    }

    return true;
  }

  // Check if app update is required
  bool get isUpdateRequired {
    if (appSettings.value == null) return false;

    if (!appSettings.value!.updateRequired) return false;

    // Compare current version with minimum required version
    final currentVersion = appSettings.value!.currentVersion.split('.');
    final minRequiredVersion = appSettings.value!.minRequiredVersion.split('.');

    // Compare major version
    if (int.parse(currentVersion[0]) < int.parse(minRequiredVersion[0])) {
      return true;
    }

    // Compare minor version if major versions are equal
    if (int.parse(currentVersion[0]) == int.parse(minRequiredVersion[0]) &&
        int.parse(currentVersion[1]) < int.parse(minRequiredVersion[1])) {
      return true;
    }

    // Compare patch version if major and minor versions are equal
    if (int.parse(currentVersion[0]) == int.parse(minRequiredVersion[0]) &&
        int.parse(currentVersion[1]) == int.parse(minRequiredVersion[1]) &&
        int.parse(currentVersion[2]) < int.parse(minRequiredVersion[2])) {
      return true;
    }

    return false;
  }

  // Show maintenance mode dialog
  void showMaintenanceDialog() {
    if (appSettings.value == null) return;

    Get.dialog(
      WillPopScope(
        onWillPop: () async => false, // Prevent dialog dismissal on back button
        child: AlertDialog(
          title: const Text('تطبيق قيد الصيانة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.engineering,
                size: 64,
                color: Colors.amber,
              ),
              const SizedBox(height: 16),
              Text(appSettings.value!.maintenanceMessage),
              if (appSettings.value!.maintenanceEndTime != null) ...[
                const SizedBox(height: 16),
                Text(
                  'وقت انتهاء الصيانة المتوقع: ${_formatDateTime(appSettings.value!.maintenanceEndTime!)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.offAllNamed(Routes.LOGIN),
              child: const Text('حسناً'),
            ),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  // Show update required dialog
  void showUpdateDialog() {
    if (appSettings.value == null) return;

    Get.dialog(
      WillPopScope(
        onWillPop: () async => false, // Prevent dialog dismissal on back button
        child: AlertDialog(
          title: const Text('تحديث مطلوب'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.system_update,
                size: 64,
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              Text(appSettings.value!.updateMessage),
              const SizedBox(height: 8),
              Text(
                'الإصدار الحالي: ${appSettings.value!.currentVersion}',
                style: const TextStyle(fontSize: 12),
              ),
              Text(
                'الإصدار المطلوب: ${appSettings.value!.minRequiredVersion}',
                style:
                    const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                _launchUpdateUrl(appSettings.value!.updateUrl);
              },
              child: const Text('تحديث الآن'),
            ),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  // Launch update URL
  Future<void> _launchUpdateUrl(String url) async {
    if (url.isEmpty) return;

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        Get.snackbar(
          'خطأ',
          'لا يمكن فتح رابط التحديث',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      print('Error launching URL: $e');
    }
  }

  // Format DateTime for display
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Calculate app fee
  double calculateAppFee(double tripPrice) {
    if (appSettings.value == null) return tripPrice * 0.10; // Default 10%

    return tripPrice * (appSettings.value!.appFeePercentage / 100);
  }

  // Calculate tax
  double calculateTax(double tripPrice) {
    if (appSettings.value == null) return tripPrice * 0.15; // Default 15%

    return tripPrice * (appSettings.value!.taxPercentage / 100);
  }

  // Update currency symbol from app settings
  void _updateCurrencySymbol() {
    if (appSettings.value != null &&
        appSettings.value!.currencySymbol.isNotEmpty) {
      currencySymbol.value = appSettings.value!.currencySymbol;
    } else {
      currencySymbol.value = 'SAR';
    }
  }

  // Get default country code
  String get defaultCountryCode {
    if (appSettings.value == null) return 'SA';

    return appSettings.value!.defaultCountryCode;
  }

  // Get supported countries
  List<String> get supportedCountries {
    if (appSettings.value == null) return ['SA'];

    return appSettings.value!.supportedCountries;
  }
}
