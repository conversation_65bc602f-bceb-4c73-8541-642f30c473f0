import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import '../data/models/coupon_model.dart';
import '../data/models/payment_model.dart';
import '../data/models/trip_request_model.dart';
import '../data/models/wallet_transaction_model.dart';
import 'app_settings_service.dart';

class PaymentService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AppSettingsService _appSettingsService = Get.find<AppSettingsService>();

  // Observable state
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  Future<PaymentService> init() async {
    return this;
  }

  // Validate coupon and calculate discount
  Future<Map<String, dynamic>> validateCoupon({
    required String couponCode,
    required String userId,
    required double amount,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = null;

      // Check if coupon exists
      final couponQuery = await _firestore
          .collection('coupons')
          .where('code', isEqualTo: couponCode)
          .where('isActive', isEqualTo: true)
          .get();

      if (couponQuery.docs.isEmpty) {
        return {
          'valid': false,
          'message': 'Invalid coupon code',
        };
      }

      final couponDoc = couponQuery.docs.first;
      final coupon = Coupon.fromFirestore(couponDoc);

      // Check if coupon is expired
      if (coupon.endDate != null &&
          coupon.endDate!.toDate().isBefore(DateTime.now())) {
        return {
          'valid': false,
          'message': 'Coupon has expired',
        };
      }

      // Check if coupon has usage limit
      if (coupon.usageLimit != null) {
        if (coupon.usageCount >= coupon.usageLimit!) {
          return {
            'valid': false,
            'message': 'Coupon usage limit reached',
          };
        }
      }

      // Check if user has already used this coupon
      if (coupon.usageLimitPerUser != null) {
        final userCouponUsageQuery = await _firestore
            .collection('couponUsage')
            .where('couponId', isEqualTo: coupon.id)
            .where('userId', isEqualTo: userId)
            .count()
            .get();

        final userUsageCount = userCouponUsageQuery.count ?? 0;

        if (userUsageCount >= coupon.usageLimitPerUser!) {
          return {
            'valid': false,
            'message': 'You have already used this coupon',
          };
        }
      }

      // Check minimum amount requirement
      if (coupon.minimumOrderAmount != null &&
          amount < coupon.minimumOrderAmount!) {
        return {
          'valid': false,
          'message':
              'Minimum amount for this coupon is ${coupon.minimumOrderAmount}',
        };
      }

      // Calculate discount amount
      double discountAmount = 0.0;

      if (coupon.discountType == DiscountType.percentage) {
        discountAmount = amount * (coupon.discountValue / 100);

        // Apply maximum discount if specified
        if (coupon.maximumDiscountAmount != null &&
            discountAmount > coupon.maximumDiscountAmount!) {
          discountAmount = coupon.maximumDiscountAmount!;
        }
      } else {
        // Fixed amount
        discountAmount = coupon.discountValue;

        // Ensure discount doesn't exceed the amount
        if (discountAmount > amount) {
          discountAmount = amount;
        }
      }

      return {
        'valid': true,
        'couponId': coupon.id,
        'discountAmount': discountAmount,
        'message': 'Coupon applied successfully',
      };
    } catch (e) {
      return {
        'valid': false,
        'message': 'Error validating coupon: $e',
      };
    } finally {
      isLoading.value = false;
    }
  }

  // Calculate trip price breakdown
  Future<Map<String, dynamic>> calculateTripPriceBreakdown({
    required double basePrice,
    String? couponCode,
    String? userId,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = null;

      double appFee = _appSettingsService.calculateAppFee(basePrice);
      double taxAmount = _appSettingsService.calculateTax(basePrice);
      double discountAmount = 0.0;
      String? couponId;

      // Apply coupon if provided
      if (couponCode != null && couponCode.isNotEmpty && userId != null) {
        final couponResult = await validateCoupon(
          couponCode: couponCode,
          userId: userId,
          amount: basePrice,
        );

        if (couponResult['valid']) {
          discountAmount = couponResult['discountAmount'];
          couponId = couponResult['couponId'];
        }
      }

      // Calculate total price
      double totalPrice = basePrice + appFee + taxAmount - discountAmount;

      // Ensure total price is not negative
      if (totalPrice < 0) totalPrice = 0;

      return {
        'basePrice': basePrice,
        'appFee': appFee,
        'taxAmount': taxAmount,
        'discountAmount': discountAmount,
        'totalPrice': totalPrice,
        'couponId': couponId,
        'couponCode': couponCode,
        'currencySymbol': _appSettingsService.currencySymbol,
      };
    } catch (e) {
      errorMessage.value = 'Error calculating trip price: $e';

      // Return default values
      return {
        'basePrice': basePrice,
        'appFee': 0.0,
        'taxAmount': 0.0,
        'discountAmount': 0.0,
        'totalPrice': basePrice,
        'couponId': null,
        'couponCode': null,
        'currencySymbol': _appSettingsService.currencySymbol,
      };
    } finally {
      isLoading.value = false;
    }
  }

  // Apply coupon to a trip
  Future<Map<String, dynamic>> applyCoupon({
    required String couponCode,
    required double amount,
    required String userId,
  }) async {
    try {
      return await validateCoupon(
        couponCode: couponCode,
        userId: userId,
        amount: amount,
      );
    } catch (e) {
      return {
        'valid': false,
        'message': 'Error applying coupon: $e',
      };
    }
  }

  // Get available coupons for a user
  Future<List<Map<String, dynamic>>> getAvailableCoupons({
    required String userId,
    required double amount,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = null;

      // Get all active coupons
      final couponQuery = await _firestore
          .collection('coupons')
          .where('isActive', isEqualTo: true)
          .get();

      if (couponQuery.docs.isEmpty) {
        return [];
      }

      final List<Map<String, dynamic>> availableCoupons = [];
      final now = Timestamp.now();

      // Get user's coupon usage
      final userCouponUsageQuery = await _firestore
          .collection('couponUsage')
          .where('userId', isEqualTo: userId)
          .get();

      // Create a map of coupon usage counts
      final Map<String, int> couponUsageCounts = {};
      for (final doc in userCouponUsageQuery.docs) {
        final data = doc.data();
        final couponId = data['couponId'];
        couponUsageCounts[couponId] = (couponUsageCounts[couponId] ?? 0) + 1;
      }

      // Get user creation date
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userCreatedAt = userDoc.data()?['createdAt'] ?? now;

      // Filter coupons
      for (final doc in couponQuery.docs) {
        final coupon = Coupon.fromFirestore(doc);

        // Check if coupon is valid for this user and amount
        if (coupon.isValid(
          currentTime: now,
          orderAmount: amount,
          userUsageCount: couponUsageCounts[coupon.id] ?? 0,
          userCreatedAt: userCreatedAt,
          userId: userId,
        )) {
          // Calculate discount
          final discountAmount = coupon.calculateDiscount(amount);

          availableCoupons.add({
            'id': coupon.id,
            'code': coupon.code,
            'title': coupon.title,
            'description': coupon.description,
            'discountType': coupon.discountType == DiscountType.percentage
                ? 'percentage'
                : 'fixed',
            'discountValue': coupon.discountValue,
            'discountAmount': discountAmount,
            'endDate': coupon.endDate,
          });
        }
      }

      // Sort by discount amount (highest first)
      availableCoupons.sort((a, b) => (b['discountAmount'] as double)
          .compareTo(a['discountAmount'] as double));

      return availableCoupons;
    } catch (e) {
      errorMessage.value = 'Error fetching available coupons: $e';
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  // Get user wallet balance
  Future<double> getWalletBalance(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return 0.0;
      }
      return (userDoc.data()?['walletBalance'] ?? 0.0).toDouble();
    } catch (e) {
      return 0.0;
    }
  }

  // Get wallet transactions for a user
  Future<List<WalletTransaction>> getWalletTransactions(String userId) async {
    try {
      final transactionsQuery = await _firestore
          .collection('walletTransactions')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      return transactionsQuery.docs
          .map((doc) => WalletTransaction.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Process payment for a trip
  Future<Map<String, dynamic>> processPayment({
    required String tripId,
    required double amount,
    required PaymentMethod paymentMethod,
    String? couponId,
    String? couponCode,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if payment has already been processed
      final existingPaymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: tripId)
          .where('status',
              isEqualTo: PaymentStatus.completed.toString().split('.').last)
          .get();

      // If payment has already been processed, return success
      if (existingPaymentQuery.docs.isNotEmpty) {
        final existingPayment = existingPaymentQuery.docs.first;
        return {
          'success': true,
          'message': 'Payment already processed',
          'paymentId': existingPayment.id,
        };
      }

      // Get trip details
      final tripDoc =
          await _firestore.collection('tripRequests').doc(tripId).get();
      if (!tripDoc.exists) {
        throw Exception('Trip not found');
      }

      final trip = TripRequest.fromFirestore(tripDoc);

      // Create payment record
      final payment = Payment(
        id: '',
        tripId: tripId,
        userId: currentUser.uid,
        driverId: trip.driverId,
        amount: amount,
        appFee: trip.appFee,
        taxAmount: trip.taxAmount,
        discountAmount: trip.discountAmount,
        method: paymentMethod,
        status: PaymentStatus.pending,
        couponId: couponId,
        couponCode: couponCode,
        createdAt: Timestamp.now(),
        baseAmount: trip.basePrice,
      );

      // Add payment to Firestore
      final paymentRef = await _firestore.collection('payments').add(
            payment.toFirestore(),
          );

      // Handle wallet payment
      if (paymentMethod == PaymentMethod.wallet) {
        // Check if user has enough balance
        final userDoc =
            await _firestore.collection('users').doc(currentUser.uid).get();
        final walletBalance =
            (userDoc.data()?['walletBalance'] ?? 0.0).toDouble();

        if (walletBalance < amount) {
          await _firestore.collection('payments').doc(paymentRef.id).update({
            'status': PaymentStatus.failed.toString().split('.').last,
            'failureReason': 'Insufficient wallet balance',
          });

          return {
            'success': false,
            'message': 'Insufficient wallet balance',
            'paymentId': paymentRef.id,
          };
        }

        // Deduct amount from user's wallet
        await _firestore.collection('users').doc(currentUser.uid).update({
          'walletBalance': FieldValue.increment(-amount),
        });

        // Update payment status
        await _firestore.collection('payments').doc(paymentRef.id).update({
          'status': PaymentStatus.completed.toString().split('.').last,
          'completedAt': Timestamp.now(),
        });

        // Get updated wallet balance after deduction
        final updatedUserDoc =
            await _firestore.collection('users').doc(currentUser.uid).get();
        final updatedWalletBalance =
            (updatedUserDoc.data()?['walletBalance'] ?? 0.0).toDouble();

        // Add wallet transaction record
        final transaction = WalletTransaction(
          id: '',
          userId: currentUser.uid,
          amount: -amount,
          type: TransactionType.payment,
          description: 'Payment for trip #${trip.id}',
          referenceId: paymentRef.id,
          tripId: tripId,
          timestamp: Timestamp.now(),
          balance: updatedWalletBalance,
          status: 'completed',
        );

        await _firestore.collection('walletTransactions').add(
              transaction.toFirestore(),
            );

        // Update trip request with payment information
        await _firestore.collection('tripRequests').doc(tripId).update({
          'paymentStatus': 'completed',
          'paymentMethod': paymentMethod.toString().split('.').last,
          'paymentId': paymentRef.id,
          'paymentCompletedAt': Timestamp.now(),
        });
      }

      return {
        'success': true,
        'message': 'Payment processed successfully',
        'paymentId': paymentRef.id,
      };
    } catch (e) {
      errorMessage.value = 'Error processing payment: $e';

      return {
        'success': false,
        'message': 'Error processing payment: $e',
        'paymentId': null,
      };
    } finally {
      isLoading.value = false;
    }
  }

  // Complete cash payment after trip
  Future<bool> completeCashPayment(String paymentId) async {
    try {
      // Get payment details
      final paymentDoc =
          await _firestore.collection('payments').doc(paymentId).get();
      if (!paymentDoc.exists) {
        return false;
      }

      final payment = Payment.fromFirestore(paymentDoc);

      // Update payment status
      await _firestore.collection('payments').doc(paymentId).update({
        'status': PaymentStatus.completed.toString().split('.').last,
        'completedAt': Timestamp.now(),
      });

      // Update trip request with payment information
      await _firestore.collection('tripRequests').doc(payment.tripId).update({
        'paymentStatus': 'completed',
        'paymentCompletedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print("Error completing cash payment: $e");
      return false;
    }
  }

  // Process driver payment
  Future<bool> processDriverPayment(String tripId, String driverId,
      [TripRequest? tripRequest]) async {
    try {
      // Check if driver payment has already been processed
      final walletTransactionQuery = await _firestore
          .collection('walletTransactions')
          .where('tripId', isEqualTo: tripId)
          .where('userId', isEqualTo: driverId)
          .where('type',
              isEqualTo: TransactionType.earning.toString().split('.').last)
          .get();

      // If driver payment has already been processed, return success
      if (walletTransactionQuery.docs.isNotEmpty) {
        return true;
      }

      // Get payment for trip
      final paymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: tripId)
          .get();

      String paymentId = '';
      double driverAmount = 0.0;

      if (paymentQuery.docs.isNotEmpty) {
        // Use existing payment record
        final paymentDoc = paymentQuery.docs.first;
        paymentId = paymentDoc.id;
        final payment = Payment.fromFirestore(paymentDoc);

        // Mark payment as completed if not already
        if (payment.status != PaymentStatus.completed) {
          await _firestore.collection('payments').doc(paymentId).update({
            'status': PaymentStatus.completed.toString().split('.').last,
            'completedAt': FieldValue.serverTimestamp(),
          });
        }

        // Calculate driver amount (base price minus app fee and tax)
        driverAmount = payment.amount - payment.appFee - payment.taxAmount;
      } else if (tripRequest != null) {
        // Create a new payment record using the trip request data
        final payment = {
          'tripId': tripId,
          'userId': tripRequest.userId,
          'driverId': driverId,
          'amount': tripRequest.totalPrice,
          'baseAmount': tripRequest.basePrice,
          'appFee': tripRequest.appFee,
          'taxAmount': tripRequest.taxAmount,
          'discountAmount': tripRequest.discountAmount,
          'method': tripRequest.paymentMethod,
          'status': 'completed',
          'createdAt': FieldValue.serverTimestamp(),
          'completedAt': FieldValue.serverTimestamp(),
        };

        final paymentRef = await _firestore.collection('payments').add(payment);
        paymentId = paymentRef.id;

        // Calculate driver amount (base price minus app fee and tax)
        driverAmount =
            tripRequest.totalPrice - tripRequest.appFee - tripRequest.taxAmount;
      } else {
        // No payment record and no trip request data
        return false;
      }

      // Add amount to driver's wallet
      await _firestore.collection('users').doc(driverId).update({
        'walletBalance': FieldValue.increment(driverAmount),
      });

      // Get current driver wallet balance
      final driverDoc =
          await _firestore.collection('users').doc(driverId).get();
      final driverBalance =
          (driverDoc.data()?['walletBalance'] ?? 0.0).toDouble();

      // Add wallet transaction record
      final transaction = WalletTransaction(
        id: '',
        userId: driverId,
        amount: driverAmount,
        type: TransactionType.earning,
        description: 'Earnings for trip #$tripId',
        referenceId: paymentId,
        tripId: tripId,
        timestamp: Timestamp.now(),
        status: 'completed',
        balance: driverBalance,
      );

      await _firestore.collection('walletTransactions').add(
            transaction.toFirestore(),
          );

      return true;
    } catch (e) {
      print("Error processing driver payment: $e");
      return false;
    }
  }
}
