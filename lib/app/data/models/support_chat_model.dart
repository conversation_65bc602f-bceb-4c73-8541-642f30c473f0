import 'package:cloud_firestore/cloud_firestore.dart';

enum SupportMessageSender { user, support }

class SupportChatMessage {
  final String id;
  final String userId;
  final String message;
  final SupportMessageSender sender;
  final Timestamp timestamp;
  final bool isRead;
  final String? attachmentUrl;
  final String? ticketId;

  SupportChatMessage({
    required this.id,
    required this.userId,
    required this.message,
    required this.sender,
    required this.timestamp,
    this.isRead = false,
    this.attachmentUrl,
    this.ticketId,
  });

  // Helper to convert enum to string for Firestore
  static String senderToString(SupportMessageSender sender) =>
      sender.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static SupportMessageSender senderFromString(String senderStr) =>
      SupportMessageSender.values.firstWhere(
          (e) => e.toString().split('.').last == senderStr,
          orElse: () => SupportMessageSender.user);

  factory SupportChatMessage.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return SupportChatMessage(
      id: doc.id,
      userId: data['userId'] ?? '',
      message: data['message'] ?? '',
      sender: senderFromString(data['sender'] ?? 'user'),
      timestamp: data['timestamp'] ?? Timestamp.now(),
      isRead: data['isRead'] ?? false,
      attachmentUrl: data['attachmentUrl'],
      ticketId: data['ticketId'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'message': message,
      'sender': senderToString(sender),
      'timestamp': timestamp,
      'isRead': isRead,
      'attachmentUrl': attachmentUrl,
      'ticketId': ticketId,
    };
  }
}

class SupportTicket {
  final String id;
  final String userId;
  final String subject;
  final String status; // 'open', 'in_progress', 'resolved', 'closed'
  final Timestamp createdAt;
  final Timestamp? lastUpdated;
  final Timestamp? resolvedAt;
  final String? assignedTo;
  final int priority; // 1-3 (low, medium, high)

  SupportTicket({
    required this.id,
    required this.userId,
    required this.subject,
    required this.status,
    required this.createdAt,
    this.lastUpdated,
    this.resolvedAt,
    this.assignedTo,
    this.priority = 2,
  });

  factory SupportTicket.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return SupportTicket(
      id: doc.id,
      userId: data['userId'] ?? '',
      subject: data['subject'] ?? '',
      status: data['status'] ?? 'open',
      createdAt: data['createdAt'] ?? Timestamp.now(),
      lastUpdated: data['lastUpdated'],
      resolvedAt: data['resolvedAt'],
      assignedTo: data['assignedTo'],
      priority: data['priority'] ?? 2,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'subject': subject,
      'status': status,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
      'resolvedAt': resolvedAt,
      'assignedTo': assignedTo,
      'priority': priority,
    };
  }

  SupportTicket copyWith({
    String? id,
    String? userId,
    String? subject,
    String? status,
    Timestamp? createdAt,
    Timestamp? lastUpdated,
    Timestamp? resolvedAt,
    String? assignedTo,
    int? priority,
  }) {
    return SupportTicket(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      subject: subject ?? this.subject,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      priority: priority ?? this.priority,
    );
  }
}
