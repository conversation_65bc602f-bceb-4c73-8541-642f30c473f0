import 'package:cloud_firestore/cloud_firestore.dart';

class FAQ {
  final String id;
  final String question;
  final String answer;
  final String category;
  final int order;
  final bool isActive;

  FAQ({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
    this.order = 0,
    this.isActive = true,
  });

  factory FAQ.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return FAQ(
      id: doc.id,
      question: data['question'] ?? '',
      answer: data['answer'] ?? '',
      category: data['category'] ?? 'General',
      order: data['order'] ?? 0,
      isActive: data['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'question': question,
      'answer': answer,
      'category': category,
      'order': order,
      'isActive': isActive,
    };
  }
}
