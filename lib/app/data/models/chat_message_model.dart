import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageSender {
  user,
  driver


}extension MessageSenderExtension on MessageSender {
  String toReadableString() {
    switch (this) {
      case MessageSender.user:
        return 'User';
      case MessageSender.driver:
        return 'Driver';
    }
  }
}

class ChatMessage {
  final String id;
  final String tripId;
  final String senderId;
  final MessageSender sender;
  final String message;
  final Timestamp timestamp;
  final bool isRead;

  ChatMessage({
    required this.id,
    required this.tripId,
    required this.senderId,
    required this.sender,
    required this.message,
    required this.timestamp,
    this.isRead = false,
  });

  // Helper to convert enum to string for Firestore
  static String senderToString(MessageSender sender) => sender.toString().split('.').last;
  
  // Helper to convert string from Firestore to enum
  static MessageSender senderFromString(String senderStr) =>
      MessageSender.values.firstWhere((e) => e.toString().split('.').last == senderStr,
          orElse: () => MessageSender.user);

  factory ChatMessage.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return ChatMessage(
      id: doc.id,
      tripId: data['tripId'] ?? '',
      senderId: data['senderId'] ?? '',
      sender: senderFromString(data['sender'] ?? 'user'),
      message: data['message'] ?? '',
      timestamp: data['timestamp'] ?? Timestamp.now(),
      isRead: data['isRead'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'tripId': tripId,
      'senderId': senderId,
      'sender': senderToString(sender),
      'message': message,
      'timestamp': timestamp,
      'isRead': isRead,
    };
  }
}
