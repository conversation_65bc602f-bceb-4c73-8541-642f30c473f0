import 'package:cloud_firestore/cloud_firestore.dart';

enum TransactionType {
  deposit,
  withdrawal,
  tripPayment,
  refund,
  bonus,
  payment,
  earning,
  other,
  all // Used for filtering only, not for actual transactions
}

class WalletTransaction {
  final String id;
  final String userId;
  final double amount;
  final TransactionType type;
  final String description;
  final String? tripId;
  final String? referenceId;
  final Timestamp timestamp;
  final double? balance;
  final String status; // 'completed', 'pending', 'failed'
  final Map<String, dynamic>? metadata;

  WalletTransaction({
    required this.id,
    required this.userId,
    required this.amount,
    required this.type,
    required this.description,
    this.tripId,
    this.referenceId,
    required this.timestamp,
    this.balance,
    required this.status,
    this.metadata,
  });

  // Helper to convert enum to string for Firestore
  static String typeToString(TransactionType type) =>
      type.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static TransactionType typeFromString(String typeStr) =>
      TransactionType.values.firstWhere(
          (e) => e.toString().split('.').last == typeStr,
          orElse: () => TransactionType.other);

  factory WalletTransaction.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return WalletTransaction(
      id: doc.id,
      userId: data['userId'] ?? '',
      amount: (data['amount'] ?? 0.0).toDouble(),
      type: typeFromString(data['type'] ?? 'other'),
      description: data['description'] ?? '',
      tripId: data['tripId'],
      referenceId: data['referenceId'],
      timestamp: data['timestamp'] ?? Timestamp.now(),
      balance:
          data['balance'] != null ? (data['balance'] as num).toDouble() : null,
      status: data['status'] ?? 'completed',
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'amount': amount,
      'type': typeToString(type),
      'description': description,
      'tripId': tripId,
      'referenceId': referenceId,
      'timestamp': timestamp,
      'balance': balance,
      'status': status,
      'metadata': metadata,
    };
  }

  // Helper method to create a copy with some fields changed
  WalletTransaction copyWith({
    String? id,
    String? userId,
    double? amount,
    TransactionType? type,
    String? description,
    String? tripId,
    String? referenceId,
    Timestamp? timestamp,
    double? balance,
    String? status,
    Map<String, dynamic>? metadata,
  }) {
    return WalletTransaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      description: description ?? this.description,
      tripId: tripId ?? this.tripId,
      referenceId: referenceId ?? this.referenceId,
      timestamp: timestamp ?? this.timestamp,
      balance: balance ?? this.balance,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }
}
