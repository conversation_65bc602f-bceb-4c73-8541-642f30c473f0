import 'package:cloud_firestore/cloud_firestore.dart';

enum NotificationType {
  tripUpdate,
  chat,
  driverArrived,
  tripStarted,
  tripCompleted,
  tripCancelled,
  scheduledTrip,
  other
}

class AppNotification {
  final String id;
  final String recipientId;
  final String? tripId;
  final String title;
  final String body;
  final NotificationType type;
  final Timestamp timestamp;
  final bool isRead;
  final String? sound;
  final String? priority;

  AppNotification({
    required this.id,
    required this.recipientId,
    this.tripId,
    required this.title,
    required this.body,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.sound = 'default',
    this.priority = 'high',
  });

  // Helper to convert enum to string for Firestore
  static String typeToString(NotificationType type) =>
      type.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static NotificationType typeFromString(String typeStr) =>
      NotificationType.values.firstWhere(
          (e) => e.toString().split('.').last == typeStr,
          orElse: () => NotificationType.other);

  factory AppNotification.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return AppNotification(
      id: doc.id,
      recipientId: data['recipientId'] ?? '',
      tripId: data['tripId'],
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: typeFromString(data['type'] ?? 'other'),
      timestamp: data['timestamp'] ?? Timestamp.now(),
      isRead: data['isRead'] ?? false,
      sound: data['sound'],
      priority: data['priority'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'recipientId': recipientId,
      'tripId': tripId,
      'title': title,
      'body': body,
      'type': typeToString(type),
      'timestamp': timestamp,
      'isRead': isRead,
      'sound': sound,
      'priority': priority,
    };
  }
}
