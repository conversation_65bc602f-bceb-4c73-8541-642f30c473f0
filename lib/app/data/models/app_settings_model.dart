import 'package:cloud_firestore/cloud_firestore.dart';

class AppSettings {
  // App Version Settings
  final String currentVersion;
  final String minRequiredVersion;
  final bool updateRequired;
  final String updateUrl;
  final String updateMessage;
  
  // App Status Settings
  final bool maintenanceMode;
  final String maintenanceMessage;
  final DateTime? maintenanceEndTime;
  
  // Financial Settings
  final double appFeePercentage;
  final double taxPercentage;
  final String currencyCode;
  final String currencySymbol;
  
  // Country Settings
  final String defaultCountryCode;
  final List<String> supportedCountries;
  
  // Contact Settings
  final String supportEmail;
  final String supportPhone;
  final String supportWhatsapp;
  
  // Terms and Privacy
  final String termsUrl;
  final String privacyUrl;
  
  // Last Updated
  final Timestamp lastUpdated;
  final String updatedBy;

  AppSettings({
    required this.currentVersion,
    required this.minRequiredVersion,
    required this.updateRequired,
    required this.updateUrl,
    required this.updateMessage,
    required this.maintenanceMode,
    required this.maintenanceMessage,
    this.maintenanceEndTime,
    required this.appFeePercentage,
    required this.taxPercentage,
    required this.currencyCode,
    required this.currencySymbol,
    required this.defaultCountryCode,
    required this.supportedCountries,
    required this.supportEmail,
    required this.supportPhone,
    required this.supportWhatsapp,
    required this.termsUrl,
    required this.privacyUrl,
    required this.lastUpdated,
    required this.updatedBy,
  });

  // Factory constructor to create AppSettings from Firestore document
  factory AppSettings.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    
    return AppSettings(
      // App Version Settings
      currentVersion: data['currentVersion'] ?? '1.0.0',
      minRequiredVersion: data['minRequiredVersion'] ?? '1.0.0',
      updateRequired: data['updateRequired'] ?? false,
      updateUrl: data['updateUrl'] ?? '',
      updateMessage: data['updateMessage'] ?? 'Please update to the latest version for new features and bug fixes.',
      
      // App Status Settings
      maintenanceMode: data['maintenanceMode'] ?? false,
      maintenanceMessage: data['maintenanceMessage'] ?? 'App is under maintenance. Please try again later.',
      maintenanceEndTime: data['maintenanceEndTime'] != null 
          ? (data['maintenanceEndTime'] as Timestamp).toDate() 
          : null,
      
      // Financial Settings
      appFeePercentage: (data['appFeePercentage'] ?? 10.0).toDouble(),
      taxPercentage: (data['taxPercentage'] ?? 15.0).toDouble(),
      currencyCode: data['currencyCode'] ?? 'SAR',
      currencySymbol: data['currencySymbol'] ?? 'ر.س',
      
      // Country Settings
      defaultCountryCode: data['defaultCountryCode'] ?? 'SA',
      supportedCountries: List<String>.from(data['supportedCountries'] ?? ['SA']),
      
      // Contact Settings
      supportEmail: data['supportEmail'] ?? '<EMAIL>',
      supportPhone: data['supportPhone'] ?? '+966123456789',
      supportWhatsapp: data['supportWhatsapp'] ?? '+966123456789',
      
      // Terms and Privacy
      termsUrl: data['termsUrl'] ?? 'https://example.com/terms',
      privacyUrl: data['privacyUrl'] ?? 'https://example.com/privacy',
      
      // Last Updated
      lastUpdated: data['lastUpdated'] ?? Timestamp.now(),
      updatedBy: data['updatedBy'] ?? 'system',
    );
  }

  // Convert AppSettings to Map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      // App Version Settings
      'currentVersion': currentVersion,
      'minRequiredVersion': minRequiredVersion,
      'updateRequired': updateRequired,
      'updateUrl': updateUrl,
      'updateMessage': updateMessage,
      
      // App Status Settings
      'maintenanceMode': maintenanceMode,
      'maintenanceMessage': maintenanceMessage,
      'maintenanceEndTime': maintenanceEndTime != null 
          ? Timestamp.fromDate(maintenanceEndTime!) 
          : null,
      
      // Financial Settings
      'appFeePercentage': appFeePercentage,
      'taxPercentage': taxPercentage,
      'currencyCode': currencyCode,
      'currencySymbol': currencySymbol,
      
      // Country Settings
      'defaultCountryCode': defaultCountryCode,
      'supportedCountries': supportedCountries,
      
      // Contact Settings
      'supportEmail': supportEmail,
      'supportPhone': supportPhone,
      'supportWhatsapp': supportWhatsapp,
      
      // Terms and Privacy
      'termsUrl': termsUrl,
      'privacyUrl': privacyUrl,
      
      // Last Updated
      'lastUpdated': FieldValue.serverTimestamp(),
      'updatedBy': updatedBy,
    };
  }

  // Create a copy with updated fields
  AppSettings copyWith({
    String? currentVersion,
    String? minRequiredVersion,
    bool? updateRequired,
    String? updateUrl,
    String? updateMessage,
    bool? maintenanceMode,
    String? maintenanceMessage,
    DateTime? maintenanceEndTime,
    double? appFeePercentage,
    double? taxPercentage,
    String? currencyCode,
    String? currencySymbol,
    String? defaultCountryCode,
    List<String>? supportedCountries,
    String? supportEmail,
    String? supportPhone,
    String? supportWhatsapp,
    String? termsUrl,
    String? privacyUrl,
    Timestamp? lastUpdated,
    String? updatedBy,
  }) {
    return AppSettings(
      currentVersion: currentVersion ?? this.currentVersion,
      minRequiredVersion: minRequiredVersion ?? this.minRequiredVersion,
      updateRequired: updateRequired ?? this.updateRequired,
      updateUrl: updateUrl ?? this.updateUrl,
      updateMessage: updateMessage ?? this.updateMessage,
      maintenanceMode: maintenanceMode ?? this.maintenanceMode,
      maintenanceMessage: maintenanceMessage ?? this.maintenanceMessage,
      maintenanceEndTime: maintenanceEndTime ?? this.maintenanceEndTime,
      appFeePercentage: appFeePercentage ?? this.appFeePercentage,
      taxPercentage: taxPercentage ?? this.taxPercentage,
      currencyCode: currencyCode ?? this.currencyCode,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      defaultCountryCode: defaultCountryCode ?? this.defaultCountryCode,
      supportedCountries: supportedCountries ?? this.supportedCountries,
      supportEmail: supportEmail ?? this.supportEmail,
      supportPhone: supportPhone ?? this.supportPhone,
      supportWhatsapp: supportWhatsapp ?? this.supportWhatsapp,
      termsUrl: termsUrl ?? this.termsUrl,
      privacyUrl: privacyUrl ?? this.privacyUrl,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  // Default settings
  static AppSettings get defaultSettings => AppSettings(
    currentVersion: '1.0.0',
    minRequiredVersion: '1.0.0',
    updateRequired: false,
    updateUrl: '',
    updateMessage: 'Please update to the latest version for new features and bug fixes.',
    maintenanceMode: false,
    maintenanceMessage: 'App is under maintenance. Please try again later.',
    maintenanceEndTime: null,
    appFeePercentage: 10.0,
    taxPercentage: 15.0,
    currencyCode: 'SAR',
    currencySymbol: 'ر.س',
    defaultCountryCode: 'SA',
    supportedCountries: ['SA'],
    supportEmail: '<EMAIL>',
    supportPhone: '+966123456789',
    supportWhatsapp: '+966123456789',
    termsUrl: 'https://example.com/terms',
    privacyUrl: 'https://example.com/privacy',
    lastUpdated: Timestamp.now(),
    updatedBy: 'system',
  );
}
