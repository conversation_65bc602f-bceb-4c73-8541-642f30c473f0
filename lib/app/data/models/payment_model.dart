import 'package:cloud_firestore/cloud_firestore.dart';

enum PaymentMethod {
  cash,
  creditCard,
  debitCard,
  wallet,
  applePay,
  googlePay,
  stcPay,
  madaPay,
  other
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
  partiallyRefunded,
  cancelled
}

class Payment {
  final String id;
  final String userId;
  final String? driverId;
  final String tripId;
  final double amount;
  final double baseAmount; // Original trip price
  final double appFee; // App fee amount
  final double taxAmount; // Tax amount
  final double discountAmount; // Discount amount (from coupons)
  final PaymentMethod method;
  final PaymentStatus status;
  final String? transactionId; // External payment gateway transaction ID
  final Timestamp createdAt;
  final Timestamp? completedAt;
  final String? couponId;
  final String? couponCode;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  Payment({
    required this.id,
    required this.userId,
    this.driverId,
    required this.tripId,
    required this.amount,
    required this.baseAmount,
    required this.appFee,
    required this.taxAmount,
    required this.discountAmount,
    required this.method,
    required this.status,
    this.transactionId,
    required this.createdAt,
    this.completedAt,
    this.couponId,
    this.couponCode,
    this.failureReason,
    this.metadata,
  });

  // Helper to convert enum to string for Firestore
  static String methodToString(PaymentMethod method) =>
      method.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static PaymentMethod methodFromString(String methodStr) =>
      PaymentMethod.values.firstWhere(
          (e) => e.toString().split('.').last == methodStr,
          orElse: () => PaymentMethod.other);

  // Helper to convert enum to string for Firestore
  static String statusToString(PaymentStatus status) =>
      status.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static PaymentStatus statusFromString(String statusStr) =>
      PaymentStatus.values.firstWhere(
          (e) => e.toString().split('.').last == statusStr,
          orElse: () => PaymentStatus.pending);

  factory Payment.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Payment(
      id: doc.id,
      userId: data['userId'] ?? '',
      driverId: data['driverId'],
      tripId: data['tripId'] ?? '',
      amount: (data['amount'] ?? 0.0).toDouble(),
      baseAmount: (data['baseAmount'] ?? 0.0).toDouble(),
      appFee: (data['appFee'] ?? 0.0).toDouble(),
      taxAmount: (data['taxAmount'] ?? 0.0).toDouble(),
      discountAmount: (data['discountAmount'] ?? 0.0).toDouble(),
      method: methodFromString(data['method'] ?? 'cash'),
      status: statusFromString(data['status'] ?? 'pending'),
      transactionId: data['transactionId'],
      createdAt: data['createdAt'] ?? Timestamp.now(),
      completedAt: data['completedAt'],
      couponId: data['couponId'],
      couponCode: data['couponCode'],
      failureReason: data['failureReason'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'driverId': driverId,
      'tripId': tripId,
      'amount': amount,
      'baseAmount': baseAmount,
      'appFee': appFee,
      'taxAmount': taxAmount,
      'discountAmount': discountAmount,
      'method': methodToString(method),
      'status': statusToString(status),
      'transactionId': transactionId,
      'createdAt': createdAt,
      'completedAt': completedAt,
      'couponId': couponId,
      'couponCode': couponCode,
      'failureReason': failureReason,
      'metadata': metadata,
    };
  }

  // Helper method to create a copy with some fields changed
  Payment copyWith({
    String? id,
    String? userId,
    String? driverId,
    String? tripId,
    double? amount,
    double? baseAmount,
    double? appFee,
    double? taxAmount,
    double? discountAmount,
    PaymentMethod? method,
    PaymentStatus? status,
    String? transactionId,
    Timestamp? createdAt,
    Timestamp? completedAt,
    String? couponId,
    String? couponCode,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return Payment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      driverId: driverId ?? this.driverId,
      tripId: tripId ?? this.tripId,
      amount: amount ?? this.amount,
      baseAmount: baseAmount ?? this.baseAmount,
      appFee: appFee ?? this.appFee,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      method: method ?? this.method,
      status: status ?? this.status,
      transactionId: transactionId ?? this.transactionId,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      couponId: couponId ?? this.couponId,
      couponCode: couponCode ?? this.couponCode,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }
}
