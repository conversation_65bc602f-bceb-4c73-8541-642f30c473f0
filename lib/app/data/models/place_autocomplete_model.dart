// Model for parsing Places Autocomplete API response
class PlaceAutocompletePrediction {
  final String description; // The human-readable name/address
  final String placeId;     // Unique identifier for getting details

  PlaceAutocompletePrediction({required this.description, required this.placeId});

  factory PlaceAutocompletePrediction.fromJson(Map<String, dynamic> json) {
    return PlaceAutocompletePrediction(
      description: json['description'] as String? ?? 'Unknown Place',
      placeId: json['place_id'] as String? ?? '',
    );
  }
}