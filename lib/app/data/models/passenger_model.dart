class Passenger {
  final String name;
  final String documentNumber;
  final String country;
  final String documentType;
  final String gender;

  Passenger({
    required this.name,
    required this.documentNumber,
    required this.country,
    required this.documentType,
    required this.gender
  });

  factory Passenger.fromJson(Map<String, dynamic> json) {
    return Passenger(
      name: json['name'],
      documentNumber: json['documentNumber'],
      country: json['country'],
      documentType: json['documentType'],
      gender: json['gender'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'documentNumber': documentNumber,
      'country': country,
      'documentType': documentType,
      'gender': gender,
    };
  }
}
