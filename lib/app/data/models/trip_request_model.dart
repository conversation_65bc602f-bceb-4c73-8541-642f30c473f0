import 'package:cloud_firestore/cloud_firestore.dart';

enum TripStatus {
  searching, // Looking for driver
  scheduled, // Looking for driver
  assigned, // Driver assigned, en route to pickup
  enRouteToPickup, // Driver on the way to passenger
  arrivedAtPickup, // Driver has arrived at pickup
  ongoing, // Trip started towards destination
  completed, // Trip finished
  cancelledByUser,
  cancelledByDriver,
  noDriversAvailable // Special status if search fails
}

class TripRequest {
  final String id;
  final String userId;
  final String directionId; // Link to the chosen Direction
  final String directionTitle; // Denormalized for easy display
  final GeoPoint
      pickupLocation; // Could be startLocation of Direction or user's current GPS
  final GeoPoint dropoffLocation; // endLocation of Direction
  final int passengers;
  final int bags;
  final String notes;
  final double basePrice; // Original price from Direction
  final double appFee; // App fee amount
  final double taxAmount; // Tax amount
  final double discountAmount; // Discount amount (from coupons)
  final double totalPrice; // Final price after fees, taxes, and discounts
  final String? couponId; // Applied coupon ID
  final String? couponCode; // Applied coupon code
  final String paymentMethod; // Payment method (cash, wallet, card, etc.)
  TripStatus status;
  String? driverId; // Assigned driver's UID
  Timestamp createdAt;
  Timestamp? assignedAt;
  Timestamp? completedAt;

  // Scheduling information
  final bool isScheduled; // Whether this is a scheduled trip or immediate
  final Timestamp?
      scheduledTime; // When the trip is scheduled for (null if immediate)

  // Route information
  String? distanceText; // Distance in text format (e.g., "5.2 km")
  String? durationText; // Duration in text format (e.g., "15 mins")
  String? routePolyline; // Encoded polyline for the route

  // Driver information
  String? driverName;
  String? driverVehicle;
  double? driverRating;

  // User information
  String? userName;
  double? userRating;

  // Chat information
  int unreadMessageCount = 0; // Number of unread messages
  int driverUnreadMessageCount = 0; // Number of unread messages for the driver

  TripRequest({
    required this.id,
    required this.userId,
    required this.directionId,
    required this.directionTitle,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.passengers,
    required this.bags,
    required this.notes,
    required this.basePrice,
    required this.appFee,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalPrice,
    this.couponId,
    this.couponCode,
    required this.paymentMethod,
    this.status = TripStatus.searching,
    this.driverId,
    required this.createdAt,
    this.assignedAt,
    this.completedAt,
    this.isScheduled = false,
    this.scheduledTime,
    this.distanceText,
    this.durationText,
    this.routePolyline,
    this.driverName,
    this.driverVehicle,
    this.driverRating,
    this.userName,
    this.userRating,
    this.unreadMessageCount = 0,
    this.driverUnreadMessageCount = 0,
  });

  // Helper to convert enum to string for Firestore
  static String statusToString(TripStatus status) =>
      status.toString().split('.').last;
  // Helper to convert string from Firestore to enum
  static TripStatus statusFromString(String statusStr) => TripStatus.values
      .firstWhere((e) => e.toString().split('.').last == statusStr,
          orElse: () => TripStatus.searching); // Default if unknown status

  factory TripRequest.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return TripRequest(
      id: doc.id,
      userId: data['userId'] ?? '',
      directionId: data['directionId'] ?? '',
      directionTitle: data['directionTitle'] ?? '',
      pickupLocation: data['pickupLocation'] ?? const GeoPoint(0, 0),
      dropoffLocation: data['dropoffLocation'] ?? const GeoPoint(0, 0),
      passengers: data['passengers'] ?? 1,
      bags: data['bags'] ?? 0,
      notes: data['notes'] ?? '',
      // Payment information
      basePrice: (data['basePrice'] ?? 0.0).toDouble(),
      appFee: (data['appFee'] ?? 0.0).toDouble(),
      taxAmount: (data['taxAmount'] ?? 0.0).toDouble(),
      discountAmount: (data['discountAmount'] ?? 0.0).toDouble(),
      totalPrice: (data['totalPrice'] ?? (data['price'] ?? 0.0))
          .toDouble(), // Backward compatibility
      couponId: data['couponId'],
      couponCode: data['couponCode'],
      paymentMethod: data['paymentMethod'] ?? 'cash',
      status: statusFromString(data['status'] ?? 'searching'),
      driverId: data['driverId'], // Can be null
      createdAt: data['createdAt'] ?? Timestamp.now(),
      assignedAt: data['assignedAt'], // Can be null
      completedAt: data['completedAt'], // Can be null
      // Scheduling information
      isScheduled: data['isScheduled'] ?? false,
      scheduledTime: data['scheduledTime'],
      // Route information
      distanceText: data['distanceText'],
      durationText: data['durationText'],
      routePolyline: data['routePolyline'],
      // Driver information
      driverName: data['driverName'],
      driverVehicle: data['driverVehicle'],
      driverRating: data['driverRating'] != null
          ? (data['driverRating'] as num).toDouble()
          : null,
      // User information
      userName: data['userName'],
      userRating: data['userRating'] != null
          ? (data['userRating'] as num).toDouble()
          : null,
      // Chat information
      unreadMessageCount: data['unreadMessageCount'] ?? 0,
      driverUnreadMessageCount: data['driverUnreadMessageCount'] ?? 0,
    );
  }

  // Create a TripRequest from a Map (useful for converting from controller data)
  factory TripRequest.fromMap(Map<String, dynamic> data) {
    return TripRequest(
      id: data['id'] ?? '',
      userId: data['userId'] ?? '',
      directionId: data['directionId'] ?? '',
      directionTitle: data['directionTitle'] ?? '',
      pickupLocation: data['pickupLocation'] ?? const GeoPoint(0, 0),
      dropoffLocation: data['dropoffLocation'] ?? const GeoPoint(0, 0),
      passengers: data['passengers'] ?? 1,
      bags: data['bags'] ?? 0,
      notes: data['notes'] ?? '',
      // Payment information
      basePrice: (data['basePrice'] ?? 0.0).toDouble(),
      appFee: (data['appFee'] ?? 0.0).toDouble(),
      taxAmount: (data['taxAmount'] ?? 0.0).toDouble(),
      discountAmount: (data['discountAmount'] ?? 0.0).toDouble(),
      totalPrice: (data['totalPrice'] ?? (data['price'] ?? 0.0))
          .toDouble(), // Backward compatibility
      couponId: data['couponId'],
      couponCode: data['couponCode'],
      paymentMethod: data['paymentMethod'] ?? 'cash',
      status: data['status'] is String
          ? statusFromString(data['status'])
          : (data['status'] ?? TripStatus.searching),
      driverId: data['driverId'],
      createdAt: data['createdAt'] ?? Timestamp.now(),
      assignedAt: data['assignedAt'],
      completedAt: data['completedAt'],
      // Scheduling information
      isScheduled: data['isScheduled'] ?? false,
      scheduledTime: data['scheduledTime'],
      // Route information
      distanceText: data['distanceText'],
      durationText: data['durationText'],
      routePolyline: data['routePolyline'],
      // Driver information
      driverName: data['driverName'],
      driverVehicle: data['driverVehicle'],
      driverRating: data['driverRating'] != null
          ? (data['driverRating'] is num
              ? (data['driverRating'] as num).toDouble()
              : data['driverRating'])
          : null,
      // User information
      userName: data['userName'],
      userRating: data['userRating'] != null
          ? (data['userRating'] is num
              ? (data['userRating'] as num).toDouble()
              : data['userRating'])
          : null,
      // Chat information
      unreadMessageCount: data['unreadMessageCount'] ?? 0,
      driverUnreadMessageCount: data['driverUnreadMessageCount'] ?? 0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'directionId': directionId,
      'directionTitle': directionTitle,
      'pickupLocation': pickupLocation,
      'dropoffLocation': dropoffLocation,
      'passengers': passengers,
      'bags': bags,
      'notes': notes,
      // Payment information
      'basePrice': basePrice,
      'appFee': appFee,
      'taxAmount': taxAmount,
      'discountAmount': discountAmount,
      'totalPrice': totalPrice,
      'couponId': couponId,
      'couponCode': couponCode,
      'paymentMethod': paymentMethod,
      'status': statusToString(status), // Store status as string
      'driverId': driverId,
      'createdAt': createdAt,
      'assignedAt': assignedAt,
      'completedAt': completedAt,
      // Scheduling information
      'isScheduled': isScheduled,
      'scheduledTime': scheduledTime,
      // Route information
      'distanceText': distanceText,
      'durationText': durationText,
      'routePolyline': routePolyline,
      // Driver information
      'driverName': driverName,
      'driverVehicle': driverVehicle,
      'driverRating': driverRating,
      // User information
      'userName': userName,
      'userRating': userRating,
      // Chat information
      'unreadMessageCount': unreadMessageCount,
      'driverUnreadMessageCount': driverUnreadMessageCount,
    };
  }
}
