import 'package:cloud_firestore/cloud_firestore.dart';

class Rating {
  final String id;
  final String tripId;
  final String ratedByUserId;
  final String ratedUserId;
  final double rating; // 1-5 stars
  final String? comment;
  final List<String> tags; // e.g., ['polite', 'on-time', 'clean']
  final Timestamp timestamp;
  final String type; // 'user_to_driver', 'driver_to_user', 'user_to_app'

  Rating({
    required this.id,
    required this.tripId,
    required this.ratedByUserId,
    required this.ratedUserId,
    required this.rating,
    this.comment,
    this.tags = const [],
    required this.timestamp,
    required this.type,
  });

  factory Rating.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Rating(
      id: doc.id,
      tripId: data['tripId'] ?? '',
      ratedByUserId: data['ratedByUserId'] ?? '',
      ratedUserId: data['ratedUserId'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      comment: data['comment'],
      tags: List<String>.from(data['tags'] ?? []),
      timestamp: data['timestamp'] ?? Timestamp.now(),
      type: data['type'] ?? 'user_to_driver',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'tripId': tripId,
      'ratedByUserId': ratedByUserId,
      'ratedUserId': ratedUserId,
      'rating': rating,
      'comment': comment,
      'tags': tags,
      'timestamp': timestamp,
      'type': type,
    };
  }
}
