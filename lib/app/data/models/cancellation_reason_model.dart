import 'package:cloud_firestore/cloud_firestore.dart';

class CancellationReason {
  final String id;
  final String reason;
  final String category; // 'user', 'driver', 'both'
  final bool isActive;
  final int order;

  CancellationReason({
    required this.id,
    required this.reason,
    required this.category,
    this.isActive = true,
    this.order = 0,
  });

  factory CancellationReason.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return CancellationReason(
      id: doc.id,
      reason: data['reason'] ?? '',
      category: data['category'] ?? 'both',
      isActive: data['isActive'] ?? true,
      order: data['order'] ?? 0,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'reason': reason,
      'category': category,
      'isActive': isActive,
      'order': order,
    };
  }
}

// Predefined cancellation reasons
List<CancellationReason> getPredefinedCancellationReasons() {
  return [
    CancellationReason(
      id: 'wait_too_long',
      reason: 'انتظرت طويلاً',
      category: 'user',
      order: 1,
    ),
    CancellationReason(
      id: 'changed_mind',
      reason: 'غيرت رأيي',
      category: 'user',
      order: 2,
    ),
    CancellationReason(
      id: 'wrong_location',
      reason: 'الموقع غير صحيح',
      category: 'both',
      order: 3,
    ),
    CancellationReason(
      id: 'driver_asked',
      reason: 'طلب السائق إلغاء الرحلة',
      category: 'user',
      order: 4,
    ),
    CancellationReason(
      id: 'emergency',
      reason: 'حالة طارئة',
      category: 'both',
      order: 5,
    ),
    CancellationReason(
      id: 'price_too_high',
      reason: 'السعر مرتفع جداً',
      category: 'user',
      order: 6,
    ),
    CancellationReason(
      id: 'user_not_responding',
      reason: 'المستخدم لا يستجيب',
      category: 'driver',
      order: 1,
    ),
    CancellationReason(
      id: 'car_issue',
      reason: 'مشكلة في السيارة',
      category: 'driver',
      order: 2,
    ),
    CancellationReason(
      id: 'traffic',
      reason: 'ازدحام مروري شديد',
      category: 'driver',
      order: 3,
    ),
  ];
}
