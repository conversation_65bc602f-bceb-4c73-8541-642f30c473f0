import 'package:cloud_firestore/cloud_firestore.dart';

enum UserStatus { active, blocked, suspended, underReview }

class UserModel {
  final String uid;
  final String email;
  final String name;
  final String? phoneNumber;
  final String? address;  // Added address field
  final String role;
  final String? profileImageUrl;
  final double walletBalance;
  final Timestamp? createdAt;
  final Timestamp? lastLoginAt;
  final int completedTrips;
  final double rating;
  final int ratingCount;
  final bool notificationsEnabled;
  final UserStatus status;
  final String? statusReason;
  final Timestamp? suspensionEndTime;
  final Map<String, dynamic>? vehicleInfo;  // Added vehicleInfo field

  UserModel({
    required this.uid,
    required this.email,
    required this.name,
    this.phoneNumber,
    this.address,  // Added address parameter
    required this.role,
    this.profileImageUrl,
    this.walletBalance = 0.0,
    this.createdAt,
    this.lastLoginAt,
    this.completedTrips = 0,
    this.rating = 0.0,
    this.ratingCount = 0,
    this.notificationsEnabled = true,
    this.status = UserStatus.active,
    this.statusReason,
    this.suspensionEndTime,
    this.vehicleInfo,  // Added vehicleInfo parameter
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    // Convert string status to enum
    UserStatus userStatus = UserStatus.active;
    if (data['status'] != null) {
      try {
        userStatus = UserStatus.values.firstWhere(
          (e) => e.toString().split('.').last == data['status'],
          orElse: () => UserStatus.active,
        );
      } catch (e) {
        // Default to active if there's an error
        userStatus = UserStatus.active;
      }
    }

    return UserModel(
      uid: doc.id,
      email: data['email'] ?? '',
      name: data['name'] ?? 'User',
      phoneNumber: data['phoneNumber'],
      address: data['address'],  // Added address field
      role: data['role'] ?? 'user',
      profileImageUrl: data['profileImageUrl'],
      walletBalance: (data['walletBalance'] ?? 0.0).toDouble(),
      createdAt: data['createdAt'],
      lastLoginAt: data['lastLoginAt'],
      completedTrips: data['completedTrips'] ?? 0,
      rating: (data['rating'] ?? 0.0).toDouble(),
      ratingCount: data['ratingCount'] ?? 0,
      notificationsEnabled: data['notificationsEnabled'] ?? true,
      status: userStatus,
      statusReason: data['statusReason'],
      suspensionEndTime: data['suspensionEndTime'],
      vehicleInfo: data['vehicleInfo'] as Map<String, dynamic>?,  // Added vehicleInfo field
    );
  }

  // Helper to convert enum to string for Firestore
  static String statusToString(UserStatus status) =>
      status.toString().split('.').last;

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'address': address,  // Added address field
      'role': role,
      'profileImageUrl': profileImageUrl,
      'walletBalance': walletBalance,
      'createdAt': createdAt,
      'lastLoginAt': lastLoginAt,
      'completedTrips': completedTrips,
      'rating': rating,
      'ratingCount': ratingCount,
      'notificationsEnabled': notificationsEnabled,
      'status': statusToString(status),
      'statusReason': statusReason,
      'suspensionEndTime': suspensionEndTime,
      'vehicleInfo': vehicleInfo,  // Added vehicleInfo field
    };
  }

  // Create a copy of the user with updated fields
  UserModel copyWith({
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? address,  // Added address parameter
    String? role,
    String? profileImageUrl,
    double? walletBalance,
    Timestamp? createdAt,
    Timestamp? lastLoginAt,
    int? completedTrips,
    double? rating,
    int? ratingCount,
    bool? notificationsEnabled,
    UserStatus? status,
    String? statusReason,
    Timestamp? suspensionEndTime,
    Map<String, dynamic>? vehicleInfo,  // Added vehicleInfo parameter
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,  // Added address field
      role: role ?? this.role,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      walletBalance: walletBalance ?? this.walletBalance,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      completedTrips: completedTrips ?? this.completedTrips,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      status: status ?? this.status,
      statusReason: statusReason ?? this.statusReason,
      suspensionEndTime: suspensionEndTime ?? this.suspensionEndTime,
      vehicleInfo: vehicleInfo ?? this.vehicleInfo,  // Added vehicleInfo field
    );
  }
}
