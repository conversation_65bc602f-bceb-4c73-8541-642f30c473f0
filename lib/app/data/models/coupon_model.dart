import 'package:cloud_firestore/cloud_firestore.dart';

enum DiscountType {
  percentage, // Percentage discount (e.g., 10% off)
  fixed // Fixed amount discount (e.g., 20 SAR off)
}

enum UserType {
  all, // All users
  new_users, // New users only (registered within X days)
  existing_users, // Existing users only (registered more than X days ago)
  specific_users // Specific users by ID
}

class Coupon {
  final String id;
  final String code; // Coupon code that users enter
  final String title; // Display title
  final String description; // Description of the coupon
  final DiscountType discountType;
  final double discountValue; // Percentage or fixed amount
  final double? minimumOrderAmount; // Minimum order amount to apply coupon
  final double? maximumDiscountAmount; // Maximum discount amount for percentage discounts
  final Timestamp startDate; // When the coupon becomes valid
  final Timestamp? endDate; // When the coupon expires (null = no expiration)
  final int? usageLimit; // Maximum number of times the coupon can be used in total
  final int? usageLimitPerUser; // Maximum number of times a user can use this coupon
  final int usageCount; // How many times the coupon has been used
  final bool isActive; // Whether the coupon is active
  final UserType userType; // Type of users who can use this coupon
  final int? newUserDays; // For new_users type, how many days since registration
  final List<String>? specificUserIds; // For specific_users type, list of user IDs
  final Timestamp createdAt;
  final Timestamp? updatedAt;
  final String? createdBy; // Admin who created the coupon

  Coupon({
    required this.id,
    required this.code,
    required this.title,
    required this.description,
    required this.discountType,
    required this.discountValue,
    this.minimumOrderAmount,
    this.maximumDiscountAmount,
    required this.startDate,
    this.endDate,
    this.usageLimit,
    this.usageLimitPerUser,
    required this.usageCount,
    required this.isActive,
    required this.userType,
    this.newUserDays,
    this.specificUserIds,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
  });

  // Helper to convert enum to string for Firestore
  static String discountTypeToString(DiscountType type) =>
      type.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static DiscountType discountTypeFromString(String typeStr) =>
      DiscountType.values.firstWhere(
          (e) => e.toString().split('.').last == typeStr,
          orElse: () => DiscountType.percentage);

  // Helper to convert enum to string for Firestore
  static String userTypeToString(UserType type) =>
      type.toString().split('.').last;

  // Helper to convert string from Firestore to enum
  static UserType userTypeFromString(String typeStr) => UserType.values
      .firstWhere((e) => e.toString().split('.').last == typeStr,
          orElse: () => UserType.all);

  factory Coupon.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Coupon(
      id: doc.id,
      code: data['code'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      discountType: discountTypeFromString(data['discountType'] ?? 'percentage'),
      discountValue: (data['discountValue'] ?? 0.0).toDouble(),
      minimumOrderAmount: data['minimumOrderAmount'] != null
          ? (data['minimumOrderAmount'] as num).toDouble()
          : null,
      maximumDiscountAmount: data['maximumDiscountAmount'] != null
          ? (data['maximumDiscountAmount'] as num).toDouble()
          : null,
      startDate: data['startDate'] ?? Timestamp.now(),
      endDate: data['endDate'],
      usageLimit: data['usageLimit'],
      usageLimitPerUser: data['usageLimitPerUser'],
      usageCount: data['usageCount'] ?? 0,
      isActive: data['isActive'] ?? false,
      userType: userTypeFromString(data['userType'] ?? 'all'),
      newUserDays: data['newUserDays'],
      specificUserIds: data['specificUserIds'] != null
          ? List<String>.from(data['specificUserIds'])
          : null,
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'],
      createdBy: data['createdBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'code': code,
      'title': title,
      'description': description,
      'discountType': discountTypeToString(discountType),
      'discountValue': discountValue,
      'minimumOrderAmount': minimumOrderAmount,
      'maximumDiscountAmount': maximumDiscountAmount,
      'startDate': startDate,
      'endDate': endDate,
      'usageLimit': usageLimit,
      'usageLimitPerUser': usageLimitPerUser,
      'usageCount': usageCount,
      'isActive': isActive,
      'userType': userTypeToString(userType),
      'newUserDays': newUserDays,
      'specificUserIds': specificUserIds,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? FieldValue.serverTimestamp(),
      'createdBy': createdBy,
    };
  }

  // Helper method to create a copy with some fields changed
  Coupon copyWith({
    String? id,
    String? code,
    String? title,
    String? description,
    DiscountType? discountType,
    double? discountValue,
    double? minimumOrderAmount,
    double? maximumDiscountAmount,
    Timestamp? startDate,
    Timestamp? endDate,
    int? usageLimit,
    int? usageLimitPerUser,
    int? usageCount,
    bool? isActive,
    UserType? userType,
    int? newUserDays,
    List<String>? specificUserIds,
    Timestamp? createdAt,
    Timestamp? updatedAt,
    String? createdBy,
  }) {
    return Coupon(
      id: id ?? this.id,
      code: code ?? this.code,
      title: title ?? this.title,
      description: description ?? this.description,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      minimumOrderAmount: minimumOrderAmount ?? this.minimumOrderAmount,
      maximumDiscountAmount: maximumDiscountAmount ?? this.maximumDiscountAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      usageLimit: usageLimit ?? this.usageLimit,
      usageLimitPerUser: usageLimitPerUser ?? this.usageLimitPerUser,
      usageCount: usageCount ?? this.usageCount,
      isActive: isActive ?? this.isActive,
      userType: userType ?? this.userType,
      newUserDays: newUserDays ?? this.newUserDays,
      specificUserIds: specificUserIds ?? this.specificUserIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  // Calculate discount amount for a given order amount
  double calculateDiscount(double orderAmount) {
    // Check if minimum order amount is met
    if (minimumOrderAmount != null && orderAmount < minimumOrderAmount!) {
      return 0.0;
    }

    double discount = 0.0;
    if (discountType == DiscountType.percentage) {
      discount = orderAmount * (discountValue / 100);
      // Apply maximum discount if specified
      if (maximumDiscountAmount != null && discount > maximumDiscountAmount!) {
        discount = maximumDiscountAmount!;
      }
    } else {
      // Fixed discount
      discount = discountValue;
      // Ensure discount doesn't exceed order amount
      if (discount > orderAmount) {
        discount = orderAmount;
      }
    }

    return discount;
  }

  // Check if coupon is valid
  bool isValid({
    required Timestamp currentTime,
    required double orderAmount,
    required int userUsageCount,
    required Timestamp userCreatedAt,
    String? userId,
  }) {
    // Check if coupon is active
    if (!isActive) return false;

    // Check if coupon has expired
    if (endDate != null && currentTime.compareTo(endDate!) > 0) return false;

    // Check if coupon start date is in the future
    if (currentTime.compareTo(startDate) < 0) return false;

    // Check if minimum order amount is met
    if (minimumOrderAmount != null && orderAmount < minimumOrderAmount!) {
      return false;
    }

    // Check if coupon has reached its usage limit
    if (usageLimit != null && usageCount >= usageLimit!) return false;

    // Check if user has reached their usage limit
    if (usageLimitPerUser != null && userUsageCount >= usageLimitPerUser!) {
      return false;
    }

    // Check user type restrictions
    if (userType == UserType.new_users) {
      // Calculate days since user registration
      final daysSinceRegistration = currentTime
          .toDate()
          .difference(userCreatedAt.toDate())
          .inDays;
      if (newUserDays != null && daysSinceRegistration > newUserDays!) {
        return false;
      }
    } else if (userType == UserType.existing_users) {
      // Calculate days since user registration
      final daysSinceRegistration = currentTime
          .toDate()
          .difference(userCreatedAt.toDate())
          .inDays;
      if (newUserDays != null && daysSinceRegistration <= newUserDays!) {
        return false;
      }
    } else if (userType == UserType.specific_users) {
      // Check if user is in the specific users list
      if (userId == null ||
          specificUserIds == null ||
          !specificUserIds!.contains(userId)) {
        return false;
      }
    }

    return true;
  }
}
