import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class Direction {
  final String id;
  final String title;
  final String description; // Optional description
  final double basePrice;
  final double pricePerKm;
  final GeoPoint startLocation; // Use GeoPoint for Firestore
  final GeoPoint endLocation;
  final double estimatedDistance; // in kilometers
  final int estimatedDuration; // in minutes
  final bool isActive;
  final Timestamp createdAt;
  final Timestamp? updatedAt;
  final String? imageUrl;

  Direction({
    required this.id,
    required this.title,
    this.description = '',
    required this.basePrice,
    required this.pricePerKm,
    required this.startLocation,
    required this.endLocation,
    this.estimatedDistance = 0.0,
    this.estimatedDuration = 0,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.imageUrl,
  });

  // Factory constructor to create a Direction from a Firestore document
  factory Direction.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Direction(
      id: doc.id,
      title: data['title'] ?? 'Unnamed Direction',
      description: data['description'] ?? '',
      basePrice: (data['basePrice'] ?? 0.0).toDouble(),
      pricePerKm: (data['pricePerKm'] ?? 0.0).toDouble(),
      startLocation: data['startLocation'] ?? const GeoPoint(0, 0),
      endLocation: data['endLocation'] ?? const GeoPoint(0, 0),
      estimatedDistance: (data['estimatedDistance'] ?? 0.0).toDouble(),
      estimatedDuration: data['estimatedDuration'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: data['createdAt'] ?? Timestamp.now(),
      updatedAt: data['updatedAt'],
      imageUrl: data['imageUrl'],
    );
  }

  // Method to convert Direction instance to a map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'basePrice': basePrice,
      'pricePerKm': pricePerKm,
      'startLocation': startLocation,
      'endLocation': endLocation,
      'estimatedDistance': estimatedDistance,
      'estimatedDuration': estimatedDuration,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? FieldValue.serverTimestamp(),
      'imageUrl': imageUrl,
    };
  }

  // Calculate total price based on distance
  double calculateTotalPrice(double actualDistance) {
    return basePrice + (pricePerKm * actualDistance);
  }

  // Get estimated price
  double get estimatedPrice {
    return basePrice + (pricePerKm * estimatedDistance);
  }

  // Convert GeoPoint to LatLng for Google Maps
  LatLng get startLatLng =>
      LatLng(startLocation.latitude, startLocation.longitude);
  LatLng get endLatLng => LatLng(endLocation.latitude, endLocation.longitude);

  // Create a copy with updated fields
  Direction copyWith({
    String? id,
    String? title,
    String? description,
    double? basePrice,
    double? pricePerKm,
    GeoPoint? startLocation,
    GeoPoint? endLocation,
    double? estimatedDistance,
    int? estimatedDuration,
    bool? isActive,
    Timestamp? createdAt,
    Timestamp? updatedAt,
    String? imageUrl,
  }) {
    return Direction(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      basePrice: basePrice ?? this.basePrice,
      pricePerKm: pricePerKm ?? this.pricePerKm,
      startLocation: startLocation ?? this.startLocation,
      endLocation: endLocation ?? this.endLocation,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}
