final Map<String, String> enTranslations = {
  // App Bar Titles
  'loading_app': 'Loading Delivery App...',
  'choose_destination': 'Choose Your Destination',
  'set_pickup': 'Set Pickup Location',
  'confirm_route': 'Confirm Your Route',
  'enter_trip_details': 'Enter Trip Details',
  'finding_driver': 'Finding Your Driver...',
  'tracking_trip': 'Tracking Your Trip',
    'trip_details': 'Trip Details',


  // Instruction Banner
  'tap_to_set_pickup': 'Tap on the map to set PICKUP for %s',

  // Directions List
  'available_destinations': 'Available Destinations',
  'no_destinations': 'No destinations found.',
  'fixed_dropoff': 'Fixed Drop-off Area',

  // Route Preview Card
  'duration': 'Duration',
  'distance': 'Distance',
  'est_price': 'Est. Price',
  'confirm_route_button': 'Confirm Route & Enter Details',
  'change_pickup': 'Change Pickup Location',

  // Trip Details Form
  'trip_to': 'Trip to: %s',
  'estimated_price': 'Estimated Price: %s',
  'passengers': 'Passengers',
  'bags': 'Bags',
  'notes_for_driver': 'Notes for Driver (Optional)',
  'notes_hint': 'e.g., Gate code, specific instructions',
  'schedule_for_later': 'Schedule for later',
  'select_date_time': 'Select Date & Time',
  'back': 'Back',
  'request_ride_now': 'Request Ride Now',
  'schedule_trip': 'Schedule Trip',
  'min_1': 'Min 1',
  'max_4': 'Max 4',
  'invalid': 'Invalid',
  'max_3': 'Max 3',
  'required': 'Required',

  // Trip Status UI
  'waiting_for_trip': 'Waiting for trip data...',
  'looking_for_drivers': 'Looking for nearby drivers...',
  'driver_on_the_way': 'Driver is on the way!',
  'driver_arrived': 'Your driver has arrived!',
  'trip_in_progress': 'Trip to %s in progress',
  'trip_completed': 'Trip Completed!',
  'trip_cancelled': 'Trip Cancelled',
  'no_drivers_available': 'No drivers were available.',
  'estimated_arrival': 'Estimated arrival: %s',
  'calculating': 'Calculating...',
  'vehicle': 'Vehicle: %s',
  'cancel_request': 'Cancel Request',
  'chat_with_driver': 'Chat with Driver',
  'done': 'Done',

  // Search
  'search_pickup': 'Search for pickup address...',
  'use_current_location': 'Use Current Location',
  'search_on_map': 'Search on Map',
  'tap_map_to_select': 'Tap on the map to select a location',
  'no_results': 'No results found.',
  'search_destination': 'Search for destination...',
  'tap_map_to_select_destination': 'Tap on the map to select a destination',
  'custom_destination': 'Custom Destination',
  'custom_destination_desc': 'Choose any location on the map',
  'select_custom_destination': 'Select Custom Destination',
  'custom_direction_error': 'Error creating custom destination',

  // Error Messages
  'error': 'Error',
  'initialization_failed': 'Initialization Failed',
  'logout_exit': 'Logout and Exit',

  // Payment
  'payment_method': 'Payment Method',
  'cash': 'Cash',
  'wallet': 'Wallet',
  'credit_card': 'Credit Card',
  'apply_coupon': 'Apply Coupon',
  'enter_coupon': 'Enter coupon code',
  'apply': 'Apply',
  'remove': 'Remove',
  'subtotal': 'Subtotal',
  'discount': 'Discount',
  'total': 'Total',

  // General
  'your_driver': 'Your Driver',
  'language': 'Language',

  // Driver Home
  'you_are_online': 'You are Online',
  'you_are_offline': 'You are Offline',
  'logout': 'Logout',
  'initializing_map': 'Initializing map and location...',
  'online': 'ONLINE',
  'offline': 'OFFLINE',
  'receiving_ride_requests': 'Receiving ride requests',
  'not_receiving_requests': 'Not receiving requests',
  'waiting_for_ride_requests': 'Waiting for ride requests...',
  'new_ride_request': 'NEW RIDE REQUEST!',
  'pickup': 'Pickup: %s',
  'approx_distance_away': 'Approx. %s away (%s)',
  'destination': 'Destination: %s',
  'reject': 'Reject',
  'accept_ride': 'ACCEPT RIDE',
  'navigate_to_pickup': 'Navigate to Pickup',
  'arrived_at_pickup': 'ARRIVED AT PICKUP',
  'waiting_for_passenger': 'Waiting for Passenger',
  'start_trip': 'START TRIP',
  'navigate_to_destination': 'Navigate to Destination',
  'complete_trip': 'COMPLETE TRIP',
  'trip_status': 'Trip Status: %s',
  'update_status': 'Update Status',
  'you_have_arrived': 'You have arrived at pickup',
  'dropoff': 'Drop-off: %s',
  'chat_with_passenger': 'Chat with Passenger',
  'cancel_assigned_trip': 'Cancel Assigned Trip',
  'active_trip': 'Active Trip',
  'scheduled': 'Scheduled',
  'scheduled_for': 'Scheduled for: %s',
  'passenger_singular': '%s passenger',
  'passenger_plural': '%s passengers',
  'bag_singular': '%s bag',
  'bag_plural': '%s bags',
  'notes': 'Notes: %s',
  'distance_duration': '%s • %s',
  'pickup_location': 'Pickup: %s, %s',
  'calculating': 'Calculating...',
  'few_mins': 'few mins',
  'app_fee': 'App Fee',
  'tax': 'Tax',

  // Passengers
  'request_passenger_data': 'Request Passenger Data',
  'select_passengers': 'Select Passengers',
  'no_passengers': 'No passengers found',
  'add_passenger': 'Add Passenger',
  'edit_passenger': 'Edit Passenger',
  'name': 'Name',
  'name_required': 'Name is required',
  'document_type': 'Document Type',
  'document_type_required': 'Document type is required',
  'document_number': 'Document Number',
  'document_number_required': 'Document number is required',
  'country': 'Country',
  'country_required': 'Country is required',
  'gender': 'Gender',
  'gender_required': 'Gender is required',
  'add': 'Add',
  'update': 'Update',
  'passport': 'Passport',
  'id_card': 'ID Card',
  'drivers_license': 'Driver\'s License',
  'male': 'Male',
  'female': 'Female',
  'other': 'Other',
  'passenger_added': 'Passenger added successfully',
  'passenger_updated': 'Passenger updated successfully',
  'passenger_deleted': 'Passenger deleted successfully',
  'passenger_not_found': 'Passenger not found',
  'add_passenger_error': 'Error adding passenger',
  'update_passenger_error': 'Error updating passenger',
  'delete_passenger_error': 'Error deleting passenger',
  'select_n_passengers': 'Select {{count}} passengers',
};
