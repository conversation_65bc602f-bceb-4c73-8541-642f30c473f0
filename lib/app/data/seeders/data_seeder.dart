import 'package:flutter/foundation.dart';
import 'package:ai_delivery_app/app/data/seeders/faq_seeder.dart';

/// A utility class to seed initial data to Firestore
class DataSeeder {
  final FAQSeeder _faqSeeder;

  DataSeeder({FAQSeeder? faqSeeder}) 
      : _faqSeeder = faqSeeder ?? FAQSeeder();

  /// Seed all initial data to Firestore
  Future<void> seedAllData() async {
    try {
      debugPrint('Starting data seeding process...');
      
      // Seed FAQs
      await _faqSeeder.seedFAQs();
      
      // Add more seeders here as needed
      // await _otherSeeder.seedData();
      
      debugPrint('Data seeding completed successfully.');
    } catch (e) {
      debugPrint('Error during data seeding: $e');
    }
  }
}
