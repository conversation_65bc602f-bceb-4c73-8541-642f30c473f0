import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// A utility class to seed FAQ data to Firestore
class FAQSeeder {
  final FirebaseFirestore _firestore;

  FAQSeeder({FirebaseFirestore? firestore}) 
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Seed FAQ data to Firestore
  Future<void> seedFAQs() async {
    try {
      // Check if FAQs already exist
      final snapshot = await _firestore.collection('faqs').limit(1).get();
      if (snapshot.docs.isNotEmpty) {
        debugPrint('FAQs already exist in Firestore. Skipping seeding.');
        return;
      }

      // Create a batch to add all FAQs at once
      final batch = _firestore.batch();
      
      // Add each FAQ to the batch
      for (int i = 0; i < defaultFAQs.length; i++) {
        final faq = defaultFAQs[i];
        final docRef = _firestore.collection('faqs').doc();
        batch.set(docRef, {
          ...faq,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // Commit the batch
      await batch.commit();
      debugPrint('Successfully seeded ${defaultFAQs.length} FAQs to Firestore.');
    } catch (e) {
      debugPrint('Error seeding FAQs: $e');
      rethrow;
    }
  }

  /// Default FAQs to seed
  static final List<Map<String, dynamic>> defaultFAQs = [
    // General Category
    {
      'question': 'ما هو تطبيق توصيل؟',
      'answer': 'تطبيق توصيل هو منصة توصيل تربط بين السائقين والركاب، مما يتيح للمستخدمين طلب رحلات بسهولة وأمان من خلال هواتفهم الذكية.',
      'category': 'عام',
      'order': 1,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني التواصل مع خدمة العملاء؟',
      'answer': 'يمكنك التواصل مع خدمة العملاء من خلال قسم "الدعم" في التطبيق، أو الاتصال بنا على الرقم 920001234، أو إرسال بريد إلكتروني إلى <EMAIL>.',
      'category': 'عام',
      'order': 2,
      'isActive': true,
    },
    {
      'question': 'ماذا أفعل إذا نسيت شيئًا في سيارة السائق؟',
      'answer': 'إذا نسيت شيئًا في سيارة السائق، يمكنك التواصل معنا من خلال قسم "الدعم" في التطبيق. سنساعدك في التواصل مع السائق لاسترداد أغراضك.',
      'category': 'عام',
      'order': 3,
      'isActive': true,
    },
    
    // Trips Category
    {
      'question': 'كيف يمكنني طلب رحلة؟',
      'answer': 'يمكنك طلب رحلة من خلال فتح التطبيق واختيار وجهتك، ثم النقر على زر "طلب رحلة". سيتم توصيلك بأقرب سائق متاح.',
      'category': 'الرحلات',
      'order': 4,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني جدولة رحلة مستقبلية؟',
      'answer': 'يمكنك جدولة رحلة مستقبلية من خلال اختيار وجهتك، ثم تفعيل خيار "جدولة رحلة" واختيار التاريخ والوقت المناسبين لك.',
      'category': 'الرحلات',
      'order': 5,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني إلغاء رحلة؟',
      'answer': 'يمكنك إلغاء رحلة من خلال النقر على زر "إلغاء الرحلة" في شاشة تفاصيل الرحلة. يرجى ملاحظة أنه قد يتم تطبيق رسوم إلغاء إذا تم إلغاء الرحلة بعد وصول السائق.',
      'category': 'الرحلات',
      'order': 6,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني الحصول على إيصال للرحلة؟',
      'answer': 'يتم إرسال إيصال الرحلة تلقائيًا إلى بريدك الإلكتروني بعد انتهاء الرحلة. يمكنك أيضًا الاطلاع على جميع الإيصالات من خلال قسم "سجل الرحلات" في التطبيق.',
      'category': 'الرحلات',
      'order': 7,
      'isActive': true,
    },
    
    // Payment Category
    {
      'question': 'ما هي طرق الدفع المتاحة؟',
      'answer': 'يدعم التطبيق الدفع النقدي، وبطاقات الائتمان، وبطاقات مدى، ومحفظة آبل، ومحفظة جوجل، وSTC Pay.',
      'category': 'الدفع',
      'order': 8,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني إضافة طريقة دفع؟',
      'answer': 'يمكنك إضافة طريقة دفع من خلال الانتقال إلى قسم "المحفظة" في القائمة الجانبية، ثم النقر على "إضافة طريقة دفع" واتباع التعليمات.',
      'category': 'الدفع',
      'order': 9,
      'isActive': true,
    },
    {
      'question': 'كيف يتم احتساب سعر الرحلة؟',
      'answer': 'يتم احتساب سعر الرحلة بناءً على المسافة والوقت المستغرق والطلب الحالي على الرحلات في منطقتك. يمكنك رؤية تقدير للسعر قبل تأكيد الرحلة.',
      'category': 'الدفع',
      'order': 10,
      'isActive': true,
    },
    
    // Account Category
    {
      'question': 'كيف يمكنني تغيير معلومات حسابي؟',
      'answer': 'يمكنك تعديل معلومات حسابك من خلال الانتقال إلى "الملف الشخصي" في القائمة الجانبية، ثم النقر على "تعديل الملف الشخصي".',
      'category': 'الحساب',
      'order': 11,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني تغيير رقم هاتفي؟',
      'answer': 'لتغيير رقم هاتفك، انتقل إلى "الملف الشخصي" ثم "تعديل الملف الشخصي" ثم "تغيير رقم الهاتف". سيتم إرسال رمز تحقق إلى رقمك الجديد للتأكيد.',
      'category': 'الحساب',
      'order': 12,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني تغيير كلمة المرور الخاصة بي؟',
      'answer': 'لتغيير كلمة المرور، انتقل إلى "الملف الشخصي" ثم "الإعدادات" ثم "تغيير كلمة المرور". ستحتاج إلى إدخال كلمة المرور الحالية ثم كلمة المرور الجديدة.',
      'category': 'الحساب',
      'order': 13,
      'isActive': true,
    },
    
    // Drivers Category
    {
      'question': 'كيف يمكنني التسجيل كسائق؟',
      'answer': 'للتسجيل كسائق، يرجى تنزيل تطبيق "توصيل للسائقين" واتباع خطوات التسجيل. ستحتاج إلى تقديم رخصة القيادة واستمارة السيارة وبعض المستندات الأخرى.',
      'category': 'السائقين',
      'order': 14,
      'isActive': true,
    },
    {
      'question': 'ما هي متطلبات العمل كسائق؟',
      'answer': 'للعمل كسائق، يجب أن تكون فوق 21 عامًا، ولديك رخصة قيادة سارية المفعول، وسيارة موديل 2015 أو أحدث، وتأمين ساري المفعول، وسجل قيادة نظيف.',
      'category': 'السائقين',
      'order': 15,
      'isActive': true,
    },
    {
      'question': 'كيف يتم احتساب أرباح السائق؟',
      'answer': 'يتم احتساب أرباح السائق بناءً على المسافة والوقت المستغرق في الرحلة، مع خصم عمولة التطبيق. يمكن للسائقين سحب أرباحهم أسبوعيًا.',
      'category': 'السائقين',
      'order': 16,
      'isActive': true,
    },
    
    // Technical Category
    {
      'question': 'ماذا أفعل إذا واجهت مشكلة فنية في التطبيق؟',
      'answer': 'إذا واجهت مشكلة فنية، يرجى محاولة إعادة تشغيل التطبيق أو تحديثه إلى أحدث إصدار. إذا استمرت المشكلة، يمكنك التواصل مع الدعم الفني من خلال قسم "الدعم" في التطبيق.',
      'category': 'تقني',
      'order': 17,
      'isActive': true,
    },
    {
      'question': 'لماذا لا يمكنني رؤية موقعي على الخريطة؟',
      'answer': 'إذا لم تتمكن من رؤية موقعك على الخريطة، تأكد من تفعيل خدمات الموقع لتطبيق توصيل في إعدادات هاتفك. إذا كانت خدمات الموقع مفعلة بالفعل، حاول إعادة تشغيل التطبيق أو هاتفك.',
      'category': 'تقني',
      'order': 18,
      'isActive': true,
    },
    {
      'question': 'كيف يمكنني تحديث التطبيق؟',
      'answer': 'يمكنك تحديث التطبيق من خلال متجر التطبيقات (App Store لأجهزة آيفون أو Google Play لأجهزة أندرويد). ابحث عن "توصيل" وانقر على "تحديث" إذا كان هناك تحديث متاح.',
      'category': 'تقني',
      'order': 19,
      'isActive': true,
    },
    {
      'question': 'هل يمكنني استخدام التطبيق بدون اتصال بالإنترنت؟',
      'answer': 'لا، يتطلب استخدام التطبيق اتصالاً بالإنترنت لطلب الرحلات وتتبع السائقين والدفع. تأكد من وجود اتصال إنترنت مستقر قبل استخدام التطبيق.',
      'category': 'تقني',
      'order': 20,
      'isActive': true,
    },
  ];
}
