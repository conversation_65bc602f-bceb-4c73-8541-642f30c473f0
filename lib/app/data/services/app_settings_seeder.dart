import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/app_settings_model.dart';

class AppSettingsSeeder {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Future<void> seedAppSettings() async {
    try {
      // Check if app settings already exist
      final docRef = _firestore.collection('settings').doc('app_settings');
      final docSnapshot = await docRef.get();
      
      if (!docSnapshot.exists) {
        print('App settings not found, creating default settings');
        
        // Create default app settings
        final defaultSettings = AppSettings.defaultSettings;
        
        // Save to Firestore
        await docRef.set(defaultSettings.toFirestore());
        
        print('Successfully created default app settings');
      } else {
        print('App settings already exist, skipping seeding');
      }
    } catch (e) {
      print('Error seeding app settings: $e');
      throw e;
    }
  }
}
