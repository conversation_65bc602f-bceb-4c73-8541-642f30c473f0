import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../translations/ar_translations.dart';
import '../translations/en_translations.dart';

class TranslationService extends Translations {
  static final fallbackLocale = Locale('en', 'US');
  static final storage = GetStorage();
  static final storageKey = 'language';

  // Supported languages
  static final locales = [
    Locale('en', 'US'),
    Locale('ar', 'SA'),
  ];

  // Default locale
  static Locale get defaultLocale {
    String? storedLocale = storage.read(storageKey);
    if (storedLocale != null) {
      final parts = storedLocale.split('_');
      if (parts.length == 2) {
        return Locale(parts[0], parts[1]);
      }
    }
    
    // If no stored preference or invalid format, use device locale or fallback
    final deviceLocale = Get.deviceLocale;
    if (deviceLocale != null && isLocaleSupported(deviceLocale)) {
      return deviceLocale;
    }
    
    return fallbackLocale;
  }

  // Check if a locale is supported
  static bool isLocaleSupported(Locale locale) {
    return locales.any((supportedLocale) => 
      supportedLocale.languageCode == locale.languageCode);
  }

  // Save selected language to storage
  static void saveLanguage(Locale locale) {
    storage.write(storageKey, '${locale.languageCode}_${locale.countryCode}');
  }

  // Change language
  static void changeLocale(String languageCode, String countryCode) {
    final locale = Locale(languageCode, countryCode);
    saveLanguage(locale);
    Get.updateLocale(locale);
  }

  // Get the current text direction based on locale
  static TextDirection getTextDirection() {
    return Get.locale?.languageCode == 'ar' 
        ? TextDirection.rtl 
        : TextDirection.ltr;
  }

  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': enTranslations,
    'ar_SA': arTranslations,
  };
}
