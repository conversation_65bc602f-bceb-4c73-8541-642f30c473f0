import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/direction_model.dart';

class DirectionSeeder {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Future<void> seedDirections() async {
    try {
      // Check if directions already exist
      final snapshot = await _firestore.collection('directions').limit(1).get();
      if (snapshot.docs.isNotEmpty) {
        print('Directions already exist, skipping seeding');
        return;
      }
      
      // Sample directions data
      final List<Direction> sampleDirections = [
        Direction(
          id: '',
          title: 'غار حراء',
          description: 'رحلة إلى غار حراء التاريخي',
          basePrice: 150.0,
          pricePerKm: 5.0,
          startLocation: const GeoPoint(21.3891, 39.8579), // Mecca
          endLocation: const GeoPoint(21.4614, 39.8741), // Hira Cave
          estimatedDistance: 15.0,
          estimatedDuration: 30,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
        Direction(
          id: '',
          title: 'المسجد النبوي',
          description: 'رحلة إلى المسجد النبوي الشريف',
          basePrice: 200.0,
          pricePerKm: 6.0,
          startLocation: const GeoPoint(21.3891, 39.8579), // Mecca
          endLocation: const GeoPoint(24.4672, 39.6112), // Medina
          estimatedDistance: 450.0,
          estimatedDuration: 300,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
        Direction(
          id: '',
          title: 'جبل أحد',
          description: 'رحلة إلى جبل أحد التاريخي',
          basePrice: 120.0,
          pricePerKm: 4.0,
          startLocation: const GeoPoint(24.4672, 39.6112), // Medina
          endLocation: const GeoPoint(24.4822, 39.6291), // Mount Uhud
          estimatedDistance: 8.0,
          estimatedDuration: 20,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
        Direction(
          id: '',
          title: 'المسجد الحرام',
          description: 'رحلة إلى المسجد الحرام',
          basePrice: 100.0,
          pricePerKm: 5.0,
          startLocation: const GeoPoint(24.4672, 39.6112), // Medina
          endLocation: const GeoPoint(21.4225, 39.8262), // Masjid al-Haram
          estimatedDistance: 450.0,
          estimatedDuration: 300,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
        Direction(
          id: '',
          title: 'جبل النور',
          description: 'رحلة إلى جبل النور',
          basePrice: 130.0,
          pricePerKm: 4.5,
          startLocation: const GeoPoint(21.4225, 39.8262), // Masjid al-Haram
          endLocation: const GeoPoint(21.4583, 39.8625), // Jabal al-Nour
          estimatedDistance: 7.0,
          estimatedDuration: 25,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
      ];
      
      // Add sample directions to Firestore
      for (final direction in sampleDirections) {
        await _firestore
            .collection('directions')
            .add(direction.toFirestore());
      }
      
      print('Successfully seeded ${sampleDirections.length} directions');
    } catch (e) {
      print('Error seeding directions: $e');
      throw e;
    }
  }
}
