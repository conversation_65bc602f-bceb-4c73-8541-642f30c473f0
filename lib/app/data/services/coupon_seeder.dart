import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/coupon_model.dart';

class CouponSeeder {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Future<void> seedCoupons() async {
    try {
      // Check if coupons already exist
      final couponsQuery = await _firestore.collection('coupons').limit(1).get();
      
      if (couponsQuery.docs.isNotEmpty) {
        print('Coupons already exist, skipping seeding');
        return;
      }
      
      print('No coupons found, creating sample coupons');
      
      // Sample coupons data
      final List<Coupon> sampleCoupons = [
        Coupon(
          id: '',
          code: 'WELCOME20',
          title: 'خصم 20% للمستخدمين الجدد',
          description: 'خصم 20% على أول رحلة للمستخدمين الجدد',
          discountType: DiscountType.percentage,
          discountValue: 20.0,
          minimumOrderAmount: 50.0,
          maximumDiscountAmount: 100.0,
          startDate: Timestamp.now(),
          endDate: Timestamp.fromDate(
            DateTime.now().add(const Duration(days: 90)),
          ),
          usageLimit: 1000,
          usageLimitPerUser: 1,
          usageCount: 0,
          isActive: true,
          userType: UserType.new_users,
          newUserDays: 7,
          createdAt: Timestamp.now(),
          createdBy: 'system',
        ),
        Coupon(
          id: '',
          code: 'FLAT50',
          title: 'خصم 50 ريال',
          description: 'خصم ثابت بقيمة 50 ريال على الرحلات',
          discountType: DiscountType.fixed,
          discountValue: 50.0,
          minimumOrderAmount: 100.0,
          startDate: Timestamp.now(),
          endDate: Timestamp.fromDate(
            DateTime.now().add(const Duration(days: 30)),
          ),
          usageLimit: 500,
          usageLimitPerUser: 2,
          usageCount: 0,
          isActive: true,
          userType: UserType.all,
          createdAt: Timestamp.now(),
          createdBy: 'system',
        ),
        Coupon(
          id: '',
          code: 'WEEKEND15',
          title: 'خصم 15% في نهاية الأسبوع',
          description: 'خصم 15% على جميع الرحلات في نهاية الأسبوع',
          discountType: DiscountType.percentage,
          discountValue: 15.0,
          startDate: Timestamp.now(),
          endDate: Timestamp.fromDate(
            DateTime.now().add(const Duration(days: 60)),
          ),
          usageLimit: null, // Unlimited usage
          usageLimitPerUser: 5,
          usageCount: 0,
          isActive: true,
          userType: UserType.all,
          createdAt: Timestamp.now(),
          createdBy: 'system',
        ),
        Coupon(
          id: '',
          code: 'LOYAL10',
          title: 'خصم 10% للمستخدمين الدائمين',
          description: 'خصم 10% للمستخدمين الذين مضى على تسجيلهم أكثر من 30 يوم',
          discountType: DiscountType.percentage,
          discountValue: 10.0,
          startDate: Timestamp.now(),
          endDate: null, // No expiration
          usageLimit: null, // Unlimited usage
          usageLimitPerUser: null, // Unlimited usage per user
          usageCount: 0,
          isActive: true,
          userType: UserType.existing_users,
          newUserDays: 30,
          createdAt: Timestamp.now(),
          createdBy: 'system',
        ),
      ];
      
      // Save coupons to Firestore
      for (final coupon in sampleCoupons) {
        await _firestore.collection('coupons').add(coupon.toFirestore());
      }
      
      print('Successfully created ${sampleCoupons.length} sample coupons');
    } catch (e) {
      print('Error seeding coupons: $e');
      throw e;
    }
  }
}
