import 'package:get/get.dart';
import '../modules/auth/bindings/auth_binding.dart';
import '../modules/auth/views/login_view.dart';
import '../modules/auth/views/phone_login_view.dart';
import '../modules/auth/views/signup_view.dart';
import '../modules/driver_home/bindings/driver_home_binding.dart';
import '../modules/driver_home/views/driver_chat_view.dart';
import '../modules/driver_home/views/driver_home_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_binding_new.dart';
import '../modules/home/<USER>/chat_view.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/home/<USER>/home_view_new.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/profile/bindings/edit_profile_binding.dart';
import '../modules/profile/views/edit_profile_view.dart';
import '../modules/wallet/bindings/wallet_binding.dart';
import '../modules/wallet/views/wallet_view.dart';
import '../modules/add_funds/bindings/add_funds_binding.dart';
import '../modules/add_funds/views/add_funds_view.dart';
import '../modules/support_chat/bindings/support_chat_binding.dart';
import '../modules/support_chat/views/modern_support_chat_view.dart';
import '../modules/faq/bindings/faq_binding.dart';
import '../modules/faq/views/modern_faq_view.dart';
import '../modules/help_center/bindings/help_center_binding.dart';
import '../modules/help_center/views/modern_help_center_view.dart';
import '../modules/user_trips/bindings/user_trips_binding.dart';
import '../modules/user_trips/views/user_trips_view.dart';
import '../modules/trip_cancellation/bindings/trip_cancellation_binding.dart';
import '../modules/trip_cancellation/views/trip_cancellation_view.dart';
import '../modules/admin/bindings/admin_dashboard_binding.dart';
import '../modules/admin/views/admin_dashboard_view.dart';
import '../modules/admin/bindings/admin_users_binding.dart';
import '../modules/admin/views/admin_users_view.dart';
import '../modules/admin/bindings/admin_trips_binding.dart';
import '../modules/admin/views/admin_trips_view.dart';
import '../modules/admin/bindings/admin_payments_binding.dart';
import '../modules/admin/views/admin_payments_view.dart';
import '../modules/admin/bindings/admin_faq_binding.dart';
import '../modules/admin/views/admin_faq_view.dart';
import '../modules/admin/bindings/admin_support_binding.dart';
import '../modules/admin/views/admin_support_view.dart';
import '../modules/admin/bindings/admin_drivers_map_binding.dart';
import '../modules/admin/views/admin_drivers_map_view.dart';
import '../modules/admin/bindings/admin_directions_binding.dart';
import '../modules/admin/views/admin_directions_view.dart';
import '../modules/admin/bindings/admin_coupons_binding.dart';
import '../modules/admin/views/admin_coupons_view.dart';
import '../modules/admin/bindings/admin_app_settings_binding.dart';
import '../modules/admin/views/admin_app_settings_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';
import '../modules/payment/bindings/payment_binding.dart';
import '../modules/payment/views/payment_view.dart';
import '../modules/support/bindings/support_dashboard_binding.dart';
import '../modules/support/views/support_dashboard_view.dart';
import '../modules/support/bindings/support_ticket_detail_binding.dart';
import '../modules/support/views/support_ticket_detail_view.dart';
import '../modules/status/bindings/user_status_binding.dart';
import '../modules/status/views/blocked_user_view.dart';
import '../modules/status/views/suspended_user_view.dart';
import '../modules/status/views/under_review_view.dart';

part 'app_routes.dart';

class AppPages {
  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(), // Use the HomeBinding
      // Add middleware here later for auth checks if needed
    ),
    // GetPage(
    //   name: Routes.HOME_NEW,
    //   page: () => const HomeViewNew(),
    //   binding: HomeBindingNew(), // Use the new HomeBinding
    //   Add middleware here later for auth checks if needed
    // ),
    // --- Driver Home Module ---
    GetPage(
      name: Routes.DRIVER_HOME,
      page: () => const DriverHomeView(), // The new Driver view
      binding: DriverHomeBinding(), // The new Driver binding
    ),
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginView(),
      binding: AuthBinding(), // AuthBinding manages AuthController
    ),
    GetPage(
      name: Routes.SIGNUP,
      page: () => const SignupView(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft, // Example transition
    ),
    GetPage(
      name: Routes.PHONE_LOGIN, // Add route for phone login
      page: () => const PhoneLoginView(),
      binding: AuthBinding(), // Uses the same AuthController instance
      transition: Transition.downToUp, // Example transition
    ),
    GetPage(
      name: Routes.CHAT,
      page: () => const ChatView(),
      binding: HomeBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.DRIVER_CHAT,
      page: () => const DriverChatView(),
      binding: DriverHomeBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.EDIT_PROFILE,
      page: () => const EditProfileView(),
      binding: EditProfileBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.WALLET,
      page: () => const WalletView(),
      binding: WalletBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADD_FUNDS,
      page: () => const AddFundsView(),
      binding: AddFundsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.SUPPORT_CHAT,
      page: () => const ModernSupportChatView(),
      binding: SupportChatBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.FAQ,
      page: () => const ModernFAQView(),
      binding: FAQBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.HELP_CENTER,
      page: () => const ModernHelpCenterView(),
      binding: HelpCenterBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.USER_TRIPS,
      page: () => const UserTripsView(),
      binding: UserTripsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.CANCEL_TRIP,
      page: () => const TripCancellationView(),
      binding: TripCancellationBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_DASHBOARD,
      page: () => const AdminDashboardView(),
      binding: AdminDashboardBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_USERS,
      page: () => const AdminUsersView(),
      binding: AdminUsersBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_TRIPS,
      page: () => const AdminTripsView(),
      binding: AdminTripsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_PAYMENTS,
      page: () => const AdminPaymentsView(),
      binding: AdminPaymentsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_FAQ,
      page: () => const AdminFAQView(),
      binding: AdminFAQBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_SUPPORT,
      page: () => const AdminSupportView(),
      binding: AdminSupportBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_DRIVERS_MAP,
      page: () => const AdminDriversMapView(),
      binding: AdminDriversMapBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_DIRECTIONS,
      page: () => const AdminDirectionsView(),
      binding: AdminDirectionsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_COUPONS,
      page: () => const AdminCouponsView(),
      binding: AdminCouponsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.ADMIN_APP_SETTINGS,
      page: () => const AdminAppSettingsView(),
      binding: AdminAppSettingsBinding(),
      transition: Transition.rightToLeft,
    ),

    GetPage(
      name: Routes.PAYMENT,
      page: () => const PaymentView(),
      binding: PaymentBinding(),
      transition: Transition.rightToLeft,
    ),

    // Support Agent Routes
    GetPage(
      name: Routes.SUPPORT_DASHBOARD,
      page: () => const SupportDashboardView(),
      binding: SupportDashboardBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.SUPPORT_TICKET_DETAIL,
      page: () => const SupportTicketDetailView(),
      binding: SupportTicketDetailBinding(),
      transition: Transition.rightToLeft,
    ),

    // User Status Routes
    GetPage(
      name: Routes.BLOCKED_USER,
      page: () => const BlockedUserView(),
      binding: UserStatusBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.SUSPENDED_USER,
      page: () => const SuspendedUserView(),
      binding: UserStatusBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.UNDER_REVIEW,
      page: () => const UnderReviewView(),
      binding: UserStatusBinding(),
      transition: Transition.fadeIn,
    ),
  ];
}
