part of 'app_pages.dart';

abstract class Routes {
  static const HOME = '/home';
  static const HOME_NEW = '/home-new';
  static const DRIVER_HOME = '/driver-home';
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const INITIAL = '/splash';
  static const SPLASH = '/splash';
  static const PHONE_LOGIN = '/phone-login';
  static const CHAT = '/chat';
  static const DRIVER_CHAT = '/driver-chat';

  // Profile Routes
  static const PROFILE = '/profile';
  static const EDIT_PROFILE = '/edit-profile';
  static const TRIP_HISTORY = '/trip-history';

  // Wallet Routes
  static const WALLET = '/wallet';
  static const ADD_FUNDS = '/add-funds';

  // Support Routes
  static const FAQ = '/faq';
  static const SUPPORT_CHAT = '/support-chat';
  static const HELP_CENTER = '/help-center';

  // Settings Route
  static const SETTINGS = '/settings';

  // Rating Routes
  static const RATE_DRIVER = '/rate-driver';
  static const RATE_USER = '/rate-user';
  static const RATE_APP = '/rate-app';

  // Trip Cancellation Route
  static const CANCEL_TRIP = '/cancel-trip';

  // Admin Routes
  static const ADMIN_DASHBOARD = '/admin-dashboard';
  static const ADMIN_USERS = '/admin-users';
  static const ADMIN_DRIVERS = '/admin-drivers';
  static const ADMIN_TRIPS = '/admin-trips';
  static const ADMIN_PAYMENTS = '/admin-payments';
  static const ADMIN_SUPPORT = '/admin-support';
  static const ADMIN_FAQ = '/admin-faq';
  static const ADMIN_SETTINGS = '/admin-settings';
  static const ADMIN_ADD_USER = '/admin-add-user';
  static const ADMIN_EDIT_USER = '/admin-edit-user';
  static const ADMIN_ADD_DRIVER = '/admin-add-driver';
  static const ADMIN_REPORTS = '/admin-reports';
  static const ADMIN_DRIVERS_MAP = '/admin-drivers-map';
  static const ADMIN_DIRECTIONS = '/admin-directions';
  static const ADMIN_COUPONS = '/admin-coupons';
  static const ADMIN_APP_SETTINGS = '/admin-app-settings';

  // User Routes
  static const USER_TRIPS = '/user-trips';
  static const PAYMENT = '/payment';

  // Support Agent Routes
  static const SUPPORT_DASHBOARD = '/support-dashboard';
  static const SUPPORT_TICKET_DETAIL = '/support-ticket-detail';

  // User Status Routes
  static const BLOCKED_USER = '/blocked-user';
  static const SUSPENDED_USER = '/suspended-user';
  static const UNDER_REVIEW = '/under-review';
}
