class AppDimensions {
  // Spacing
  static const double spacing2 = 2.0;
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  static const double spacing56 = 56.0;
  static const double spacing64 = 64.0;
  
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;
  
  // Margin
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  static const double marginXXL = 48.0;
  
  // Border Radius
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 24.0;
  static const double radiusRound = 50.0;
  
  // Button Heights
  static const double buttonHeightS = 36.0;
  static const double buttonHeightM = 44.0;
  static const double buttonHeightL = 52.0;
  static const double buttonHeightXL = 60.0;
  
  // Icon Sizes
  static const double iconXS = 12.0;
  static const double iconS = 16.0;
  static const double iconM = 20.0;
  static const double iconL = 24.0;
  static const double iconXL = 32.0;
  static const double iconXXL = 48.0;
  
  // Avatar Sizes
  static const double avatarS = 32.0;
  static const double avatarM = 40.0;
  static const double avatarL = 56.0;
  static const double avatarXL = 80.0;
  static const double avatarXXL = 120.0;
  
  // Card Dimensions
  static const double cardElevation = 4.0;
  static const double cardRadius = 16.0;
  static const double cardPadding = 16.0;
  
  // Input Field Dimensions
  static const double inputHeight = 52.0;
  static const double inputRadius = 12.0;
  static const double inputPadding = 16.0;
  
  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 80.0;
  static const double bottomNavElevation = 8.0;
  
  // Drawer
  static const double drawerWidth = 280.0;
  
  // Dialog
  static const double dialogRadius = 20.0;
  static const double dialogPadding = 24.0;
  static const double dialogMaxWidth = 400.0;
  
  // Bottom Sheet
  static const double bottomSheetRadius = 20.0;
  static const double bottomSheetPadding = 24.0;
  
  // Divider
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 16.0;
  
  // Shadow
  static const double shadowBlurRadius = 10.0;
  static const double shadowSpreadRadius = 0.0;
  static const double shadowOffsetY = 4.0;
  
  // Animation Durations (in milliseconds)
  static const int animationFast = 200;
  static const int animationMedium = 300;
  static const int animationSlow = 500;
  
  // Screen Breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
  
  // Map
  static const double mapHeight = 300.0;
  static const double mapRadius = 16.0;
  
  // Trip Card
  static const double tripCardHeight = 120.0;
  static const double tripCardRadius = 16.0;
  
  // Payment Card
  static const double paymentCardHeight = 80.0;
  static const double paymentCardRadius = 12.0;
  
  // Rating
  static const double ratingStarSize = 24.0;
  static const double ratingSpacing = 4.0;
  
  // Progress Indicator
  static const double progressHeight = 4.0;
  static const double progressRadius = 2.0;
  
  // Floating Action Button
  static const double fabSize = 56.0;
  static const double fabMiniSize = 40.0;
  
  // Chip
  static const double chipHeight = 32.0;
  static const double chipRadius = 16.0;
  
  // Badge
  static const double badgeSize = 20.0;
  static const double badgeRadius = 10.0;
}
