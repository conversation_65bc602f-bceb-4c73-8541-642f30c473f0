import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpCenterController extends GetxController {
  void navigateToFAQ() {
    Get.toNamed(Routes.FAQ);
  }
  
  void navigateToSupportChat() {
    Get.toNamed(Routes.SUPPORT_CHAT);
  }
  
  Future<void> callSupportNumber() async {
    const phoneNumber = 'tel:920001234';
    if (await canLaunch(phoneNumber)) {
      await launch(phoneNumber);
    } else {
      Get.snackbar(
        'خطأ',
        'لا يمكن الاتصال بالرقم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  Future<void> callEmergencyNumber() async {
    const phoneNumber = 'tel:911';
    if (await canLaunch(phoneNumber)) {
      await launch(phoneNumber);
    } else {
      Get.snackbar(
        'خطأ',
        'لا يمكن الاتصال بالرقم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  Future<void> sendSupportEmail() async {
    const email = 'mailto:<EMAIL>?subject=طلب مساعدة';
    if (await canLaunch(email)) {
      await launch(email);
    } else {
      Get.snackbar(
        'خطأ',
        'لا يمكن فتح تطبيق البريد الإلكتروني',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  void showAccountIssuesHelp() {
    _showHelpDialog(
      title: 'مشكلات الحساب',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHelpItem(
            'لا يمكنني تسجيل الدخول',
            'تأكد من إدخال بريدك الإلكتروني وكلمة المرور بشكل صحيح. إذا نسيت كلمة المرور، استخدم خيار "نسيت كلمة المرور" لإعادة تعيينها.',
          ),
          _buildHelpItem(
            'أريد تغيير رقم هاتفي',
            'يمكنك تغيير رقم هاتفك من خلال الانتقال إلى "الملف الشخصي" ثم "تعديل الملف الشخصي".',
          ),
          _buildHelpItem(
            'أريد حذف حسابي',
            'لحذف حسابك، يرجى التواصل مع فريق الدعم من خلال الدردشة أو البريد الإلكتروني.',
          ),
          _buildHelpItem(
            'لا أتلقى رمز التحقق',
            'تأكد من إدخال رقم هاتفك بشكل صحيح. إذا استمرت المشكلة، حاول استخدام طريقة تسجيل دخول أخرى أو تواصل مع الدعم.',
          ),
        ],
      ),
    );
  }
  
  void showTripIssuesHelp() {
    _showHelpDialog(
      title: 'مشكلات الرحلات',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHelpItem(
            'كيف يمكنني إلغاء رحلة؟',
            'يمكنك إلغاء الرحلة من خلال النقر على زر "إلغاء الرحلة" في شاشة تتبع الرحلة. يرجى ملاحظة أنه قد يتم تطبيق رسوم إلغاء إذا تم الإلغاء بعد قبول السائق للرحلة.',
          ),
          _buildHelpItem(
            'السائق لم يصل',
            'إذا تأخر السائق بشكل كبير، يمكنك التواصل معه من خلال الاتصال أو الدردشة. إذا لم يستجب، يمكنك إلغاء الرحلة وطلب رحلة جديدة.',
          ),
          _buildHelpItem(
            'نسيت شيئًا في السيارة',
            'إذا نسيت شيئًا في سيارة السائق، يمكنك التواصل معنا من خلال قسم "الدعم" في التطبيق. سنساعدك في التواصل مع السائق لاسترداد أغراضك.',
          ),
          _buildHelpItem(
            'أريد تغيير وجهتي',
            'يمكنك تغيير وجهتك من خلال التواصل مع السائق مباشرة عبر الدردشة أو الاتصال. يرجى ملاحظة أن تغيير الوجهة قد يؤدي إلى تغيير سعر الرحلة.',
          ),
        ],
      ),
    );
  }
  
  void showPaymentIssuesHelp() {
    _showHelpDialog(
      title: 'مشكلات الدفع',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHelpItem(
            'تم خصم مبلغ خاطئ',
            'إذا تم خصم مبلغ غير صحيح، يرجى التواصل مع فريق الدعم وتزويدنا برقم الرحلة وتفاصيل المشكلة.',
          ),
          _buildHelpItem(
            'لا يمكنني إضافة بطاقة ائتمان',
            'تأكد من إدخال بيانات البطاقة بشكل صحيح. إذا استمرت المشكلة، قد تكون هناك مشكلة في البطاقة نفسها أو في بوابة الدفع.',
          ),
          _buildHelpItem(
            'كيف يمكنني الحصول على فاتورة؟',
            'يتم إرسال الفواتير تلقائيًا إلى بريدك الإلكتروني بعد كل رحلة. يمكنك أيضًا الاطلاع على جميع الفواتير من خلال قسم "سجل الرحلات" في التطبيق.',
          ),
          _buildHelpItem(
            'أريد استرداد المبلغ',
            'لطلب استرداد المبلغ، يرجى التواصل مع فريق الدعم وتزويدنا برقم الرحلة وسبب طلب الاسترداد.',
          ),
        ],
      ),
    );
  }
  
  void showAppIssuesHelp() {
    _showHelpDialog(
      title: 'مشكلات التطبيق',
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHelpItem(
            'التطبيق يتوقف فجأة',
            'حاول إعادة تشغيل التطبيق أو إعادة تشغيل هاتفك. إذا استمرت المشكلة، حاول إلغاء تثبيت التطبيق وإعادة تثبيته.',
          ),
          _buildHelpItem(
            'لا يمكنني تحديث التطبيق',
            'تأكد من وجود مساحة كافية على هاتفك وأن لديك اتصالًا جيدًا بالإنترنت. حاول أيضًا التحقق من متجر التطبيقات للتأكد من توفر التحديث.',
          ),
          _buildHelpItem(
            'الخريطة لا تعمل بشكل صحيح',
            'تأكد من تفعيل خدمات الموقع على هاتفك وأن التطبيق لديه إذن للوصول إلى موقعك. حاول أيضًا التحقق من اتصالك بالإنترنت.',
          ),
          _buildHelpItem(
            'الإشعارات لا تصل',
            'تأكد من تفعيل الإشعارات للتطبيق في إعدادات هاتفك. يمكنك أيضًا التحقق من إعدادات الإشعارات داخل التطبيق.',
          ),
        ],
      ),
    );
  }
  
  void _showHelpDialog({
    required String title,
    required Widget content,
  }) {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: content,
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                navigateToSupportChat();
              },
              child: const Text('تحدث مع الدعم'),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHelpItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(description),
          const Divider(height: 24),
        ],
      ),
    );
  }
}
