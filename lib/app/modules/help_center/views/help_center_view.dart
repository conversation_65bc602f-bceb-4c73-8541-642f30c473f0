import 'package:ai_delivery_app/app/modules/help_center/controllers/help_center_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HelpCenterView extends GetView<HelpCenterController> {
  const HelpCenterView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('مركز المساعدة'),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: 64,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'كيف يمكننا مساعدتك؟',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اختر من الخيارات أدناه للحصول على المساعدة',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Quick Help Options
              Text(
                'مساعدة سريعة',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  _buildQuickHelpCard(
                    context,
                    icon: Icons.question_answer,
                    title: 'الأسئلة الشائعة',
                    onTap: () => controller.navigateToFAQ(),
                  ),
                  const SizedBox(width: 16),
                  _buildQuickHelpCard(
                    context,
                    icon: Icons.support_agent,
                    title: 'تحدث مع الدعم',
                    onTap: () => controller.navigateToSupportChat(),
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Common Issues
              Text(
                'مشكلات شائعة',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              _buildIssueCard(
                context,
                icon: Icons.account_circle,
                title: 'مشكلات الحساب',
                description: 'تسجيل الدخول، تغيير كلمة المرور، تحديث المعلومات',
                onTap: () => controller.showAccountIssuesHelp(),
              ),
              
              _buildIssueCard(
                context,
                icon: Icons.directions_car,
                title: 'مشكلات الرحلات',
                description: 'إلغاء الرحلة، تغيير الوجهة، مشكلات مع السائق',
                onTap: () => controller.showTripIssuesHelp(),
              ),
              
              _buildIssueCard(
                context,
                icon: Icons.payment,
                title: 'مشكلات الدفع',
                description: 'طرق الدفع، الفواتير، استرداد المبالغ',
                onTap: () => controller.showPaymentIssuesHelp(),
              ),
              
              _buildIssueCard(
                context,
                icon: Icons.smartphone,
                title: 'مشكلات التطبيق',
                description: 'أخطاء، تحديثات، مشكلات تقنية',
                onTap: () => controller.showAppIssuesHelp(),
              ),
              
              const SizedBox(height: 32),
              
              // Contact Information
              Text(
                'معلومات الاتصال',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              _buildContactCard(
                context,
                icon: Icons.phone,
                title: 'اتصل بنا',
                value: '920001234',
                onTap: () => controller.callSupportNumber(),
              ),
              
              _buildContactCard(
                context,
                icon: Icons.email,
                title: 'البريد الإلكتروني',
                value: '<EMAIL>',
                onTap: () => controller.sendSupportEmail(),
              ),
              
              _buildContactCard(
                context,
                icon: Icons.access_time,
                title: 'ساعات العمل',
                value: 'الأحد - الخميس: 9 صباحًا - 5 مساءً\nالجمعة - السبت: 10 صباحًا - 2 ظهرًا',
                onTap: null,
              ),
              
              const SizedBox(height: 32),
              
              // Emergency Contact
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.emergency,
                      color: Colors.red,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'للحالات الطارئة',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'في حالة وجود طوارئ أثناء الرحلة، يرجى الاتصال بالرقم:',
                            style: theme.textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () => controller.callEmergencyNumber(),
                            child: Text(
                              '911',
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildQuickHelpCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: theme.colorScheme.primary,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildIssueCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildContactCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: TextStyle(
                        color: onTap != null ? theme.colorScheme.primary : Colors.grey.shade600,
                        fontWeight: onTap != null ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey.shade400,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
