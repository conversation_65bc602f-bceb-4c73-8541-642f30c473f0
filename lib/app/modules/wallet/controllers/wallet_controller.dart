import 'package:ai_delivery_app/app/data/models/wallet_transaction_model.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class WalletController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final RxDouble walletBalance = 0.0.obs;
  final RxList<WalletTransaction> transactions = <WalletTransaction>[].obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  final Rx<TransactionType> currentFilter = TransactionType.all.obs;
  
  User? get currentUser => _auth.currentUser;
  
  @override
  void onInit() {
    super.onInit();
    loadWalletData();
  }
  
  Future<void> loadWalletData() async {
    if (currentUser == null) {
      errorMessage.value = 'User not logged in';
      return;
    }
    
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Load user wallet balance
      final userDoc = await _firestore.collection('users').doc(currentUser!.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        walletBalance.value = (userData['walletBalance'] ?? 0.0).toDouble();
      }
      
      // Load wallet transactions
      await _loadTransactions();
    } catch (e) {
      errorMessage.value = 'Error loading wallet data: $e';
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> _loadTransactions() async {
    try {
      Query query = _firestore
          .collection('walletTransactions')
          .where('userId', isEqualTo: currentUser!.uid)
          .orderBy('timestamp', descending: true);
      
      // Apply filter if not showing all
      if (currentFilter.value != TransactionType.all) {
        query = query.where('type', 
            isEqualTo: WalletTransaction.typeToString(currentFilter.value));
      }
      
      final transactionsSnapshot = await query.get();
      
      transactions.value = transactionsSnapshot.docs
          .map((doc) => WalletTransaction.fromFirestore(doc))
          .toList();
    } catch (e) {
      errorMessage.value = 'Error loading transactions: $e';
    }
  }
  
  void navigateToAddFunds() {
    Get.toNamed(Routes.ADD_FUNDS);
  }
  
  void showWithdrawDialog() {
    final TextEditingController amountController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('سحب الرصيد'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('أدخل المبلغ الذي ترغب في سحبه:'),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'المبلغ (ر.س)',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                final amount = double.tryParse(amountController.text);
                if (amount == null || amount <= 0) {
                  Get.snackbar(
                    'خطأ',
                    'يرجى إدخال مبلغ صحيح',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }
                
                if (amount > walletBalance.value) {
                  Get.snackbar(
                    'خطأ',
                    'المبلغ المطلوب أكبر من رصيدك الحالي',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }
                
                Get.back();
                _processWithdrawal(amount);
              },
              child: const Text('سحب'),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> _processWithdrawal(double amount) async {
    isLoading.value = true;
    
    try {
      // Create a new transaction
      final transaction = WalletTransaction(
        id: '', // Will be set by Firestore
        userId: currentUser!.uid,
        amount: amount,
        type: TransactionType.withdrawal,
        description: 'سحب رصيد',
        timestamp: Timestamp.now(),
        status: 'pending',
      );
      
      // Add transaction to Firestore
      final docRef = await _firestore.collection('walletTransactions').add(
        transaction.toFirestore(),
      );
      
      // Update user's wallet balance
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'walletBalance': FieldValue.increment(-amount),
      });
      
      // Update local state
      walletBalance.value -= amount;
      
      // Add the new transaction to the list
      final newTransaction = transaction.copyWith(
        id: docRef.id,
      );
      transactions.insert(0, newTransaction);
      
      Get.snackbar(
        'تم بنجاح',
        'تم تقديم طلب السحب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء معالجة طلب السحب: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void showFilterOptions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تصفية المعاملات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildFilterOption(TransactionType.all, 'الكل'),
              _buildFilterOption(TransactionType.deposit, 'إيداع'),
              _buildFilterOption(TransactionType.withdrawal, 'سحب'),
              _buildFilterOption(TransactionType.tripPayment, 'دفع رحلة'),
              _buildFilterOption(TransactionType.refund, 'استرداد'),
              _buildFilterOption(TransactionType.bonus, 'مكافأة'),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildFilterOption(TransactionType type, String label) {
    return InkWell(
      onTap: () {
        currentFilter.value = type;
        Get.back();
        _loadTransactions();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(
              currentFilter.value == type
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: currentFilter.value == type
                  ? Get.theme.colorScheme.primary
                  : Colors.grey,
            ),
            const SizedBox(width: 16),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: currentFilter.value == type
                    ? FontWeight.bold
                    : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  String getFilterName(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return 'إيداع';
      case TransactionType.withdrawal:
        return 'سحب';
      case TransactionType.tripPayment:
        return 'دفع رحلة';
      case TransactionType.refund:
        return 'استرداد';
      case TransactionType.bonus:
        return 'مكافأة';
      case TransactionType.other:
        return 'أخرى';
      case TransactionType.payment:
        return 'دفع';
      case TransactionType.earning:
        return 'دخل';
      case TransactionType.all:
        return 'الكل';
    }
  }
  
  void showTransactionDetails(WalletTransaction transaction) {
    final theme = Get.theme;
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('تفاصيل المعاملة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('النوع'),
                trailing: Text(getFilterName(transaction.type)),
              ),
              ListTile(
                title: const Text('المبلغ'),
                trailing: Text(
                  '${transaction.amount.toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    color: transaction.type == TransactionType.deposit ||
                            transaction.type == TransactionType.refund ||
                            transaction.type == TransactionType.bonus
                        ? Colors.green
                        : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ListTile(
                title: const Text('الوصف'),
                trailing: Text(transaction.description),
              ),
              ListTile(
                title: const Text('التاريخ'),
                trailing: Text(
                  intl.DateFormat('yyyy/MM/dd HH:mm').format(
                    transaction.timestamp.toDate(),
                  ),
                ),
              ),
              ListTile(
                title: const Text('الحالة'),
                trailing: Text(
                  _getStatusText(transaction.status),
                  style: TextStyle(
                    color: _getStatusColor(transaction.status, theme),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      ),
    );
  }
  
  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'قيد المعالجة';
      case 'failed':
        return 'فشلت';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }
  
  Color _getStatusColor(String status, ThemeData theme) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return theme.colorScheme.primary;
    }
  }
}
