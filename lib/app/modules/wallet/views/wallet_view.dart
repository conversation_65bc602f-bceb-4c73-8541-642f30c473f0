import 'package:ai_delivery_app/app/data/models/wallet_transaction_model.dart';
import 'package:ai_delivery_app/app/modules/wallet/controllers/wallet_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class WalletView extends GetView<WalletController> {
  const WalletView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المحفظة'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.add_card),
              tooltip: 'إضافة رصيد',
              onPressed: () => controller.navigateToAddFunds(),
            ),
          ],
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage.value != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline,
                      size: 48, color: theme.colorScheme.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value!,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.loadWalletData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: controller.loadWalletData,
            child: CustomScrollView(
              slivers: [
                // Wallet Balance Card
                SliverToBoxAdapter(
                  child: _buildWalletBalanceCard(context),
                ),

                // Transactions Header
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'المعاملات',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        // Filter Button
                        TextButton.icon(
                          onPressed: () => controller.showFilterOptions(),
                          icon: const Icon(Icons.filter_list),
                          label: Text(
                            controller.currentFilter.value ==
                                    TransactionType.all
                                ? 'الكل'
                                : controller.getFilterName(
                                    controller.currentFilter.value),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Transactions List
                if (controller.transactions.isEmpty)
                  SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.account_balance_wallet_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد معاملات',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final transaction = controller.transactions[index];

                        // Add date header if this is the first transaction or if the date changed
                        final bool showDateHeader = index == 0 ||
                            !_isSameDay(
                              controller.transactions[index - 1].timestamp
                                  .toDate(),
                              transaction.timestamp.toDate(),
                            );

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (showDateHeader)
                              Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(16, 16, 16, 8),
                                child: Text(
                                  _formatDateHeader(
                                      transaction.timestamp.toDate()),
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color: Colors.grey.shade600,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            _buildTransactionItem(context, transaction),
                          ],
                        );
                      },
                      childCount: controller.transactions.length,
                    ),
                  ),
              ],
            ),
          );
        }),
        floatingActionButton: FloatingActionButton(
          onPressed: () => controller.navigateToAddFunds(),
          backgroundColor: theme.colorScheme.primary,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildWalletBalanceCard(BuildContext context) {
    final theme = Theme.of(context);
    final walletBalance = controller.walletBalance.value;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'رصيد المحفظة',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              Icon(
                Icons.account_balance_wallet,
                color: Colors.white.withOpacity(0.9),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            '${walletBalance.toStringAsFixed(2)} ر.س',
            style: theme.textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              OutlinedButton.icon(
                onPressed: () => controller.navigateToAddFunds(),
                icon: const Icon(Icons.add, size: 16),
                label: const Text('إضافة رصيد'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Colors.white),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
              OutlinedButton.icon(
                onPressed: () => controller.showWithdrawDialog(),
                icon: const Icon(Icons.arrow_upward, size: 16),
                label: const Text('سحب'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Colors.white),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(
      BuildContext context, WalletTransaction transaction) {
    final theme = Theme.of(context);
    final isPositive = transaction.type == TransactionType.deposit ||
        transaction.type == TransactionType.refund ||
        transaction.type == TransactionType.bonus;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: isPositive
                ? Colors.green.withOpacity(0.1)
                : Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            _getTransactionIcon(transaction.type),
            color: isPositive ? Colors.green : Colors.red,
          ),
        ),
        title: Text(
          transaction.description,
          style: theme.textTheme.titleMedium,
        ),
        subtitle: Text(
          _formatTime(transaction.timestamp.toDate()),
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
        trailing: Text(
          '${isPositive ? '+' : '-'} ${transaction.amount.toStringAsFixed(2)} ر.س',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isPositive ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        onTap: () => controller.showTransactionDetails(transaction),
      ),
    );
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return Icons.add_circle_outline;
      case TransactionType.withdrawal:
        return Icons.remove_circle_outline;
      case TransactionType.tripPayment:
        return Icons.directions_car;
      case TransactionType.refund:
        return Icons.replay;
      case TransactionType.bonus:
        return Icons.card_giftcard;
      case TransactionType.payment:
        return Icons.payment;
      case TransactionType.earning:
        return Icons.account_balance_wallet;
      case TransactionType.other:
        return Icons.swap_horiz;
      case TransactionType.all:
        return Icons.all_inclusive;
    }
  }

  String _formatTime(DateTime date) {
    return intl.DateFormat('HH:mm').format(date);
  }

  String _formatDateHeader(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == DateTime(now.year, now.month, now.day)) {
      return 'اليوم';
    } else if (dateToCheck ==
        DateTime(yesterday.year, yesterday.month, yesterday.day)) {
      return 'الأمس';
    } else {
      return intl.DateFormat('dd/MM/yyyy').format(date);
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
