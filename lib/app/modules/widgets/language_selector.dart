import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../data/services/translation_service.dart';

class LanguageSelector extends StatelessWidget {
  final bool isDrawer;

  const LanguageSelector({Key? key, this.isDrawer = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isDrawer ? _buildDrawerSelector(context) : _buildPopupSelector(context);
  }

  Widget _buildDrawerSelector(BuildContext context) {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.language),
          title: Text('language'.tr),
          trailing: DropdownButton<String>(
            value: Get.locale?.languageCode,
            underline: const SizedBox(),
            onChanged: (String? newValue) {
              if (newValue == 'ar') {
                TranslationService.changeLocale('ar', 'SA');
              } else {
                TranslationService.changeLocale('en', 'US');
              }
            },
            items: [
              DropdownMenuItem(
                value: 'en',
                child: const Text('English'),
              ),
              DropdownMenuItem(
                value: 'ar',
                child: const Text('العربية'),
              ),
            ],
          ),
        ),
        const Divider(),
      ],
    );
  }

  Widget _buildPopupSelector(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.language),
      onSelected: (String value) {
        if (value == 'ar') {
          TranslationService.changeLocale('ar', 'SA');
        } else {
          TranslationService.changeLocale('en', 'US');
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        const PopupMenuItem<String>(
          value: 'en',
          child: Text('English'),
        ),
        const PopupMenuItem<String>(
          value: 'ar',
          child: Text('العربية'),
        ),
      ],
    );
  }
}
