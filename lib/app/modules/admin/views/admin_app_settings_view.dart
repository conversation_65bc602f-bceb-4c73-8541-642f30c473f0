import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/admin_app_settings_controller.dart';

class AdminAppSettingsView extends GetView<AdminAppSettingsController> {
  const AdminAppSettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return DefaultTabController(
      length: 6, // Number of tabs
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إعدادات التطبيق'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: controller.loadAppSettings,
              tooltip: 'تحديث',
            ),
          ],
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage.value != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline,
                      size: 48, color: Colors.red.shade300),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value!,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: controller.loadAppSettings,
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (controller.appSettings.value == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.settings, size: 48, color: Colors.grey.shade400),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد إعدادات للتطبيق',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: controller.loadAppSettings,
                    icon: const Icon(Icons.refresh),
                    label: const Text('تحميل الإعدادات'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Tabs
              Container(
                color: theme.colorScheme.surface,
                child: TabBar(
                  isScrollable: true,
                  onTap: controller.setActiveTab,
                  tabs: const [
                    Tab(text: 'إصدار التطبيق'),
                    Tab(text: 'حالة التطبيق'),
                    Tab(text: 'الإعدادات المالية'),
                    Tab(text: 'إعدادات الدولة'),
                    Tab(text: 'معلومات الاتصال'),
                    Tab(text: 'الشروط والخصوصية'),
                  ],
                ),
              ),

              // Tab content
              Expanded(
                child: Obx(() {
                  final tabIndex = controller.activeTabIndex.value;

                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: [
                      _buildAppVersionSettings(context),
                      _buildAppStatusSettings(context),
                      _buildFinancialSettings(context),
                      _buildCountrySettings(context),
                      _buildContactSettings(context),
                      _buildTermsPrivacySettings(context),
                    ][tabIndex],
                  );
                }),
              ),
            ],
          );
        }),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton(
            onPressed: controller.saveAppSettings,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
            ),
            child: Obx(() => controller.isLoading.value
                ? const CircularProgressIndicator(color: Colors.white)
                : const Text('حفظ الإعدادات')),
          ),
        ),
      ),
    );
  }

  Widget _buildAppVersionSettings(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات إصدار التطبيق',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 24),

        // Current Version
        TextFormField(
          controller: controller.currentVersionController,
          decoration: const InputDecoration(
            labelText: 'الإصدار الحالي *',
            hintText: 'مثال: 1.0.0',
            border: OutlineInputBorder(),
            helperText: 'إصدار التطبيق الحالي (x.y.z)',
          ),
        ),
        const SizedBox(height: 16),

        // Minimum Required Version
        TextFormField(
          controller: controller.minRequiredVersionController,
          decoration: const InputDecoration(
            labelText: 'الحد الأدنى للإصدار المطلوب *',
            hintText: 'مثال: 1.0.0',
            border: OutlineInputBorder(),
            helperText: 'أقل إصدار مسموح به للتطبيق',
          ),
        ),
        const SizedBox(height: 16),

        // Update Required
        Obx(() => SwitchListTile(
              title: const Text('التحديث مطلوب'),
              subtitle:
                  const Text('إجبار المستخدمين على التحديث إلى أحدث إصدار'),
              value: controller.updateRequiredRx.value,
              onChanged: (value) => controller.updateRequiredRx.value = value,
              activeColor: theme.colorScheme.primary,
            )),
        const SizedBox(height: 16),

        // Update URL
        TextFormField(
          controller: controller.updateUrlController,
          decoration: const InputDecoration(
            labelText: 'رابط التحديث',
            hintText:
                'مثال: https://play.google.com/store/apps/details?id=com.example.app',
            border: OutlineInputBorder(),
            helperText: 'رابط تحميل أحدث إصدار من التطبيق',
          ),
        ),
        const SizedBox(height: 16),

        // Update Message
        TextFormField(
          controller: controller.updateMessageController,
          decoration: const InputDecoration(
            labelText: 'رسالة التحديث',
            hintText:
                'مثال: يرجى تحديث التطبيق للحصول على أحدث الميزات وإصلاحات الأخطاء',
            border: OutlineInputBorder(),
            helperText: 'الرسالة التي ستظهر للمستخدمين عند طلب التحديث',
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildAppStatusSettings(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات حالة التطبيق',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 24),

        // Maintenance Mode
        Obx(() => SwitchListTile(
              title: const Text('وضع الصيانة'),
              subtitle: const Text('تعطيل الوصول إلى التطبيق أثناء الصيانة'),
              value: controller.maintenanceModeRx.value,
              onChanged: (value) => controller.maintenanceModeRx.value = value,
              activeColor: theme.colorScheme.primary,
            )),
        const SizedBox(height: 16),

        // Maintenance Message
        TextFormField(
          controller: controller.maintenanceMessageController,
          decoration: const InputDecoration(
            labelText: 'رسالة الصيانة',
            hintText: 'مثال: التطبيق قيد الصيانة حالياً. يرجى المحاولة لاحقاً.',
            border: OutlineInputBorder(),
            helperText: 'الرسالة التي ستظهر للمستخدمين أثناء الصيانة',
          ),
          maxLines: 3,
        ),
        const SizedBox(height: 16),

        // Maintenance End Time
        ListTile(
          title: const Text('وقت انتهاء الصيانة'),
          subtitle: Obx(() => Text(
                controller.maintenanceEndTimeRx.value != null
                    ? controller
                        .formatDateTime(controller.maintenanceEndTimeRx.value!)
                    : 'غير محدد',
              )),
          trailing: IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () async {
              // Store context for later use
              final BuildContext currentContext = context;

              final DateTime? pickedDate = await showDatePicker(
                context: currentContext,
                initialDate: controller.maintenanceEndTimeRx.value ??
                    DateTime.now().add(const Duration(days: 1)),
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 30)),
              );

              // Check if widget is still mounted before proceeding
              if (pickedDate != null && currentContext.mounted) {
                final TimeOfDay? pickedTime = await showTimePicker(
                  context: currentContext,
                  initialTime: TimeOfDay.fromDateTime(
                    controller.maintenanceEndTimeRx.value ??
                        DateTime.now().add(const Duration(hours: 1)),
                  ),
                );

                if (pickedTime != null) {
                  final DateTime combinedDateTime = DateTime(
                    pickedDate.year,
                    pickedDate.month,
                    pickedDate.day,
                    pickedTime.hour,
                    pickedTime.minute,
                  );

                  controller.setMaintenanceEndTime(combinedDateTime);
                }
              }
            },
          ),
        ),

        // Clear Maintenance End Time
        if (controller.maintenanceEndTimeRx.value != null)
          TextButton.icon(
            onPressed: () => controller.setMaintenanceEndTime(null),
            icon: const Icon(Icons.clear),
            label: const Text('مسح وقت انتهاء الصيانة'),
          ),
      ],
    );
  }

  Widget _buildFinancialSettings(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإعدادات المالية',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 24),

        // App Fee Percentage
        TextFormField(
          controller: controller.appFeePercentageController,
          decoration: const InputDecoration(
            labelText: 'نسبة رسوم التطبيق (%) *',
            hintText: 'مثال: 10',
            border: OutlineInputBorder(),
            helperText: 'النسبة المئوية التي يأخذها التطبيق من كل رحلة',
            suffixText: '%',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        const SizedBox(height: 16),

        // Tax Percentage
        TextFormField(
          controller: controller.taxPercentageController,
          decoration: const InputDecoration(
            labelText: 'نسبة الضريبة (%) *',
            hintText: 'مثال: 15',
            border: OutlineInputBorder(),
            helperText: 'نسبة ضريبة القيمة المضافة',
            suffixText: '%',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
        ),
        const SizedBox(height: 16),

        // Currency Code
        TextFormField(
          controller: controller.currencyCodeController,
          decoration: const InputDecoration(
            labelText: 'رمز العملة *',
            hintText: 'مثال: SAR',
            border: OutlineInputBorder(),
            helperText: 'رمز العملة المستخدمة في التطبيق (ISO 4217)',
          ),
        ),
        const SizedBox(height: 16),

        // Currency Symbol
        TextFormField(
          controller: controller.currencySymbolController,
          decoration: const InputDecoration(
            labelText: 'رمز العملة *',
            hintText: 'مثال: ر.س',
            border: OutlineInputBorder(),
            helperText: 'رمز العملة الذي سيظهر في التطبيق',
          ),
        ),
      ],
    );
  }

  Widget _buildCountrySettings(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات الدولة',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 24),

        // Default Country Code
        TextFormField(
          controller: controller.defaultCountryCodeController,
          decoration: const InputDecoration(
            labelText: 'رمز الدولة الافتراضي *',
            hintText: 'مثال: SA',
            border: OutlineInputBorder(),
            helperText: 'رمز الدولة الافتراضي (ISO 3166-1 alpha-2)',
          ),
        ),
        const SizedBox(height: 16),

        // Supported Countries
        TextFormField(
          controller: controller.supportedCountriesController,
          decoration: const InputDecoration(
            labelText: 'الدول المدعومة *',
            hintText: 'مثال: SA, AE, KW',
            border: OutlineInputBorder(),
            helperText: 'قائمة رموز الدول المدعومة مفصولة بفواصل',
          ),
        ),
      ],
    );
  }

  Widget _buildContactSettings(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الاتصال',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 24),

        // Support Email
        TextFormField(
          controller: controller.supportEmailController,
          decoration: const InputDecoration(
            labelText: 'البريد الإلكتروني للدعم *',
            hintText: 'مثال: <EMAIL>',
            border: OutlineInputBorder(),
            helperText: 'البريد الإلكتروني للدعم الفني',
          ),
          keyboardType: TextInputType.emailAddress,
        ),
        const SizedBox(height: 16),

        // Support Phone
        TextFormField(
          controller: controller.supportPhoneController,
          decoration: const InputDecoration(
            labelText: 'رقم هاتف الدعم *',
            hintText: 'مثال: +966123456789',
            border: OutlineInputBorder(),
            helperText: 'رقم هاتف الدعم الفني',
          ),
          keyboardType: TextInputType.phone,
        ),
        const SizedBox(height: 16),

        // Support WhatsApp
        TextFormField(
          controller: controller.supportWhatsappController,
          decoration: const InputDecoration(
            labelText: 'رقم واتساب الدعم *',
            hintText: 'مثال: +966123456789',
            border: OutlineInputBorder(),
            helperText: 'رقم واتساب الدعم الفني',
          ),
          keyboardType: TextInputType.phone,
        ),
      ],
    );
  }

  Widget _buildTermsPrivacySettings(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الشروط والخصوصية',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 24),

        // Terms URL
        TextFormField(
          controller: controller.termsUrlController,
          decoration: const InputDecoration(
            labelText: 'رابط شروط الاستخدام *',
            hintText: 'مثال: https://example.com/terms',
            border: OutlineInputBorder(),
            helperText: 'رابط صفحة شروط الاستخدام',
          ),
          keyboardType: TextInputType.url,
        ),
        const SizedBox(height: 16),

        // Privacy URL
        TextFormField(
          controller: controller.privacyUrlController,
          decoration: const InputDecoration(
            labelText: 'رابط سياسة الخصوصية *',
            hintText: 'مثال: https://example.com/privacy',
            border: OutlineInputBorder(),
            helperText: 'رابط صفحة سياسة الخصوصية',
          ),
          keyboardType: TextInputType.url,
        ),
      ],
    );
  }
}
