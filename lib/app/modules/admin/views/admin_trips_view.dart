import 'package:ai_delivery_app/app/modules/admin/controllers/admin_trips_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminTripsView extends GetView<AdminTripsController> {
  const AdminTripsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الرحلات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => controller.showSearchDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => controller.showFilterOptions(),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: controller.refreshData,
        child: Obx(
          () => controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    _buildStatsCards(context),
                    Expanded(
                      child: controller.trips.isEmpty
                          ? Center(
                              child: Text(
                                'لا توجد رحلات متاحة',
                                style: theme.textTheme.titleMedium,
                              ),
                            )
                          : ListView.builder(
                              itemCount: controller.trips.length,
                              itemBuilder: (context, index) {
                                final trip = controller.trips[index];
                                return _buildTripItem(context, trip);
                              },
                            ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context,
              title: 'الكل',
              value: controller.totalTrips.value.toString(),
              color: Colors.blue,
              isSelected: controller.filterOption.value == 'all',
              onTap: () => controller.setFilter('all'),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              context,
              title: 'مكتملة',
              value: controller.completedTrips.value.toString(),
              color: Colors.green,
              isSelected: controller.filterOption.value == 'completed',
              onTap: () => controller.setFilter('completed'),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              context,
              title: 'قيد التنفيذ',
              value: controller.pendingTrips.value.toString(),
              color: Colors.orange,
              isSelected: controller.filterOption.value == 'pending',
              onTap: () => controller.setFilter('pending'),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              context,
              title: 'ملغاة',
              value: controller.cancelledTrips.value.toString(),
              color: Colors.red,
              isSelected: controller.filterOption.value == 'cancelled',
              onTap: () => controller.setFilter('cancelled'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isSelected ? color : Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? color : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripItem(BuildContext context, Map<String, dynamic> trip) {
    final theme = Theme.of(context);
    final status = trip['status'] as String;
    final statusColor = _getStatusColor(status);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        title: Text(
          'من ${trip['origin']} إلى ${trip['destination']}',
          style: theme.textTheme.titleMedium,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 4),
                Text('${trip['date']} - ${trip['time']}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.attach_money, size: 16),
                const SizedBox(width: 4),
                Text('${trip['amount']} ر.س'),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(status),
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        onTap: () => controller.showTripDetails(trip['id']),
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'قيد الانتظار';
      case 'assigned':
        return 'تم تعيين سائق';
      case 'accepted':
        return 'تم قبول الرحلة';
      case 'started':
        return 'بدأت الرحلة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      case 'assigned':
      case 'accepted':
      case 'started':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
