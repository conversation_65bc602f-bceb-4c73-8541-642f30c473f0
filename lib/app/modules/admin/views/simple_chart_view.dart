import 'package:ai_delivery_app/app/modules/admin/controllers/admin_dashboard_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SimpleRevenueChart extends StatelessWidget {
  final AdminDashboardController controller;

  const SimpleRevenueChart({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإيرادات (آخر 7 أيام)',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              DropdownButton<String>(
                value: controller.selectedChartPeriod.value,
                items: const [
                  DropdownMenuItem(
                    value: 'week',
                    child: Text('أسبوع'),
                  ),
                  DropdownMenuItem(
                    value: 'month',
                    child: Text('شهر'),
                  ),
                  DropdownMenuItem(
                    value: 'year',
                    child: Text('سنة'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedChartPeriod.value = value;
                    controller.updateChartData();
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: Row(
              children: [
                // Y-axis labels
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      controller.chartMaxY.value.toStringAsFixed(0),
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    Text(
                      (controller.chartMaxY.value / 2).toStringAsFixed(0),
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const Text(
                      '0',
                      style: TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(width: 8),
                // Chart bars
                Expanded(
                  child: Column(
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: List.generate(
                            controller.chartData.length,
                            (index) {
                              final data = controller.chartData[index];
                              final height = (data.y / controller.chartMaxY.value) * 100;
                              
                              return Tooltip(
                                message: '${controller.chartLabels[index]}: ${data.y.toStringAsFixed(2)} ر.س',
                                child: Container(
                                  width: 20,
                                  height: height.clamp(5.0, 150.0),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary,
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(4),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // X-axis labels
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: controller.chartLabels
                            .map(
                              (label) => Text(
                                label,
                                style: const TextStyle(color: Colors.grey, fontSize: 12),
                              ),
                            )
                            .toList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
