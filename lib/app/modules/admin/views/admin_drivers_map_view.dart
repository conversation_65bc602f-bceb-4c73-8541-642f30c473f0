import 'package:ai_delivery_app/app/modules/admin/controllers/admin_drivers_map_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AdminDriversMapView extends GetView<AdminDriversMapController> {
  const AdminDriversMapView({super.key});

  Widget _buildSearchField() {
    return TextField(
      controller: controller.searchController,
      autofocus: true,
      decoration: InputDecoration(
        hintText: 'ابحث عن اسم السائق أو رقم الهاتف',
        border: InputBorder.none,
        hintStyle: const TextStyle(color: Colors.white70),
      ),
      style: const TextStyle(color: Colors.white, fontSize: 16),
      onChanged: controller.onSearchChanged,
    );
  }

  Color _getStatusColor(bool isOnline, bool isBusy) {
    if (isBusy) {
      return Colors.orange;
    } else if (isOnline) {
      return Colors.green;
    } else {
      return Colors.red;
    }
  }

  String _getDriverStatusText(bool isOnline, bool isBusy) {
    if (isBusy) {
      return 'مشغول في رحلة';
    } else if (isOnline) {
      return 'متصل ومتاح';
    } else {
      return 'غير متصل';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Obx(() =>
        controller.isSearching.value
            ? _buildSearchField()
            : const Text('خريطة السائقين')),
        actions: [
          Obx(
                () =>
            controller.isSearching.value
                ? IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                controller.isSearching.value = false;
                controller.clearSearch();
              },
              tooltip: 'إلغاء البحث',
            )
                : IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => controller.isSearching.value = true,
              tooltip: 'بحث عن سائق',
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.startTrackingDrivers(),
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterOptions(context),
            tooltip: 'تصفية',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatisticsBar(context),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return Stack(
                children: [
                  Obx(() {
                    return GoogleMap(
                      initialCameraPosition:
                      controller.initialCameraPosition.value,
                      onMapCreated: controller.onMapCreated,
                      markers: controller.markers.value,
                      myLocationEnabled: true,
                      myLocationButtonEnabled: false,
                      zoomControlsEnabled: false,
                      mapToolbarEnabled: false,
                    );
                  }),
                  // Search results panel
                  Obx(() =>
                  controller.showSearchResults.value &&
                      controller.searchResults.isNotEmpty
                      ? Positioned(
                    top: 16,
                    left: 16,
                    right: 16,
                    child: Card(
                      child: Container(
                        constraints: BoxConstraints(
                          maxHeight: Get.height * 0.3,
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: controller.searchResults.length,
                          itemBuilder: (context, index) {
                            final driverId =
                            controller.searchResults[index];
                            final driver =
                            controller.driverLocations[driverId];
                            if (driver == null)
                              return const SizedBox.shrink();

                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getStatusColor(
                                    driver.isOnline, driver.isBusy),
                                child: const Icon(Icons.person,
                                    color: Colors.white),
                              ),
                              title: Text(driver.driverName),
                              subtitle: Text(_getDriverStatusText(
                                  driver.isOnline, driver.isBusy)),
                              trailing: IconButton(
                                icon: const Icon(Icons.location_on),
                                onPressed: () =>
                                    controller
                                        .centerMapOnDriver(driverId),
                              ),
                              onTap: () =>
                                  controller.showDriverDetails(driverId),
                            );
                          },
                        ),
                      ),
                    ),
                  )
                      : const SizedBox.shrink()),

                  // Map controls
                  Positioned(
                    right: 16,
                    bottom: 16,
                    child: Column(
                      children: [
                        FloatingActionButton(
                          heroTag: 'centerMap',
                          mini: true,
                          onPressed: controller.centerMapOnAllDrivers,
                          tooltip: 'عرض جميع السائقين',
                          backgroundColor: theme.colorScheme.primary,
                          child: const Icon(Icons.center_focus_strong),
                        ),
                        const SizedBox(height: 8),
                        FloatingActionButton(
                          heroTag: 'myLocation',
                          mini: true,
                          onPressed: () {
                            controller.mapController.value?.animateCamera(
                              CameraUpdate.newCameraPosition(
                                controller.initialCameraPosition.value,
                              ),
                            );
                          },
                          tooltip: 'موقعي',
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                          child: const Icon(Icons.my_location),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 16,
                    top: 16,
                    child: _buildLegend(context),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      color: Colors.white,
      child: Obx(() =>
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                context,
                label: 'إجمالي السائقين',
                value: controller.totalDrivers.value.toString(),
                color: Colors.blue,
              ),
              _buildStatItem(
                context,
                label: 'متصلين',
                value: controller.onlineDrivers.value.toString(),
                color: Colors.green,
              ),
              _buildStatItem(
                context,
                label: 'مشغولين',
                value: controller.busyDrivers.value.toString(),
                color: Colors.orange,
              ),
              _buildStatItem(
                context,
                label: 'غير متصلين',
                value: (controller.totalDrivers.value -
                    controller.onlineDrivers.value)
                    .toString(),
                color: Colors.red,
              ),
            ],
          )),
    );
  }

  Widget _buildStatItem(BuildContext context, {
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildLegend(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'مفتاح الخريطة',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            _buildLegendItem(
              context,
              color: Colors.green,
              label: 'متصل ومتاح',
            ),
            _buildLegendItem(
              context,
              color: Colors.orange,
              label: 'مشغول في رحلة',
            ),
            _buildLegendItem(
              context,
              color: Colors.red,
              label: 'غير متصل',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(BuildContext context, {
    required Color color,
    required String label,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تصفية السائقين',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Obx(() =>
                  SwitchListTile(
                    title: const Text('السائقين المتصلين'),
                    subtitle: const Text('عرض السائقين المتاحين'),
                    value: controller.showOnlineDrivers.value,
                    onChanged: controller.toggleOnlineDrivers,
                    activeColor: Colors.green,
                  )),
              Obx(() =>
                  SwitchListTile(
                    title: const Text('السائقين المشغولين'),
                    subtitle: const Text('عرض السائقين في رحلة'),
                    value: controller.showBusyDrivers.value,
                    onChanged: controller.toggleBusyDrivers,
                    activeColor: Colors.orange,
                  )),
              Obx(() =>
                  SwitchListTile(
                    title: const Text('السائقين غير المتصلين'),
                    subtitle: const Text('عرض السائقين غير المتاحين'),
                    value: controller.showOfflineDrivers.value,
                    onChanged: controller.toggleOfflineDrivers,
                    activeColor: Colors.red,
                  )),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Get.back();
                    controller.centerMapOnAllDrivers();
                  },
                  child: const Text('تطبيق وعرض الكل'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
