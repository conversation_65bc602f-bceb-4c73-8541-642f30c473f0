import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

import '../controllers/admin_directions_controller.dart';
import '../../../data/models/direction_model.dart';

class AdminDirectionsView extends GetView<AdminDirectionsController> {
  const AdminDirectionsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوجهات'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddEditDirectionDialog(context),
            tooltip: 'إضافة وجهة جديدة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadDirections,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (controller.errorMessage.value != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.red.shade300),
                const SizedBox(height: 16),
                Text(
                  controller.errorMessage.value!,
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: controller.loadDirections,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }
        
        if (controller.directions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.route, size: 48, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                Text(
                  'لا توجد وجهات مضافة',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'اضغط على زر الإضافة لإنشاء وجهة جديدة',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _showAddEditDirectionDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة وجهة'),
                ),
              ],
            ),
          );
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.directions.length,
          itemBuilder: (context, index) {
            final direction = controller.directions[index];
            return _buildDirectionCard(context, direction);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddEditDirectionDialog(context),
        tooltip: 'إضافة وجهة جديدة',
        child: const Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildDirectionCard(BuildContext context, Direction direction) {
    final theme = Theme.of(context);
    final formatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س ',
      decimalDigits: 2,
    );
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: direction.isActive 
              ? Colors.green.withOpacity(0.3) 
              : Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with title and status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    direction.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: direction.isActive 
                        ? Colors.green.withOpacity(0.1) 
                        : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    direction.isActive ? 'نشط' : 'غير نشط',
                    style: TextStyle(
                      color: direction.isActive ? Colors.green : Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Direction details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (direction.description.isNotEmpty) ...[
                  Text(
                    direction.description,
                    style: theme.textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                ],
                
                // Price information
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'السعر الأساسي',
                        formatter.format(direction.basePrice),
                        Icons.money,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'السعر لكل كم',
                        formatter.format(direction.pricePerKm),
                        Icons.route,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Distance and duration
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'المسافة التقديرية',
                        '${direction.estimatedDistance.toStringAsFixed(1)} كم',
                        Icons.straighten,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'الوقت التقديري',
                        '${direction.estimatedDuration} دقيقة',
                        Icons.timer,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Estimated total price
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.price_check, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'السعر التقديري: ${formatter.format(direction.estimatedPrice)}',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showAddEditDirectionDialog(
                    context, 
                    direction: direction,
                  ),
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _toggleDirectionStatus(direction),
                  icon: Icon(
                    direction.isActive ? Icons.unpublished : Icons.check_circle,
                  ),
                  label: Text(direction.isActive ? 'تعطيل' : 'تفعيل'),
                  style: TextButton.styleFrom(
                    foregroundColor: direction.isActive ? Colors.red : Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _confirmDeleteDirection(context, direction),
                  icon: const Icon(Icons.delete),
                  label: const Text('حذف'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoItem(
    BuildContext context, 
    String label, 
    String value, 
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  void _toggleDirectionStatus(Direction direction) {
    final newStatus = !direction.isActive;
    final message = newStatus 
        ? 'تم تفعيل الوجهة بنجاح' 
        : 'تم تعطيل الوجهة بنجاح';
    
    controller.updateDirectionStatus(direction.id, newStatus).then((_) {
      Get.snackbar(
        'تم بنجاح',
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }).catchError((error) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة الوجهة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    });
  }
  
  void _confirmDeleteDirection(BuildContext context, Direction direction) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الوجهة "${direction.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteDirection(direction.id).then((_) {
                Get.snackbar(
                  'تم بنجاح',
                  'تم حذف الوجهة بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              }).catchError((error) {
                Get.snackbar(
                  'خطأ',
                  'حدث خطأ أثناء حذف الوجهة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
  
  void _showAddEditDirectionDialog(BuildContext context, {Direction? direction}) {
    final isEditing = direction != null;
    final formKey = GlobalKey<FormState>();
    
    // Form controllers
    final titleController = TextEditingController(text: direction?.title ?? '');
    final descriptionController = TextEditingController(text: direction?.description ?? '');
    final basePriceController = TextEditingController(
      text: direction?.basePrice.toString() ?? '100.0',
    );
    final pricePerKmController = TextEditingController(
      text: direction?.pricePerKm.toString() ?? '5.0',
    );
    final estimatedDistanceController = TextEditingController(
      text: direction?.estimatedDistance.toString() ?? '0.0',
    );
    final estimatedDurationController = TextEditingController(
      text: direction?.estimatedDuration.toString() ?? '0',
    );
    
    // Source and destination coordinates
    final sourceLatController = TextEditingController(
      text: direction?.startLocation.latitude.toString() ?? '',
    );
    final sourceLngController = TextEditingController(
      text: direction?.startLocation.longitude.toString() ?? '',
    );
    final destLatController = TextEditingController(
      text: direction?.endLocation.latitude.toString() ?? '',
    );
    final destLngController = TextEditingController(
      text: direction?.endLocation.longitude.toString() ?? '',
    );
    
    // Active status
    final isActiveRx = (direction?.isActive ?? true).obs;
    
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: Get.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.9,
            maxWidth: 600,
          ),
          padding: const EdgeInsets.all(16),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Dialog title
                  Text(
                    isEditing ? 'تعديل الوجهة' : 'إضافة وجهة جديدة',
                    style: Theme.of(context).textTheme.titleLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  
                  // Title field
                  TextFormField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'عنوان الوجهة *',
                      hintText: 'مثال: غار حراء',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال عنوان الوجهة';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Description field
                  TextFormField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'وصف الوجهة',
                      hintText: 'وصف اختياري للوجهة',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  
                  // Price fields
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: basePriceController,
                          decoration: const InputDecoration(
                            labelText: 'السعر الأساسي *',
                            hintText: 'مثال: 100.0',
                            border: OutlineInputBorder(),
                            prefixText: 'ر.س ',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مطلوب';
                            }
                            if (double.tryParse(value) == null) {
                              return 'رقم غير صالح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: pricePerKmController,
                          decoration: const InputDecoration(
                            labelText: 'السعر لكل كم *',
                            hintText: 'مثال: 5.0',
                            border: OutlineInputBorder(),
                            prefixText: 'ر.س ',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مطلوب';
                            }
                            if (double.tryParse(value) == null) {
                              return 'رقم غير صالح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // Estimated distance and duration
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: estimatedDistanceController,
                          decoration: const InputDecoration(
                            labelText: 'المسافة التقديرية (كم) *',
                            hintText: 'مثال: 10.5',
                            border: OutlineInputBorder(),
                            suffixText: 'كم',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مطلوب';
                            }
                            if (double.tryParse(value) == null) {
                              return 'رقم غير صالح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: estimatedDurationController,
                          decoration: const InputDecoration(
                            labelText: 'الوقت التقديري (دقيقة) *',
                            hintText: 'مثال: 30',
                            border: OutlineInputBorder(),
                            suffixText: 'دقيقة',
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مطلوب';
                            }
                            if (int.tryParse(value) == null) {
                              return 'رقم غير صالح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  
                  // Location coordinates section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إحداثيات الموقع',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        
                        // Source location
                        Text(
                          'نقطة البداية *',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: sourceLatController,
                                decoration: const InputDecoration(
                                  labelText: 'خط العرض',
                                  hintText: 'مثال: 21.4614',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'مطلوب';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'رقم غير صالح';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextFormField(
                                controller: sourceLngController,
                                decoration: const InputDecoration(
                                  labelText: 'خط الطول',
                                  hintText: 'مثال: 39.8741',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'مطلوب';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'رقم غير صالح';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // Destination location
                        Text(
                          'نقطة النهاية *',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: destLatController,
                                decoration: const InputDecoration(
                                  labelText: 'خط العرض',
                                  hintText: 'مثال: 21.4614',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'مطلوب';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'رقم غير صالح';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextFormField(
                                controller: destLngController,
                                decoration: const InputDecoration(
                                  labelText: 'خط الطول',
                                  hintText: 'مثال: 39.8741',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'مطلوب';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'رقم غير صالح';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Active status
                  Obx(() => SwitchListTile(
                    title: const Text('الحالة'),
                    subtitle: Text(
                      isActiveRx.value ? 'نشط (ظاهر للمستخدمين)' : 'غير نشط (مخفي)',
                    ),
                    value: isActiveRx.value,
                    onChanged: (value) => isActiveRx.value = value,
                    activeColor: Colors.green,
                  )),
                  const SizedBox(height: 24),
                  
                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('إلغاء'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          if (formKey.currentState!.validate()) {
                            // Parse form values
                            final title = titleController.text;
                            final description = descriptionController.text;
                            final basePrice = double.parse(basePriceController.text);
                            final pricePerKm = double.parse(pricePerKmController.text);
                            final estimatedDistance = double.parse(estimatedDistanceController.text);
                            final estimatedDuration = int.parse(estimatedDurationController.text);
                            final sourceLat = double.parse(sourceLatController.text);
                            final sourceLng = double.parse(sourceLngController.text);
                            final destLat = double.parse(destLatController.text);
                            final destLng = double.parse(destLngController.text);
                            final isActive = isActiveRx.value;
                            
                            // Create GeoPoints
                            final startLocation = GeoPoint(sourceLat, sourceLng);
                            final endLocation = GeoPoint(destLat, destLng);
                            
                            Get.back();
                            
                            if (isEditing) {
                              // Update existing direction
                              final updatedDirection = direction!.copyWith(
                                title: title,
                                description: description,
                                basePrice: basePrice,
                                pricePerKm: pricePerKm,
                                estimatedDistance: estimatedDistance,
                                estimatedDuration: estimatedDuration,
                                startLocation: startLocation,
                                endLocation: endLocation,
                                isActive: isActive,
                                updatedAt: Timestamp.now(),
                              );
                              
                              controller.updateDirection(updatedDirection).then((_) {
                                Get.snackbar(
                                  'تم بنجاح',
                                  'تم تحديث الوجهة بنجاح',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                );
                              }).catchError((error) {
                                Get.snackbar(
                                  'خطأ',
                                  'حدث خطأ أثناء تحديث الوجهة',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.red,
                                  colorText: Colors.white,
                                );
                              });
                            } else {
                              // Create new direction
                              final newDirection = Direction(
                                id: '', // Will be set by Firestore
                                title: title,
                                description: description,
                                basePrice: basePrice,
                                pricePerKm: pricePerKm,
                                startLocation: startLocation,
                                endLocation: endLocation,
                                estimatedDistance: estimatedDistance,
                                estimatedDuration: estimatedDuration,
                                isActive: isActive,
                                createdAt: Timestamp.now(),
                              );
                              
                              controller.addDirection(newDirection).then((_) {
                                Get.snackbar(
                                  'تم بنجاح',
                                  'تمت إضافة الوجهة بنجاح',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                );
                              }).catchError((error) {
                                Get.snackbar(
                                  'خطأ',
                                  'حدث خطأ أثناء إضافة الوجهة',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.red,
                                  colorText: Colors.white,
                                );
                              });
                            }
                          }
                        },
                        child: Text(isEditing ? 'تحديث' : 'إضافة'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
