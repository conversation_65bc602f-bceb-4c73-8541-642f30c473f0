import 'package:ai_delivery_app/app/data/models/coupon_model.dart';
import 'package:ai_delivery_app/app/modules/admin/controllers/admin_coupons_controller.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class AdminCouponsView extends GetView<AdminCouponsController> {
  const AdminCouponsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الكوبونات'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddEditCouponDialog(context),
            tooltip: 'إضافة كوبون جديد',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadCoupons,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (controller.errorMessage.value != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
                const SizedBox(height: 16),
                Text(
                  controller.errorMessage.value!,
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: controller.loadCoupons,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }
        
        if (controller.coupons.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.local_offer_outlined, size: 64, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                Text(
                  'لا توجد كوبونات',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _showAddEditCouponDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة كوبون جديد'),
                ),
                const SizedBox(height: 16),
                TextButton.icon(
                  onPressed: controller.seedCoupons,
                  icon: const Icon(Icons.data_array),
                  label: const Text('إضافة بيانات تجريبية'),
                ),
              ],
            ),
          );
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.coupons.length,
          itemBuilder: (context, index) {
            final coupon = controller.coupons[index];
            return _buildCouponCard(context, coupon);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddEditCouponDialog(context),
        tooltip: 'إضافة كوبون جديد',
        child: const Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildCouponCard(BuildContext context, Coupon coupon) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('yyyy/MM/dd');
    
    // Format discount value
    String discountText;
    if (coupon.discountType == DiscountType.percentage) {
      discountText = '${coupon.discountValue.toStringAsFixed(0)}%';
    } else {
      discountText = '${coupon.discountValue.toStringAsFixed(2)} ر.س';
    }
    
    // Format date range
    String dateRangeText = 'من ${dateFormat.format(coupon.startDate.toDate())}';
    if (coupon.endDate != null) {
      dateRangeText += ' إلى ${dateFormat.format(coupon.endDate!.toDate())}';
    } else {
      dateRangeText += ' (بدون تاريخ انتهاء)';
    }
    
    // Check if coupon is expired
    final isExpired = coupon.endDate != null && 
        coupon.endDate!.toDate().isBefore(DateTime.now());
    
    // Check if coupon usage limit is reached
    final isUsageLimitReached = coupon.usageLimit != null && 
        coupon.usageCount >= coupon.usageLimit!;
    
    // Determine status text and color
    String statusText;
    Color statusColor;
    
    if (!coupon.isActive) {
      statusText = 'غير مفعل';
      statusColor = Colors.grey;
    } else if (isExpired) {
      statusText = 'منتهي';
      statusColor = Colors.red;
    } else if (isUsageLimitReached) {
      statusText = 'تم استنفاذ الحد';
      statusColor = Colors.orange;
    } else {
      statusText = 'مفعل';
      statusColor = Colors.green;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: coupon.isActive ? theme.colorScheme.primary.withOpacity(0.2) : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with code and status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.local_offer, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      coupon.code,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Coupon details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  coupon.title,
                  style: theme.textTheme.titleMedium,
                ),
                if (coupon.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    coupon.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                
                // Discount info
                Row(
                  children: [
                    Icon(Icons.discount, size: 16, color: theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      'الخصم: $discountText',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
                
                // Date range
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.date_range, size: 16, color: theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        dateRangeText,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
                
                // Usage info
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.people, size: 16, color: theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      'الاستخدام: ${coupon.usageCount}',
                      style: theme.textTheme.bodyMedium,
                    ),
                    if (coupon.usageLimit != null) ...[
                      Text(
                        ' / ${coupon.usageLimit}',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ],
                ),
                
                // Minimum order amount
                if (coupon.minimumOrderAmount != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.shopping_cart, size: 16, color: theme.colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        'الحد الأدنى للطلب: ${coupon.minimumOrderAmount!.toStringAsFixed(2)} ر.س',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ],
                
                // User type
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.person, size: 16, color: theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      'متاح لـ: ${_getUserTypeText(coupon.userType, coupon.newUserDays)}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showAddEditCouponDialog(
                    context, 
                    coupon: coupon,
                  ),
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _toggleCouponStatus(coupon),
                  icon: Icon(
                    coupon.isActive ? Icons.unpublished : Icons.check_circle,
                  ),
                  label: Text(coupon.isActive ? 'تعطيل' : 'تفعيل'),
                  style: TextButton.styleFrom(
                    foregroundColor: coupon.isActive ? Colors.red : Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _confirmDeleteCoupon(context, coupon),
                  icon: const Icon(Icons.delete),
                  label: const Text('حذف'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  String _getUserTypeText(UserType userType, int? newUserDays) {
    switch (userType) {
      case UserType.all:
        return 'جميع المستخدمين';
      case UserType.new_users:
        return 'المستخدمين الجدد (${newUserDays ?? 0} يوم)';
      case UserType.existing_users:
        return 'المستخدمين الحاليين';
      case UserType.specific_users:
        return 'مستخدمين محددين';
      default:
        return 'غير محدد';
    }
  }
  
  void _showAddEditCouponDialog(BuildContext context, {Coupon? coupon}) {
    final isEditing = coupon != null;
    final formKey = GlobalKey<FormState>();
    
    // Reset form and set values if editing
    controller.resetForm();
    if (isEditing) {
      controller.setFormDataForCoupon(coupon);
    }
    
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 600,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Dialog title
                    Text(
                      isEditing ? 'تعديل الكوبون' : 'إضافة كوبون جديد',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Coupon code
                    TextFormField(
                      controller: controller.codeController,
                      decoration: const InputDecoration(
                        labelText: 'كود الكوبون *',
                        hintText: 'مثال: SUMMER50',
                        border: OutlineInputBorder(),
                      ),
                      textCapitalization: TextCapitalization.characters,
                    ),
                    const SizedBox(height: 16),
                    
                    // Coupon title
                    TextFormField(
                      controller: controller.titleController,
                      decoration: const InputDecoration(
                        labelText: 'عنوان الكوبون *',
                        hintText: 'مثال: خصم الصيف',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Coupon description
                    TextFormField(
                      controller: controller.descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'وصف الكوبون',
                        hintText: 'مثال: خصم 50% على جميع الرحلات',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    
                    // Discount type and value
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Obx(() => DropdownButtonFormField<DiscountType>(
                            value: controller.selectedDiscountType.value,
                            decoration: const InputDecoration(
                              labelText: 'نوع الخصم *',
                              border: OutlineInputBorder(),
                            ),
                            items: [
                              DropdownMenuItem(
                                value: DiscountType.percentage,
                                child: const Text('نسبة مئوية'),
                              ),
                              DropdownMenuItem(
                                value: DiscountType.fixed,
                                child: const Text('مبلغ ثابت'),
                              ),
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                controller.selectedDiscountType.value = value;
                              }
                            },
                          )),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 1,
                          child: TextFormField(
                            controller: controller.discountValueController,
                            decoration: InputDecoration(
                              labelText: 'قيمة الخصم *',
                              border: const OutlineInputBorder(),
                              suffixText: controller.selectedDiscountType.value == DiscountType.percentage ? '%' : 'ر.س',
                              // suffixText: Obx(() => controller.selectedDiscountType.value == DiscountType.percentage ? '%' : 'ر.س'),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Minimum order amount and maximum discount
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: controller.minimumOrderController,
                            decoration: const InputDecoration(
                              labelText: 'الحد الأدنى للطلب',
                              hintText: 'اختياري',
                              border: OutlineInputBorder(),
                              suffixText: 'ر.س',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Obx(() => TextFormField(
                            controller: controller.maximumDiscountController,
                            decoration: const InputDecoration(
                              labelText: 'الحد الأقصى للخصم',
                              hintText: 'اختياري',
                              border: OutlineInputBorder(),
                              suffixText: 'ر.س',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            enabled: controller.selectedDiscountType.value == DiscountType.percentage,
                          )),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Date range
                    Row(
                      children: [
                        Expanded(
                          child: Obx(() => InkWell(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: controller.startDate.value ?? DateTime.now(),
                                firstDate: DateTime.now().subtract(const Duration(days: 365)),
                                lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
                              );
                              if (picked != null) {
                                controller.startDate.value = picked;
                              }
                            },
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'تاريخ البدء *',
                                border: OutlineInputBorder(),
                                suffixIcon: Icon(Icons.calendar_today),
                              ),
                              child: Text(
                                controller.startDate.value != null
                                    ? DateFormat('yyyy/MM/dd').format(controller.startDate.value!)
                                    : 'اختر التاريخ',
                              ),
                            ),
                          )),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Obx(() => InkWell(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: controller.endDate.value ?? DateTime.now().add(const Duration(days: 30)),
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
                              );
                              if (picked != null) {
                                controller.endDate.value = picked;
                              }
                            },
                            child: InputDecorator(
                              decoration: InputDecoration(
                                labelText: 'تاريخ الانتهاء',
                                hintText: 'اختياري',
                                border: const OutlineInputBorder(),
                                suffixIcon: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (controller.endDate.value != null)
                                      IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () => controller.endDate.value = null,
                                        iconSize: 18,
                                      ),
                                    const Icon(Icons.calendar_today),
                                  ],
                                ),
                              ),
                              child: Text(
                                controller.endDate.value != null
                                    ? DateFormat('yyyy/MM/dd').format(controller.endDate.value!)
                                    : 'بدون تاريخ انتهاء',
                              ),
                            ),
                          )),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Usage limits
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: controller.usageLimitController,
                            decoration: const InputDecoration(
                              labelText: 'الحد الأقصى للاستخدام',
                              hintText: 'اختياري',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: controller.usageLimitPerUserController,
                            decoration: const InputDecoration(
                              labelText: 'الحد الأقصى لكل مستخدم',
                              hintText: 'اختياري',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // User type
                    Obx(() => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'متاح لـ:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        RadioListTile<UserType>(
                          title: const Text('جميع المستخدمين'),
                          value: UserType.all,
                          groupValue: controller.selectedUserType.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedUserType.value = value;
                            }
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                        RadioListTile<UserType>(
                          title: const Text('المستخدمين الجدد'),
                          value: UserType.new_users,
                          groupValue: controller.selectedUserType.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedUserType.value = value;
                            }
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                        if (controller.selectedUserType.value == UserType.new_users)
                          Padding(
                            padding: const EdgeInsets.only(left: 32),
                            child: TextFormField(
                              controller: controller.newUserDaysController,
                              decoration: const InputDecoration(
                                labelText: 'عدد أيام المستخدم الجديد *',
                                hintText: 'مثال: 30',
                                border: OutlineInputBorder(),
                                suffixText: 'يوم',
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        RadioListTile<UserType>(
                          title: const Text('المستخدمين الحاليين'),
                          value: UserType.existing_users,
                          groupValue: controller.selectedUserType.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedUserType.value = value;
                            }
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ],
                    )),
                    const SizedBox(height: 16),
                    
                    // Active status
                    Obx(() => SwitchListTile(
                      title: const Text('مفعل'),
                      value: controller.isActive.value,
                      onChanged: (value) => controller.isActive.value = value,
                      contentPadding: EdgeInsets.zero,
                    )),
                    const SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Get.back(),
                          child: const Text('إلغاء'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            if (isEditing) {
                              controller.updateCoupon(coupon.id).then((_) {
                                Get.back();
                              });
                            } else {
                              controller.addCoupon().then((_) {
                                Get.back();
                              });
                            }
                          },
                          child: Text(isEditing ? 'تحديث' : 'إضافة'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  void _toggleCouponStatus(Coupon coupon) {
    Get.dialog(
      AlertDialog(
        title: Text(coupon.isActive ? 'تعطيل الكوبون' : 'تفعيل الكوبون'),
        content: Text(
          coupon.isActive
              ? 'هل أنت متأكد من رغبتك في تعطيل هذا الكوبون؟'
              : 'هل أنت متأكد من رغبتك في تفعيل هذا الكوبون؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.toggleCouponStatus(coupon.id, coupon.isActive);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: coupon.isActive ? Colors.red : Colors.green,
            ),
            child: Text(coupon.isActive ? 'تعطيل' : 'تفعيل'),
          ),
        ],
      ),
    );
  }
  
  void _confirmDeleteCoupon(BuildContext context, Coupon coupon) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف الكوبون'),
        content: Text('هل أنت متأكد من رغبتك في حذف الكوبون "${coupon.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteCoupon(coupon.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
