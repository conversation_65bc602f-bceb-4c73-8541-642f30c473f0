import 'package:ai_delivery_app/app/modules/admin/controllers/admin_payments_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class AdminPaymentsView extends GetView<AdminPaymentsController> {
  const AdminPaymentsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = intl.NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المدفوعات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => controller.showSearchDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => controller.showFilterOptions(),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: controller.refreshData,
        child: Obx(
          () => controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    _buildRevenueOverview(context, currencyFormat),
                    _buildFilterTabs(context),
                    Expanded(
                      child: controller.payments.isEmpty
                          ? Center(
                              child: Text(
                                'لا توجد معاملات مالية متاحة',
                                style: theme.textTheme.titleMedium,
                              ),
                            )
                          : ListView.builder(
                              itemCount: controller.payments.length,
                              itemBuilder: (context, index) {
                                final payment = controller.payments[index];
                                return _buildPaymentItem(context, payment);
                              },
                            ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildRevenueOverview(BuildContext context, intl.NumberFormat formatter) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجمالي الإيرادات',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
            formatter.format(controller.totalRevenue.value),
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          )),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildRevenueCard(
                  context,
                  title: 'اليوم',
                  value: controller.todayRevenue.value,
                  formatter: formatter,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildRevenueCard(
                  context,
                  title: 'الأسبوع',
                  value: controller.weekRevenue.value,
                  formatter: formatter,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildRevenueCard(
                  context,
                  title: 'الشهر',
                  value: controller.monthRevenue.value,
                  formatter: formatter,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueCard(
    BuildContext context, {
    required String title,
    required double value,
    required intl.NumberFormat formatter,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            formatter.format(value),
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildFilterTab(
            context,
            title: 'الكل',
            filter: 'all',
            icon: Icons.all_inclusive,
          ),
          _buildFilterTab(
            context,
            title: 'الرحلات',
            filter: 'trips',
            icon: Icons.directions_car,
          ),
          _buildFilterTab(
            context,
            title: 'الإيداعات',
            filter: 'deposits',
            icon: Icons.add_circle_outline,
          ),
          _buildFilterTab(
            context,
            title: 'السحوبات',
            filter: 'withdrawals',
            icon: Icons.remove_circle_outline,
          ),
          _buildFilterTab(
            context,
            title: 'الاستردادات',
            filter: 'refunds',
            icon: Icons.replay,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTab(
    BuildContext context, {
    required String title,
    required String filter,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    
    return Obx(() {
      final isSelected = controller.filterOption.value == filter;
      
      return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: InkWell(
          onTap: () => controller.setFilter(filter),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: isSelected ? theme.colorScheme.primary : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildPaymentItem(BuildContext context, Map<String, dynamic> payment) {
    final theme = Theme.of(context);
    final status = payment['status'] as String;
    final type = payment['type'] as String;
    final statusColor = _getStatusColor(status);
    final typeIcon = _getTypeIcon(type);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
          child: Icon(
            typeIcon,
            color: theme.colorScheme.primary,
          ),
        ),
        title: Text(
          payment['description'],
          style: theme.textTheme.titleMedium,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 4),
                Text('${payment['date']} - ${payment['time']}'),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(status),
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        trailing: Text(
          '${payment['amount']} ر.س',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: type.contains('withdrawal') ? Colors.red : Colors.green,
          ),
        ),
        onTap: () => controller.showPaymentDetails(payment['id']),
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'قيد الانتظار';
      case 'failed':
        return 'فشلت';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    if (type == 'trip') {
      return Icons.directions_car;
    } else if (type == 'wallet_deposit') {
      return Icons.add_circle_outline;
    } else if (type == 'wallet_withdrawal') {
      return Icons.remove_circle_outline;
    } else if (type == 'wallet_refund') {
      return Icons.replay;
    } else {
      return Icons.attach_money;
    }
  }
}
