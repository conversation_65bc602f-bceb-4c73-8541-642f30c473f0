import 'package:ai_delivery_app/app/modules/admin/controllers/admin_users_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminUsersView extends GetView<AdminUsersController> {
  const AdminUsersView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة المستخدمين'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => controller.showSearchDialog(),
              tooltip: 'بحث',
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => controller.showFilterOptions(),
              tooltip: 'تصفية',
            ),
          ],
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage.value != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline,
                      size: 48, color: theme.colorScheme.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value!,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.loadUsers,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Search Bar
              if (controller.searchQuery.value.isNotEmpty)
                _buildSearchBar(context),

              // Stats Cards
              _buildStatsCards(context),

              // Users List
              Expanded(
                child: RefreshIndicator(
                  onRefresh: controller.refreshData,
                  child: controller.users.isEmpty
                      ? Center(
                          child: Text(
                            'لا يوجد مستخدمين',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: controller.users.length,
                          itemBuilder: (context, index) {
                            final user = controller.users[index];
                            return _buildUserItem(context, user);
                          },
                        ),
                ),
              ),
            ],
          );
        }),
        floatingActionButton: FloatingActionButton(
          onPressed: () => controller.navigateToAddUser(),
          backgroundColor: theme.colorScheme.primary,
          child: const Icon(Icons.add),
          tooltip: 'إضافة مستخدم',
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      color: theme.colorScheme.surface,
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن مستخدم',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: controller.clearSearch,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              onChanged: controller.onSearchChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildStatCard(
            context,
            icon: Icons.people,
            title: 'إجمالي المستخدمين',
            value: controller.totalUsers.toString(),
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            context,
            icon: Icons.person_add,
            title: 'مستخدمين جدد',
            value: controller.newUsers.toString(),
            color: Colors.green,
          ),
          const SizedBox(width: 16),
          _buildStatCard(
            context,
            icon: Icons.block,
            title: 'محظورين',
            value: controller.blockedUsers.toString(),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserItem(BuildContext context, Map<String, dynamic> user) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => controller.showUserDetails(user['id']),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // User Avatar
              CircleAvatar(
                radius: 30,
                backgroundImage: user['profileImageUrl'] != null
                    ? NetworkImage(user['profileImageUrl'])
                    : null,
                child: user['profileImageUrl'] == null
                    ? Icon(
                        Icons.person,
                        color: theme.colorScheme.primary,
                        size: 30,
                      )
                    : null,
              ),
              const SizedBox(width: 16),

              // User Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          user['name'],
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (user['isBlocked'] == true)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'محظور',
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user['email'],
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.phone,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user['phoneNumber'] ?? 'لا يوجد',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user['joinDate'],
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Actions
              Column(
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => controller.navigateToEditUser(user['id']),
                    tooltip: 'تعديل',
                  ),
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: theme.colorScheme.primary,
                    ),
                    tooltip: 'خيارات',
                    onSelected: (value) {
                      switch (value) {
                        case 'block':
                          controller.toggleUserBlock(
                              user['id'], user['status'] == 'blocked');
                          break;
                        case 'suspend':
                          controller.suspendUser(user['id']);
                          break;
                        case 'review':
                          if (user['role'] == 'driver') {
                            controller.setDriverUnderReview(user['id']);
                          } else {
                            Get.snackbar(
                              'خطأ',
                              'يمكن وضع السائقين فقط قيد المراجعة',
                              backgroundColor: Colors.red,
                              colorText: Colors.white,
                            );
                          }
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'block',
                        child: Row(
                          children: [
                            Icon(
                              user['status'] == 'blocked'
                                  ? Icons.lock_open
                                  : Icons.block,
                              color: user['status'] == 'blocked'
                                  ? Colors.green
                                  : Colors.red,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(user['status'] == 'blocked'
                                ? 'إلغاء الحظر'
                                : 'حظر'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'suspend',
                        child: Row(
                          children: [
                            Icon(
                              Icons.timer,
                              color: Colors.orange,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text('إيقاف مؤقت'),
                          ],
                        ),
                      ),
                      if (user['role'] == 'driver')
                        const PopupMenuItem(
                          value: 'review',
                          child: Row(
                            children: [
                              Icon(
                                Icons.pending_actions,
                                color: Colors.blue,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text('وضع قيد المراجعة'),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
