import 'package:ai_delivery_app/app/modules/admin/controllers/admin_faq_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminFAQView extends GetView<AdminFAQController> {
  const AdminFAQView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأسئلة الشائعة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => controller.showSearchDialog(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.showAddEditFAQDialog(),
        child: const Icon(Icons.add),
      ),
      body: RefreshIndicator(
        onRefresh: controller.refreshData,
        child: Obx(
          () => controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    _buildCategoryTabs(context),
                    Expanded(
                      child: controller.faqs.isEmpty
                          ? Center(
                              child: Text(
                                'لا توجد أسئلة شائعة متاحة',
                                style: theme.textTheme.titleMedium,
                              ),
                            )
                          : ReorderableListView.builder(
                              itemCount: controller.faqs.length,
                              onReorder: controller.reorderFAQs,
                              itemBuilder: (context, index) {
                                final faq = controller.faqs[index];
                                return _buildFAQItem(context, faq, index);
                              },
                            ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildCategoryTabs(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildCategoryTab(
            context,
            title: 'الكل',
            category: 'all',
            icon: Icons.all_inclusive,
          ),
          ...controller.categories.map((category) {
            return _buildCategoryTab(
              context,
              title: _getCategoryText(category),
              category: category,
              icon: _getCategoryIcon(category),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCategoryTab(
    BuildContext context, {
    required String title,
    required String category,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    
    return Obx(() {
      final isSelected = controller.selectedCategory.value == category;
      
      return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: InkWell(
          onTap: () => controller.setCategory(category),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: isSelected ? theme.colorScheme.primary : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildFAQItem(BuildContext context, Map<String, dynamic> faq, int index) {
    final theme = Theme.of(context);
    final isActive = faq['isActive'] as bool;
    final category = faq['category'] as String;
    final categoryColor = _getCategoryColor(category);
    
    return Card(
      key: Key('faq_${faq['id']}'),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ExpansionTile(
        leading: Icon(
          _getCategoryIcon(category),
          color: categoryColor,
        ),
        title: Text(
          faq['question'],
          style: theme.textTheme.titleMedium?.copyWith(
            color: isActive ? null : Colors.grey,
            decoration: isActive ? null : TextDecoration.lineThrough,
          ),
        ),
        subtitle: Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: categoryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getCategoryText(category),
            style: TextStyle(
              color: categoryColor,
              fontSize: 12,
            ),
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                isActive ? Icons.visibility : Icons.visibility_off,
                color: isActive ? Colors.green : Colors.grey,
              ),
              onPressed: () => controller.toggleFAQStatus(faq['id'], isActive),
              tooltip: isActive ? 'إخفاء' : 'إظهار',
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue),
              onPressed: () => controller.showAddEditFAQDialog(faq: faq),
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => controller.showDeleteConfirmation(faq['id']),
              tooltip: 'حذف',
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              faq['answer'],
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _getCategoryText(String category) {
    switch (category) {
      case 'general':
        return 'عام';
      case 'payment':
        return 'الدفع';
      case 'trips':
        return 'الرحلات';
      case 'account':
        return 'الحساب';
      case 'drivers':
        return 'السائقين';
      case 'technical':
        return 'مشاكل تقنية';
      default:
        return category;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'general':
        return Colors.blue;
      case 'payment':
        return Colors.green;
      case 'trips':
        return Colors.purple;
      case 'account':
        return Colors.orange;
      case 'drivers':
        return Colors.teal;
      case 'technical':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'general':
        return Icons.info_outline;
      case 'payment':
        return Icons.attach_money;
      case 'trips':
        return Icons.directions_car;
      case 'account':
        return Icons.person;
      case 'drivers':
        return Icons.drive_eta;
      case 'technical':
        return Icons.build;
      default:
        return Icons.help_outline;
    }
  }
}
