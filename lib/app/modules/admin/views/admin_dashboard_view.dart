import 'package:ai_delivery_app/app/modules/admin/controllers/admin_dashboard_controller.dart';
import 'package:ai_delivery_app/app/modules/admin/views/simple_chart_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminDashboardView extends GetView<AdminDashboardController> {
  const AdminDashboardView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('لوحة التحكم'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: controller.refreshData,
              tooltip: 'تحديث البيانات',
            ),
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: () => controller.showNotifications(),
              tooltip: 'الإشعارات',
            ),
          ],
        ),
        drawer: _buildAdminDrawer(context),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage.value != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline,
                      size: 48, color: theme.colorScheme.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value!,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.loadDashboardData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: controller.refreshData,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome Message
                  Text(
                    'مرحبًا، ${controller.adminName}',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'آخر تحديث: ${controller.formattedLastUpdate}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Stats Cards
                  _buildStatsCards(context),

                  const SizedBox(height: 24),

                  // Revenue Chart
                  SimpleRevenueChart(controller: controller),

                  const SizedBox(height: 24),

                  // Recent Trips
                  _buildRecentTrips(context),

                  const SizedBox(height: 24),

                  // Active Drivers
                  _buildActiveDrivers(context),

                  const SizedBox(height: 24),

                  // Support Tickets
                  _buildSupportTickets(context),
                ],
              ),
            ),
          );
        }),
        floatingActionButton: FloatingActionButton(
          onPressed: () => controller.showQuickActions(),
          backgroundColor: theme.colorScheme.primary,
          child: const Icon(Icons.add),
          tooltip: 'إجراءات سريعة',
        ),
      ),
    );
  }

  Widget _buildAdminDrawer(BuildContext context) {
    final theme = Theme.of(context);

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.admin_panel_settings,
                    color: theme.colorScheme.primary,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'لوحة تحكم المشرف',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  controller.adminEmail,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.dashboard,
            title: 'لوحة التحكم',
            isSelected: true,
            onTap: () => Get.back(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.people,
            title: 'إدارة المستخدمين',
            onTap: () => controller.navigateToUsersManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.drive_eta,
            title: 'إدارة السائقين',
            onTap: () => controller.navigateToDriversManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.map,
            title: 'إدارة الرحلات',
            onTap: () => controller.navigateToTripsManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.payment,
            title: 'إدارة المدفوعات',
            onTap: () => controller.navigateToPaymentsManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.support_agent,
            title: 'إدارة الدعم',
            onTap: () => controller.navigateToSupportManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.directions,
            title: 'إدارة الوجهات',
            onTap: () => controller.navigateToDirectionsManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.local_offer,
            title: 'إدارة الكوبونات',
            onTap: () => controller.navigateToCouponsManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.question_answer,
            title: 'إدارة الأسئلة الشائعة',
            onTap: () => controller.navigateToFAQManagement(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.settings,
            title: 'الإعدادات',
            onTap: () => controller.navigateToSettings(),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.app_settings_alt,
            title: 'إعدادات التطبيق',
            onTap: () => controller.navigateToAppSettings(),
          ),
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            onTap: () => controller.logout(),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    bool isSelected = false,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? theme.colorScheme.primary : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? theme.colorScheme.primary : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      onTap: onTap,
    );
  }

  Widget _buildStatsCards(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildStatCard(
          context,
          icon: Icons.people,
          title: 'المستخدمين',
          value: controller.stats.value.totalUsers.toString(),
          change: '+${controller.stats.value.newUsers} اليوم',
          isPositive: true,
          onTap: () => controller.navigateToUsersManagement(filter: 'all'),
        ),
        _buildStatCard(
          context,
          icon: Icons.drive_eta,
          title: 'السائقين',
          value: controller.stats.value.totalDrivers.toString(),
          change: '+${controller.stats.value.newDrivers} اليوم',
          isPositive: true,
          onTap: () => controller.navigateToDriversManagement(filter: 'all'),
        ),
        _buildStatCard(
          context,
          icon: Icons.map,
          title: 'الرحلات',
          value: controller.stats.value.totalTrips.toString(),
          change: '+${controller.stats.value.tripsToday} اليوم',
          isPositive: true,
          onTap: () => controller.navigateToTripsManagement(filter: 'all'),
        ),
        _buildStatCard(
          context,
          icon: Icons.attach_money,
          title: 'الإيرادات',
          value:
              '${controller.stats.value.totalRevenue.toStringAsFixed(2)} ر.س',
          change:
              '+${controller.stats.value.revenueToday.toStringAsFixed(2)} ر.س اليوم',
          isPositive: true,
          onTap: () => controller.navigateToPaymentsManagement(filter: 'all'),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required String change,
    required bool isPositive,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  icon,
                  color: theme.colorScheme.primary,
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isPositive
                        ? Colors.green.withOpacity(0.1)
                        : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    change,
                    style: TextStyle(
                      color: isPositive ? Colors.green : Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                if (onTap != null)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTrips(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الرحلات الأخيرة',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => controller.navigateToTripsManagement(),
                child: const Text('عرض الكل'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (controller.recentTrips.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'لا توجد رحلات حديثة',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.recentTrips.length,
              itemBuilder: (context, index) {
                final trip = controller.recentTrips[index];
                return _buildTripItem(context, trip);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildTripItem(BuildContext context, Map<String, dynamic> trip) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
        child: Icon(
          Icons.directions_car,
          color: theme.colorScheme.primary,
        ),
      ),
      title: Text(
        'من ${trip['origin']} إلى ${trip['destination']}',
        style: theme.textTheme.titleSmall,
      ),
      subtitle: Text(
        '${trip['date']} - ${trip['time']}',
        style: theme.textTheme.bodySmall,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${trip['amount']} ر.س',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(trip['status']).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getStatusText(trip['status']),
              style: TextStyle(
                color: _getStatusColor(trip['status']),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      onTap: () => controller.showTripDetails(trip['id']),
    );
  }

  Widget _buildActiveDrivers(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السائقين النشطين',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => controller.navigateToDriversManagement(),
                child: const Text('عرض الكل'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (controller.activeDrivers.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'لا يوجد سائقين نشطين حاليًا',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.activeDrivers.length,
              itemBuilder: (context, index) {
                final driver = controller.activeDrivers[index];
                return _buildDriverItem(context, driver);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildDriverItem(BuildContext context, Map<String, dynamic> driver) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundImage: driver['profileImageUrl'] != null
            ? NetworkImage(driver['profileImageUrl'])
            : null,
        child: driver['profileImageUrl'] == null
            ? Icon(
                Icons.person,
                color: theme.colorScheme.primary,
              )
            : null,
      ),
      title: Text(
        driver['name'],
        style: theme.textTheme.titleSmall,
      ),
      subtitle: Row(
        children: [
          Icon(
            Icons.star,
            color: Colors.amber,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '${driver['rating']} (${driver['ratingCount']})',
            style: theme.textTheme.bodySmall,
          ),
        ],
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          driver['status'],
          style: const TextStyle(
            color: Colors.green,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      onTap: () => controller.showDriverDetails(driver['id']),
    );
  }

  Widget _buildSupportTickets(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تذاكر الدعم',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => controller.navigateToSupportManagement(),
                child: const Text('عرض الكل'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (controller.supportTickets.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'لا توجد تذاكر دعم حالية',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.supportTickets.length,
              itemBuilder: (context, index) {
                final ticket = controller.supportTickets[index];
                return _buildTicketItem(context, ticket);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildTicketItem(BuildContext context, Map<String, dynamic> ticket) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor:
            _getTicketPriorityColor(ticket['priority']).withOpacity(0.1),
        child: Icon(
          Icons.support_agent,
          color: _getTicketPriorityColor(ticket['priority']),
        ),
      ),
      title: Text(
        ticket['subject'],
        style: theme.textTheme.titleSmall,
      ),
      subtitle: Text(
        '${ticket['date']} - ${ticket['time']}',
        style: theme.textTheme.bodySmall,
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: _getTicketStatusColor(ticket['status']).withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          _getTicketStatusText(ticket['status']),
          style: TextStyle(
            color: _getTicketStatusColor(ticket['status']),
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      onTap: () => controller.showTicketDetails(ticket['id']),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'in_progress':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'in_progress':
        return 'جارية';
      case 'cancelled':
        return 'ملغاة';
      case 'pending':
        return 'معلقة';
      default:
        return status;
    }
  }

  Color _getTicketPriorityColor(int priority) {
    switch (priority) {
      case 2:
        return Colors.red;
      case 1:
        return Colors.orange;
      case 0:
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  Color _getTicketStatusColor(String status) {
    switch (status) {
      case 'new':
        return Colors.blue;
      case 'open':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'resolved':
        return Colors.purple;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getTicketStatusText(String status) {
    switch (status) {
      case 'new':
        return 'جديدة';
      case 'open':
        return 'مفتوحة';
      case 'pending':
        return 'معلقة';
      case 'resolved':
        return 'محلولة';
      case 'closed':
        return 'مغلقة';
      default:
        return status;
    }
  }
}
