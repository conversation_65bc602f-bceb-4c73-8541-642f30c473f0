import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class AdminTripsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final TextEditingController searchController = TextEditingController();
  final RxList<Map<String, dynamic>> trips = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> allTrips = <Map<String, dynamic>>[].obs;
  final RxString searchQuery = ''.obs;
  final RxString filterOption = 'all'.obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  final RxInt totalTrips = 0.obs;
  final RxInt completedTrips = 0.obs;
  final RxInt pendingTrips = 0.obs;
  final RxInt cancelledTrips = 0.obs;

  @override
  void onInit() {
    super.onInit();
    
    // Check if we have a filter parameter from arguments
    if (Get.arguments != null && Get.arguments['filter'] != null) {
      filterOption.value = Get.arguments['filter'];
    }
    
    loadTrips();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadTrips() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('tripRequests')
          .orderBy('createdAt', descending: true)
          .get();

      final List<Map<String, dynamic>> loadedTrips = [];
      int completed = 0;
      int pending = 0;
      int cancelled = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final createdAt = (data['createdAt'] as Timestamp).toDate();
        final status = data['status'] ?? 'pending';

        // Count trips by status
        if (status == 'completed') {
          completed++;
        } else if (status == 'cancelled') {
          cancelled++;
        } else {
          pending++;
        }

        loadedTrips.add({
          'id': doc.id,
          'userId': data['userId'] ?? '',
          'driverId': data['driverId'] ?? '',
          'origin': data['originAddress'] ?? 'Unknown',
          'destination': data['destinationAddress'] ?? 'Unknown',
          'date': intl.DateFormat('yyyy/MM/dd').format(createdAt),
          'time': intl.DateFormat('HH:mm').format(createdAt),
          'amount': (data['fare'] ?? 0.0).toStringAsFixed(2),
          'status': status,
          'createdAt': createdAt,
        });
      }

      allTrips.value = loadedTrips;
      applyFilters(); // This will update the trips list based on filters

      totalTrips.value = loadedTrips.length;
      completedTrips.value = completed;
      pendingTrips.value = pending;
      cancelledTrips.value = cancelled;

    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل بيانات الرحلات: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات الرحلات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      allTrips.value = [];
      trips.value = [];
      totalTrips.value = 0;
      completedTrips.value = 0;
      pendingTrips.value = 0;
      cancelledTrips.value = 0;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> refreshData() async {
    await loadTrips();
  }

  void onSearchChanged(String query) {
    searchQuery.value = query;
    applyFilters();
  }

  void clearSearch() {
    searchQuery.value = '';
    searchController.clear();
    applyFilters();
  }

  void setFilter(String filter) {
    filterOption.value = filter;
    applyFilters();
  }

  void applyFilters() {
    List<Map<String, dynamic>> filteredTrips = List.from(allTrips);

    // Apply status filter
    if (filterOption.value != 'all') {
      filteredTrips = filteredTrips.where((trip) {
        if (filterOption.value == 'completed') {
          return trip['status'] == 'completed';
        } else if (filterOption.value == 'pending') {
          return trip['status'] == 'pending' || 
                 trip['status'] == 'assigned' || 
                 trip['status'] == 'accepted';
        } else if (filterOption.value == 'cancelled') {
          return trip['status'] == 'cancelled';
        }
        return true;
      }).toList();
    }

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filteredTrips = filteredTrips.where((trip) {
        return trip['origin'].toString().toLowerCase().contains(query) ||
               trip['destination'].toString().toLowerCase().contains(query) ||
               trip['id'].toString().toLowerCase().contains(query) ||
               trip['userId'].toString().toLowerCase().contains(query) ||
               trip['driverId'].toString().toLowerCase().contains(query);
      }).toList();
    }

    trips.value = filteredTrips;
  }

  void showSearchDialog() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('البحث عن رحلة'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'اكتب مكان الانطلاق أو الوصول أو رقم الرحلة',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: onSearchChanged,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                clearSearch();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                onSearchChanged(searchController.text);
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }

  void showFilterOptions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'تصفية الرحلات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.all_inclusive),
                title: const Text('جميع الرحلات'),
                trailing: filterOption.value == 'all'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('all');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.check_circle_outline),
                title: const Text('الرحلات المكتملة'),
                trailing: filterOption.value == 'completed'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('completed');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.pending_actions),
                title: const Text('الرحلات قيد التنفيذ'),
                trailing: filterOption.value == 'pending'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('pending');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel_outlined),
                title: const Text('الرحلات الملغاة'),
                trailing: filterOption.value == 'cancelled'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('cancelled');
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void showTripDetails(String tripId) {
    final trip = trips.firstWhere((t) => t['id'] == tripId, orElse: () => {});
    if (trip.isEmpty) return;

    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: Text('تفاصيل الرحلة: ${trip['id']}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailItem('مكان الانطلاق', trip['origin']),
              _buildDetailItem('الوجهة', trip['destination']),
              _buildDetailItem('التاريخ', trip['date']),
              _buildDetailItem('الوقت', trip['time']),
              _buildDetailItem('المبلغ', '${trip['amount']} ر.س'),
              _buildDetailItem('الحالة', _getStatusText(trip['status'])),
              if (trip['userId'] != null && trip['userId'].isNotEmpty)
                _buildDetailItem('معرف المستخدم', trip['userId']),
              if (trip['driverId'] != null && trip['driverId'].isNotEmpty)
                _buildDetailItem('معرف السائق', trip['driverId']),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'قيد الانتظار';
      case 'assigned':
        return 'تم تعيين سائق';
      case 'accepted':
        return 'تم قبول الرحلة';
      case 'started':
        return 'بدأت الرحلة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      case 'assigned':
      case 'accepted':
      case 'started':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
