import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../data/models/app_settings_model.dart';

class AdminAppSettingsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Observable state
  final Rx<AppSettings?> appSettings = Rx<AppSettings?>(null);
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  
  // Form controllers
  // App Version Settings
  final currentVersionController = TextEditingController();
  final minRequiredVersionController = TextEditingController();
  final updateRequiredRx = false.obs;
  final updateUrlController = TextEditingController();
  final updateMessageController = TextEditingController();
  
  // App Status Settings
  final maintenanceModeRx = false.obs;
  final maintenanceMessageController = TextEditingController();
  final maintenanceEndTimeRx = Rxn<DateTime>();
  
  // Financial Settings
  final appFeePercentageController = TextEditingController();
  final taxPercentageController = TextEditingController();
  final currencyCodeController = TextEditingController();
  final currencySymbolController = TextEditingController();
  
  // Country Settings
  final defaultCountryCodeController = TextEditingController();
  final supportedCountriesController = TextEditingController();
  
  // Contact Settings
  final supportEmailController = TextEditingController();
  final supportPhoneController = TextEditingController();
  final supportWhatsappController = TextEditingController();
  
  // Terms and Privacy
  final termsUrlController = TextEditingController();
  final privacyUrlController = TextEditingController();
  
  // Active tab
  final RxInt activeTabIndex = 0.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadAppSettings();
  }
  
  @override
  void onClose() {
    // Dispose controllers
    currentVersionController.dispose();
    minRequiredVersionController.dispose();
    updateUrlController.dispose();
    updateMessageController.dispose();
    maintenanceMessageController.dispose();
    appFeePercentageController.dispose();
    taxPercentageController.dispose();
    currencyCodeController.dispose();
    currencySymbolController.dispose();
    defaultCountryCodeController.dispose();
    supportedCountriesController.dispose();
    supportEmailController.dispose();
    supportPhoneController.dispose();
    supportWhatsappController.dispose();
    termsUrlController.dispose();
    privacyUrlController.dispose();
    super.onClose();
  }
  
  Future<void> loadAppSettings() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      final docRef = _firestore.collection('settings').doc('app_settings');
      final docSnapshot = await docRef.get();
      
      if (docSnapshot.exists) {
        appSettings.value = AppSettings.fromFirestore(docSnapshot);
        _populateFormControllers();
      } else {
        // Create default settings if none exist
        final defaultSettings = AppSettings.defaultSettings;
        await docRef.set(defaultSettings.toFirestore());
        appSettings.value = defaultSettings;
        _populateFormControllers();
      }
    } catch (e) {
      errorMessage.value = 'Error loading app settings: $e';
      print('Error loading app settings: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  void _populateFormControllers() {
    if (appSettings.value == null) return;
    
    // App Version Settings
    currentVersionController.text = appSettings.value!.currentVersion;
    minRequiredVersionController.text = appSettings.value!.minRequiredVersion;
    updateRequiredRx.value = appSettings.value!.updateRequired;
    updateUrlController.text = appSettings.value!.updateUrl;
    updateMessageController.text = appSettings.value!.updateMessage;
    
    // App Status Settings
    maintenanceModeRx.value = appSettings.value!.maintenanceMode;
    maintenanceMessageController.text = appSettings.value!.maintenanceMessage;
    maintenanceEndTimeRx.value = appSettings.value!.maintenanceEndTime;
    
    // Financial Settings
    appFeePercentageController.text = appSettings.value!.appFeePercentage.toString();
    taxPercentageController.text = appSettings.value!.taxPercentage.toString();
    currencyCodeController.text = appSettings.value!.currencyCode;
    currencySymbolController.text = appSettings.value!.currencySymbol;
    
    // Country Settings
    defaultCountryCodeController.text = appSettings.value!.defaultCountryCode;
    supportedCountriesController.text = appSettings.value!.supportedCountries.join(', ');
    
    // Contact Settings
    supportEmailController.text = appSettings.value!.supportEmail;
    supportPhoneController.text = appSettings.value!.supportPhone;
    supportWhatsappController.text = appSettings.value!.supportWhatsapp;
    
    // Terms and Privacy
    termsUrlController.text = appSettings.value!.termsUrl;
    privacyUrlController.text = appSettings.value!.privacyUrl;
  }
  
  Future<void> saveAppSettings() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Validate form fields
      if (!_validateFormFields()) {
        isLoading.value = false;
        return;
      }
      
      // Get current user for updatedBy field
      final currentUser = _auth.currentUser;
      final updatedBy = currentUser?.email ?? 'admin';
      
      // Create updated settings object
      final updatedSettings = AppSettings(
        // App Version Settings
        currentVersion: currentVersionController.text.trim(),
        minRequiredVersion: minRequiredVersionController.text.trim(),
        updateRequired: updateRequiredRx.value,
        updateUrl: updateUrlController.text.trim(),
        updateMessage: updateMessageController.text.trim(),
        
        // App Status Settings
        maintenanceMode: maintenanceModeRx.value,
        maintenanceMessage: maintenanceMessageController.text.trim(),
        maintenanceEndTime: maintenanceEndTimeRx.value,
        
        // Financial Settings
        appFeePercentage: double.tryParse(appFeePercentageController.text.trim()) ?? 10.0,
        taxPercentage: double.tryParse(taxPercentageController.text.trim()) ?? 15.0,
        currencyCode: currencyCodeController.text.trim(),
        currencySymbol: currencySymbolController.text.trim(),
        
        // Country Settings
        defaultCountryCode: defaultCountryCodeController.text.trim(),
        supportedCountries: supportedCountriesController.text.split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
        
        // Contact Settings
        supportEmail: supportEmailController.text.trim(),
        supportPhone: supportPhoneController.text.trim(),
        supportWhatsapp: supportWhatsappController.text.trim(),
        
        // Terms and Privacy
        termsUrl: termsUrlController.text.trim(),
        privacyUrl: privacyUrlController.text.trim(),
        
        // Last Updated
        lastUpdated: Timestamp.now(),
        updatedBy: updatedBy,
      );
      
      // Save to Firestore
      await _firestore
          .collection('settings')
          .doc('app_settings')
          .set(updatedSettings.toFirestore());
      
      // Update local state
      appSettings.value = updatedSettings;
      
      // Show success message
      Get.snackbar(
        'Success',
        'App settings updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      errorMessage.value = 'Error saving app settings: $e';
      print('Error saving app settings: $e');
      
      // Show error message
      Get.snackbar(
        'Error',
        'Failed to update app settings: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  bool _validateFormFields() {
    // Validate version format (x.y.z)
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    if (!versionRegex.hasMatch(currentVersionController.text.trim())) {
      Get.snackbar(
        'Validation Error',
        'Current version must be in format x.y.z (e.g., 1.0.0)',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    if (!versionRegex.hasMatch(minRequiredVersionController.text.trim())) {
      Get.snackbar(
        'Validation Error',
        'Minimum required version must be in format x.y.z (e.g., 1.0.0)',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate URL formats
    if (updateRequiredRx.value && updateUrlController.text.trim().isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Update URL is required when update is required',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate numeric fields
    if (double.tryParse(appFeePercentageController.text.trim()) == null) {
      Get.snackbar(
        'Validation Error',
        'App fee percentage must be a valid number',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    if (double.tryParse(taxPercentageController.text.trim()) == null) {
      Get.snackbar(
        'Validation Error',
        'Tax percentage must be a valid number',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate email format
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(supportEmailController.text.trim())) {
      Get.snackbar(
        'Validation Error',
        'Support email must be a valid email address',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    return true;
  }
  
  void setMaintenanceEndTime(DateTime? dateTime) {
    maintenanceEndTimeRx.value = dateTime;
  }
  
  String formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }
  
  void setActiveTab(int index) {
    activeTabIndex.value = index;
  }
}
