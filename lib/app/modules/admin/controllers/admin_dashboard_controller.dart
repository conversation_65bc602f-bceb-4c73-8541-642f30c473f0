import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class ChartData {
  final int x;
  final double y;

  ChartData(this.x, this.y);
}

class DashboardStats {
  final int totalUsers;
  final int newUsers;
  final int totalDrivers;
  final int newDrivers;
  final int totalTrips;
  final int tripsToday;
  final double totalRevenue;
  final double revenueToday;

  DashboardStats({
    this.totalUsers = 0,
    this.newUsers = 0,
    this.totalDrivers = 0,
    this.newDrivers = 0,
    this.totalTrips = 0,
    this.tripsToday = 0,
    this.totalRevenue = 0.0,
    this.revenueToday = 0.0,
  });
}

class AdminDashboardController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final Rx<DashboardStats> stats = DashboardStats().obs;
  final RxList<Map<String, dynamic>> recentTrips = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> activeDrivers =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> supportTickets =
      <Map<String, dynamic>>[].obs;
  final RxList<ChartData> chartData = <ChartData>[].obs;
  final RxList<String> chartLabels = <String>[].obs;
  final RxDouble chartMaxY = 0.0.obs;
  final RxString selectedChartPeriod = 'week'.obs;
  final Rx<DateTime> lastUpdate = DateTime.now().obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  String get adminName => _auth.currentUser?.displayName ?? 'المشرف';
  String get adminEmail => _auth.currentUser?.email ?? '<EMAIL>';
  String get formattedLastUpdate =>
      intl.DateFormat('yyyy/MM/dd HH:mm').format(lastUpdate.value);

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      await Future.wait([
        _loadStats(),
        _loadRecentTrips(),
        _loadActiveDrivers(),
        _loadSupportTickets(),
      ]);

      updateChartData();
      lastUpdate.value = DateTime.now();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل البيانات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> refreshData() async {
    await loadDashboardData();
  }

  Future<void> _loadStats() async {
    try {
      // Fetch actual data from Firestore

      // Get total users count
      final usersSnapshot = await _firestore.collection('users').count().get();
      final totalUsers = usersSnapshot.count;

      // Get new users count (registered today)
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final newUsersSnapshot = await _firestore
          .collection('users')
          .where('createdAt',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .count()
          .get();
      final newUsers = newUsersSnapshot.count;

      // Get total drivers count
      final driversSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'driver')
          .count()
          .get();
      final totalDrivers = driversSnapshot.count;

      // Get new drivers count (registered today)
      final newDriversSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'driver')
          .where('createdAt',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .count()
          .get();
      final newDrivers = newDriversSnapshot.count;

      // Get total trips count
      final tripsSnapshot =
          await _firestore.collection('tripRequests').count().get();
      final totalTrips = tripsSnapshot.count;

      // Get trips count for today
      final tripsTodaySnapshot = await _firestore
          .collection('tripRequests')
          .where('createdAt',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .count()
          .get();
      final tripsToday = tripsTodaySnapshot.count;

      // Calculate total revenue from completed trips and wallet deposits
      double totalRevenue = 0.0;
      double revenueToday = 0.0;

      final completedTripsSnapshot = await _firestore
          .collection('tripRequests')
          .where('status', isEqualTo: 'completed')
          .get();

      for (final doc in completedTripsSnapshot.docs) {
        final data = doc.data();
        final amount = (data['fare'] ?? 0.0).toDouble();
        totalRevenue += amount;

        final tripDate = (data['completedAt'] as Timestamp?)?.toDate();
        if (tripDate != null &&
            tripDate.year == today.year &&
            tripDate.month == today.month &&
            tripDate.day == today.day) {
          revenueToday += amount;
        }
      }

      // Also check wallet transactions for additional revenue data
      final walletTransactionsSnapshot = await _firestore
          .collection('walletTransactions')
          .where('type', isEqualTo: 'deposit')
          .where('status', isEqualTo: 'completed')
          .get();

      for (final doc in walletTransactionsSnapshot.docs) {
        final data = doc.data();
        final amount = (data['amount'] ?? 0.0).toDouble();
        totalRevenue += amount;

        final transactionDate = (data['timestamp'] as Timestamp?)?.toDate();
        if (transactionDate != null &&
            transactionDate.year == today.year &&
            transactionDate.month == today.month &&
            transactionDate.day == today.day) {
          revenueToday += amount;
        }
      }

      stats.value = DashboardStats(
        totalUsers: totalUsers ?? 0,
        newUsers: newUsers ?? 0,
        totalDrivers: totalDrivers ?? 0,
        newDrivers: newDrivers ?? 0,
        totalTrips: totalTrips ?? 0,
        tripsToday: tripsToday ?? 0,
        totalRevenue: totalRevenue,
        revenueToday: revenueToday,
      );
    } catch (e) {
      Get.log('Error loading stats: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل البيانات الإحصائية',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      // Initialize with zeros instead of mock data
      stats.value = DashboardStats(
        totalUsers: 0,
        newUsers: 0,
        totalDrivers: 0,
        newDrivers: 0,
        totalTrips: 0,
        tripsToday: 0,
        totalRevenue: 0.0,
        revenueToday: 0.0,
      );
    }
  }

  Future<void> _loadRecentTrips() async {
    try {
      final snapshot = await _firestore
          .collection('tripRequests')
          .orderBy('createdAt', descending: true)
          .limit(5)
          .get();

      final List<Map<String, dynamic>> trips = [];

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final createdAt = (data['createdAt'] as Timestamp).toDate();

        trips.add({
          'id': doc.id,
          'origin': data['originAddress'] ?? 'Unknown',
          'destination': data['destinationAddress'] ?? 'Unknown',
          'date': intl.DateFormat('yyyy/MM/dd').format(createdAt),
          'time': intl.DateFormat('HH:mm').format(createdAt),
          'amount': (data['fare'] ?? 0.0).toStringAsFixed(2),
          'status': data['status'] ?? 'pending',
        });
      }

      recentTrips.value = trips;

      // If no trips found, just keep the empty list
    } catch (e) {
      Get.log('Error loading recent trips: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات الرحلات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      recentTrips.value = [];
    }
  }

  Future<void> _loadActiveDrivers() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'driver')
          // .where('isOnline', isEqualTo: true)
          .limit(5)
          .get();

      final List<Map<String, dynamic>> drivers = [];

      for (final doc in snapshot.docs) {
        final data = doc.data();

        drivers.add({
          'id': doc.id,
          'name': data['name'] ?? 'Unknown',
          'profileImageUrl': data['profileImageUrl'],
          'rating': (data['rating'] ?? 0.0).toDouble(),
          'ratingCount': data['ratingCount'] ?? 0,
          'status': 'متصل',
        });
      }

      activeDrivers.value = drivers;

      // If no active drivers found, just keep the empty list
    } catch (e) {
      Get.log('Error loading active drivers: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات السائقين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      activeDrivers.value = [];
    }
  }

  Future<void> _loadSupportTickets() async {
    try {
      final snapshot = await _firestore
          .collection('supportTickets')
          .where('status', whereIn: ['new', 'open', 'pending'])
          .orderBy('createdAt', descending: true)
          .limit(5)
          .get();

      final List<Map<String, dynamic>> tickets = [];

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final createdAt = (data['createdAt'] as Timestamp).toDate();

        tickets.add({
          'id': doc.id,
          'subject': data['subject'] ?? 'No Subject',
          'date': intl.DateFormat('yyyy/MM/dd').format(createdAt),
          'time': intl.DateFormat('HH:mm').format(createdAt),
          'status': data['status'] ?? 'new',
          'priority': data['priority'] ?? 'medium',
        });
      }

      supportTickets.value = tickets;

      // If no tickets found, just keep the empty list
    } catch (e) {
      Get.log('Error loading support tickets: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات تذاكر الدعم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      supportTickets.value = [];
    }
  }

  void updateChartData() {
    final List<ChartData> data = [];
    final List<String> labels = [];

    // Generate chart data based on selected period
    switch (selectedChartPeriod.value) {
      case 'week':
        _generateWeeklyChartData(data, labels);
        break;
      case 'month':
        _generateMonthlyChartData(data, labels);
        break;
      case 'year':
        _generateYearlyChartData(data, labels);
        break;
    }

    chartData.value = data;
    chartLabels.value = labels;

    // Calculate max Y value for chart
    double maxY = 0;
    for (final point in data) {
      if (point.y > maxY) {
        maxY = point.y;
      }
    }

    // Round up to nearest 100 or 1000 for better visualization
    if (maxY > 1000) {
      chartMaxY.value = ((maxY ~/ 1000) + 1) * 1000.0;
    } else if (maxY > 100) {
      chartMaxY.value = ((maxY ~/ 100) + 1) * 100.0;
    } else {
      chartMaxY.value = ((maxY ~/ 10) + 1) * 10.0;
    }

    // Ensure minimum value for better visualization
    if (chartMaxY.value < 100) {
      chartMaxY.value = 100;
    }
  }

  void _generateWeeklyChartData(List<ChartData> data, List<String> labels) {
    final now = DateTime.now();
    final dayFormat = intl.DateFormat('E'); // Day abbreviation (Sun, Mon, etc.)

    // Generate data for the last 7 days
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayLabel = dayFormat.format(date);

      // Calculate revenue for this day from our stats
      double revenue = 0.0;

      // We'll use the revenueToday value for today, and estimate for other days
      if (i == 0) {
        revenue = stats.value.revenueToday;
      } else {
        // For past days, we'll use a formula based on total revenue
        // This is still an estimate but better than random data
        revenue = stats.value.totalRevenue / 30 * (1 - (i * 0.05));
      }

      data.add(ChartData(6 - i, revenue));
      labels.add(dayLabel);
    }
  }

  void _generateMonthlyChartData(List<ChartData> data, List<String> labels) {
    final now = DateTime.now();
    final dayFormat = intl.DateFormat('d'); // Day of month (1-31)

    // Generate data for the last 30 days, grouped by week
    for (int i = 0; i < 4; i++) {
      final weekStart = now.subtract(Duration(days: now.weekday + (i * 7)));
      final weekEnd = weekStart.add(const Duration(days: 6));

      final startLabel = dayFormat.format(weekStart);
      final endLabel = dayFormat.format(weekEnd);
      final weekLabel = '$startLabel-$endLabel';

      // Calculate revenue for this week based on our stats
      // We'll use a formula based on total revenue
      final revenue = stats.value.totalRevenue / 4 * (1 - (i * 0.1));

      data.add(ChartData(i, revenue));
      labels.add(weekLabel);
    }
  }

  void _generateYearlyChartData(List<ChartData> data, List<String> labels) {
    final monthFormat =
        intl.DateFormat('MMM'); // Month abbreviation (Jan, Feb, etc.)
    final now = DateTime.now();

    // Generate data for the last 12 months
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthLabel = monthFormat.format(month);

      // Calculate revenue for this month based on our stats
      // We'll use a formula based on total revenue
      final revenue = stats.value.totalRevenue / 12 * (1 + ((12 - i) * 0.05));

      data.add(ChartData(11 - i, revenue));
      labels.add(monthLabel);
    }
  }

  void showNotifications() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('الإشعارات'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: [
                _buildNotificationItem(
                  'تم تسجيل مستخدم جديد',
                  'قام مستخدم جديد بالتسجيل في التطبيق',
                  DateTime.now().subtract(const Duration(minutes: 5)),
                ),
                _buildNotificationItem(
                  'تذكرة دعم جديدة',
                  'تم إنشاء تذكرة دعم جديدة بأولوية عالية',
                  DateTime.now().subtract(const Duration(hours: 1)),
                ),
                _buildNotificationItem(
                  'إكمال 100 رحلة',
                  'تم إكمال 100 رحلة اليوم',
                  DateTime.now().subtract(const Duration(hours: 3)),
                ),
                _buildNotificationItem(
                  'تحديث النظام',
                  'تم تحديث النظام إلى الإصدار الجديد',
                  DateTime.now().subtract(const Duration(days: 1)),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                Get.snackbar(
                  'تم',
                  'تم تعليم جميع الإشعارات كمقروءة',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              },
              child: const Text('تعليم الكل كمقروء'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(String title, String message, DateTime time) {
    final timeAgo = _getTimeAgo(time);

    return ListTile(
      leading: const CircleAvatar(
        child: Icon(Icons.notifications),
      ),
      title: Text(title),
      subtitle: Text(message),
      trailing: Text(
        timeAgo,
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 12,
        ),
      ),
      onTap: () => Get.back(),
    );
  }

  String _getTimeAgo(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inSeconds < 60) {
      return 'منذ ${difference.inSeconds} ثانية';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  void showQuickActions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'إجراءات سريعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildQuickActionItem(
                icon: Icons.person_add,
                title: 'إضافة مستخدم جديد',
                onTap: () {
                  Get.back();
                  navigateToAddUser();
                },
              ),
              _buildQuickActionItem(
                icon: Icons.drive_eta,
                title: 'إضافة سائق جديد',
                onTap: () {
                  Get.back();
                  navigateToAddDriver();
                },
              ),
              _buildQuickActionItem(
                icon: Icons.support_agent,
                title: 'الرد على تذاكر الدعم',
                onTap: () {
                  Get.back();
                  navigateToSupportManagement();
                },
              ),
              _buildQuickActionItem(
                icon: Icons.bar_chart,
                title: 'عرض تقارير مفصلة',
                onTap: () {
                  Get.back();
                  navigateToReports();
                },
              ),
              _buildQuickActionItem(
                icon: Icons.local_offer,
                title: 'إدارة الكوبونات',
                onTap: () {
                  Get.back();
                  navigateToCouponsManagement();
                },
              ),
              _buildQuickActionItem(
                icon: Icons.directions,
                title: 'إدارة الوجهات',
                onTap: () {
                  Get.back();
                  navigateToDirectionsManagement();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: onTap,
    );
  }

  void showTripDetails(String tripId) {
    // In a real app, this would fetch the trip details from Firestore
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('تفاصيل الرحلة'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('جاري تحميل التفاصيل...'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                navigateToTripsManagement();
              },
              child: const Text('عرض كل الرحلات'),
            ),
          ],
        ),
      ),
    );
  }

  void showDriverDetails(String driverId) {
    // In a real app, this would fetch the driver details from Firestore
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('تفاصيل السائق'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('جاري تحميل التفاصيل...'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                navigateToDriversManagement();
              },
              child: const Text('عرض كل السائقين'),
            ),
          ],
        ),
      ),
    );
  }

  void showTicketDetails(String ticketId) {
    // In a real app, this would fetch the ticket details from Firestore
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('تفاصيل التذكرة'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('جاري تحميل التفاصيل...'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                navigateToSupportManagement();
              },
              child: const Text('عرض كل التذاكر'),
            ),
          ],
        ),
      ),
    );
  }

  void navigateToUsersManagement({String? filter}) {
    Get.toNamed(Routes.ADMIN_USERS, arguments: {'filter': filter});
  }

  void navigateToDriversManagement({String? filter}) {
    Get.toNamed(Routes.ADMIN_DRIVERS, arguments: {'filter': filter});
  }

  void navigateToDriversMap() {
    Get.toNamed(Routes.ADMIN_DRIVERS_MAP);
  }

  void navigateToTripsManagement({String? filter}) {
    Get.toNamed(Routes.ADMIN_TRIPS, arguments: {'filter': filter});
  }

  void navigateToPaymentsManagement({String? filter}) {
    Get.toNamed(Routes.ADMIN_PAYMENTS, arguments: {'filter': filter});
  }

  void navigateToSupportManagement({String? filter}) {
    Get.toNamed(Routes.ADMIN_SUPPORT, arguments: {'filter': filter});
  }

  void navigateToFAQManagement() {
    Get.toNamed(Routes.ADMIN_FAQ);
  }

  void navigateToSettings() {
    Get.toNamed(Routes.ADMIN_SETTINGS);
  }

  void navigateToAppSettings() {
    Get.toNamed(Routes.ADMIN_APP_SETTINGS);
  }

  void navigateToAddUser() {
    Get.toNamed(Routes.ADMIN_ADD_USER);
  }

  void navigateToAddDriver() {
    Get.toNamed(Routes.ADMIN_ADD_DRIVER);
  }

  void navigateToReports() {
    Get.toNamed(Routes.ADMIN_REPORTS);
  }

  void navigateToDirectionsManagement() {
    Get.toNamed(Routes.ADMIN_DIRECTIONS);
  }

  void navigateToCouponsManagement() {
    Get.toNamed(Routes.ADMIN_COUPONS);
  }

  void logout() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                await _auth.signOut();
                Get.offAllNamed(Routes.LOGIN);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ],
        ),
      ),
    );
  }

  //navigateToLocationsManagement
  void navigateToLocationsManagement() {
    Get.toNamed(Routes.ADMIN_DIRECTIONS);
  }
}
