import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class AdminPaymentsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final TextEditingController searchController = TextEditingController();
  final RxList<Map<String, dynamic>> payments = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> allPayments = <Map<String, dynamic>>[].obs;
  final RxString searchQuery = ''.obs;
  final RxString filterOption = 'all'.obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  // Statistics
  final RxDouble totalRevenue = 0.0.obs;
  final RxDouble todayRevenue = 0.0.obs;
  final RxDouble weekRevenue = 0.0.obs;
  final RxDouble monthRevenue = 0.0.obs;
  final RxInt totalTransactions = 0.obs;

  @override
  void onInit() {
    super.onInit();
    
    // Check if we have a filter parameter from arguments
    if (Get.arguments != null && Get.arguments['filter'] != null) {
      filterOption.value = Get.arguments['filter'];
    }
    
    loadPayments();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadPayments() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      // Load from both trip payments and wallet transactions
      await Future.wait([
        _loadTripPayments(),
        _loadWalletTransactions(),
      ]);

      // Calculate statistics
      _calculateStatistics();
      
      // Apply filters
      applyFilters();
      
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل بيانات المدفوعات: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات المدفوعات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      allPayments.value = [];
      payments.value = [];
      _resetStatistics();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _loadTripPayments() async {
    final snapshot = await _firestore
        .collection('tripRequests')
        .where('status', isEqualTo: 'completed')
        .orderBy('completedAt', descending: true)
        .get();

    for (final doc in snapshot.docs) {
      final data = doc.data();
      final completedAt = (data['completedAt'] as Timestamp?)?.toDate();
      
      if (completedAt != null) {
        final amount = (data['fare'] ?? 0.0).toDouble();
        
        allPayments.add({
          'id': doc.id,
          'type': 'trip',
          'amount': amount,
          'date': intl.DateFormat('yyyy/MM/dd').format(completedAt),
          'time': intl.DateFormat('HH:mm').format(completedAt),
          'status': 'completed',
          'userId': data['userId'] ?? '',
          'driverId': data['driverId'] ?? '',
          'description': 'رسوم رحلة',
          'timestamp': completedAt,
        });
      }
    }
  }

  Future<void> _loadWalletTransactions() async {
    final snapshot = await _firestore
        .collection('walletTransactions')
        .orderBy('timestamp', descending: true)
        .get();

    for (final doc in snapshot.docs) {
      final data = doc.data();
      final timestamp = (data['timestamp'] as Timestamp?)?.toDate();
      
      if (timestamp != null) {
        final amount = (data['amount'] ?? 0.0).toDouble();
        final type = data['type'] ?? 'unknown';
        final status = data['status'] ?? 'pending';
        
        allPayments.add({
          'id': doc.id,
          'type': 'wallet_${type}',
          'amount': amount,
          'date': intl.DateFormat('yyyy/MM/dd').format(timestamp),
          'time': intl.DateFormat('HH:mm').format(timestamp),
          'status': status,
          'userId': data['userId'] ?? '',
          'description': _getTransactionDescription(type),
          'paymentMethod': data['paymentMethod'] ?? 'unknown',
          'timestamp': timestamp,
        });
      }
    }
  }

  String _getTransactionDescription(String type) {
    switch (type) {
      case 'deposit':
        return 'إيداع رصيد';
      case 'withdrawal':
        return 'سحب رصيد';
      case 'refund':
        return 'استرداد مبلغ';
      case 'payment':
        return 'دفع رسوم';
      default:
        return 'معاملة مالية';
    }
  }

  void _calculateStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final monthStart = DateTime(now.year, now.month, 1);
    
    double total = 0.0;
    double todayTotal = 0.0;
    double weekTotal = 0.0;
    double monthTotal = 0.0;
    
    for (final payment in allPayments) {
      final timestamp = payment['timestamp'] as DateTime;
      final amount = payment['amount'] as double;
      final type = payment['type'] as String;
      final status = payment['status'] as String;
      
      // Only count completed transactions and deposits
      if (status == 'completed' && 
          (type == 'trip' || type == 'wallet_deposit')) {
        total += amount;
        
        if (timestamp.isAfter(today) || 
            timestamp.year == today.year && 
            timestamp.month == today.month && 
            timestamp.day == today.day) {
          todayTotal += amount;
        }
        
        if (timestamp.isAfter(weekStart)) {
          weekTotal += amount;
        }
        
        if (timestamp.isAfter(monthStart)) {
          monthTotal += amount;
        }
      }
    }
    
    totalRevenue.value = total;
    todayRevenue.value = todayTotal;
    weekRevenue.value = weekTotal;
    monthRevenue.value = monthTotal;
    totalTransactions.value = allPayments.length;
  }

  void _resetStatistics() {
    totalRevenue.value = 0.0;
    todayRevenue.value = 0.0;
    weekRevenue.value = 0.0;
    monthRevenue.value = 0.0;
    totalTransactions.value = 0;
  }

  Future<void> refreshData() async {
    allPayments.clear();
    await loadPayments();
  }

  void onSearchChanged(String query) {
    searchQuery.value = query;
    applyFilters();
  }

  void clearSearch() {
    searchQuery.value = '';
    searchController.clear();
    applyFilters();
  }

  void setFilter(String filter) {
    filterOption.value = filter;
    applyFilters();
  }

  void applyFilters() {
    List<Map<String, dynamic>> filteredPayments = List.from(allPayments);

    // Apply type filter
    if (filterOption.value != 'all') {
      filteredPayments = filteredPayments.where((payment) {
        if (filterOption.value == 'trips') {
          return payment['type'] == 'trip';
        } else if (filterOption.value == 'deposits') {
          return payment['type'] == 'wallet_deposit';
        } else if (filterOption.value == 'withdrawals') {
          return payment['type'] == 'wallet_withdrawal';
        } else if (filterOption.value == 'refunds') {
          return payment['type'] == 'wallet_refund';
        }
        return true;
      }).toList();
    }

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filteredPayments = filteredPayments.where((payment) {
        return payment['id'].toString().toLowerCase().contains(query) ||
               payment['userId'].toString().toLowerCase().contains(query) ||
               (payment['driverId']?.toString().toLowerCase() ?? '').contains(query) ||
               payment['description'].toString().toLowerCase().contains(query) ||
               payment['amount'].toString().contains(query);
      }).toList();
    }

    payments.value = filteredPayments;
  }

  void showSearchDialog() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('البحث عن معاملة'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'اكتب معرف المعاملة أو المستخدم أو المبلغ',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: onSearchChanged,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                clearSearch();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                onSearchChanged(searchController.text);
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }

  void showFilterOptions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'تصفية المدفوعات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.all_inclusive),
                title: const Text('جميع المعاملات'),
                trailing: filterOption.value == 'all'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('all');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.directions_car),
                title: const Text('رسوم الرحلات'),
                trailing: filterOption.value == 'trips'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('trips');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.add_circle_outline),
                title: const Text('إيداعات المحفظة'),
                trailing: filterOption.value == 'deposits'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('deposits');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.remove_circle_outline),
                title: const Text('سحوبات المحفظة'),
                trailing: filterOption.value == 'withdrawals'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('withdrawals');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.replay),
                title: const Text('استردادات'),
                trailing: filterOption.value == 'refunds'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('refunds');
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void showPaymentDetails(String paymentId) {
    final payment = payments.firstWhere((p) => p['id'] == paymentId, orElse: () => {});
    if (payment.isEmpty) return;

    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: Text('تفاصيل المعاملة: ${payment['id']}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailItem('النوع', payment['description']),
              _buildDetailItem('المبلغ', '${payment['amount']} ر.س'),
              _buildDetailItem('التاريخ', payment['date']),
              _buildDetailItem('الوقت', payment['time']),
              _buildDetailItem('الحالة', _getStatusText(payment['status'])),
              if (payment['userId'] != null && payment['userId'].isNotEmpty)
                _buildDetailItem('معرف المستخدم', payment['userId']),
              if (payment['driverId'] != null && payment['driverId'].isNotEmpty)
                _buildDetailItem('معرف السائق', payment['driverId']),
              if (payment['paymentMethod'] != null)
                _buildDetailItem('طريقة الدفع', _getPaymentMethodText(payment['paymentMethod'])),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'قيد الانتظار';
      case 'failed':
        return 'فشلت';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }

  String _getPaymentMethodText(String method) {
    switch (method) {
      case 'credit_card':
        return 'بطاقة ائتمان';
      case 'debit_card':
        return 'بطاقة خصم';
      case 'wallet':
        return 'محفظة إلكترونية';
      case 'cash':
        return 'نقداً';
      default:
        return method;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    if (type == 'trip') {
      return Icons.directions_car;
    } else if (type == 'wallet_deposit') {
      return Icons.add_circle_outline;
    } else if (type == 'wallet_withdrawal') {
      return Icons.remove_circle_outline;
    } else if (type == 'wallet_refund') {
      return Icons.replay;
    } else {
      return Icons.attach_money;
    }
  }
}
