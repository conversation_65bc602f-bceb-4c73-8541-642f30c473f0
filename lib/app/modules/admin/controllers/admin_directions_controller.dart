import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../data/models/direction_model.dart';

class AdminDirectionsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Observable state
  final RxList<Direction> directions = <Direction>[].obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  
  @override
  void onInit() {
    super.onInit();
    loadDirections();
  }
  
  Future<void> loadDirections() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      final snapshot = await _firestore
          .collection('directions')
          .orderBy('title')
          .get();
      
      final List<Direction> loadedDirections = snapshot.docs
          .map((doc) => Direction.fromFirestore(doc))
          .toList();
      
      directions.value = loadedDirections;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الوجهات: $e';
      print('Error loading directions: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> addDirection(Direction direction) async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Add to Firestore
      final docRef = await _firestore
          .collection('directions')
          .add(direction.toFirestore());
      
      // Create a new direction with the generated ID
      final newDirection = Direction(
        id: docRef.id,
        title: direction.title,
        description: direction.description,
        basePrice: direction.basePrice,
        pricePerKm: direction.pricePerKm,
        startLocation: direction.startLocation,
        endLocation: direction.endLocation,
        estimatedDistance: direction.estimatedDistance,
        estimatedDuration: direction.estimatedDuration,
        isActive: direction.isActive,
        createdAt: direction.createdAt,
        updatedAt: direction.updatedAt,
        imageUrl: direction.imageUrl,
      );
      
      // Add to local list
      directions.add(newDirection);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إضافة الوجهة: $e';
      print('Error adding direction: $e');
      throw e; // Re-throw to handle in UI
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> updateDirection(Direction direction) async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Update in Firestore
      await _firestore
          .collection('directions')
          .doc(direction.id)
          .update(direction.toFirestore());
      
      // Update in local list
      final index = directions.indexWhere((d) => d.id == direction.id);
      if (index >= 0) {
        directions[index] = direction;
        directions.refresh();
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث الوجهة: $e';
      print('Error updating direction: $e');
      throw e; // Re-throw to handle in UI
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> updateDirectionStatus(String id, bool isActive) async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Update in Firestore
      await _firestore
          .collection('directions')
          .doc(id)
          .update({
            'isActive': isActive,
            'updatedAt': FieldValue.serverTimestamp(),
          });
      
      // Update in local list
      final index = directions.indexWhere((d) => d.id == id);
      if (index >= 0) {
        final updatedDirection = directions[index].copyWith(
          isActive: isActive,
          updatedAt: Timestamp.now(),
        );
        directions[index] = updatedDirection;
        directions.refresh();
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث حالة الوجهة: $e';
      print('Error updating direction status: $e');
      throw e; // Re-throw to handle in UI
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> deleteDirection(String id) async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Delete from Firestore
      await _firestore
          .collection('directions')
          .doc(id)
          .delete();
      
      // Remove from local list
      directions.removeWhere((d) => d.id == id);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف الوجهة: $e';
      print('Error deleting direction: $e');
      throw e; // Re-throw to handle in UI
    } finally {
      isLoading.value = false;
    }
  }
  
  // Create a seeder for initial directions data
  Future<void> seedDirections() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Check if directions already exist
      final snapshot = await _firestore.collection('directions').limit(1).get();
      if (snapshot.docs.isNotEmpty) {
        print('Directions already exist, skipping seeding');
        return;
      }
      
      // Sample directions data
      final List<Direction> sampleDirections = [
        Direction(
          id: '',
          title: 'غار حراء',
          description: 'رحلة إلى غار حراء التاريخي',
          basePrice: 150.0,
          pricePerKm: 5.0,
          startLocation: const GeoPoint(21.3891, 39.8579), // Mecca
          endLocation: const GeoPoint(21.4614, 39.8741), // Hira Cave
          estimatedDistance: 15.0,
          estimatedDuration: 30,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
        Direction(
          id: '',
          title: 'المسجد النبوي',
          description: 'رحلة إلى المسجد النبوي الشريف',
          basePrice: 200.0,
          pricePerKm: 6.0,
          startLocation: const GeoPoint(21.3891, 39.8579), // Mecca
          endLocation: const GeoPoint(24.4672, 39.6112), // Medina
          estimatedDistance: 450.0,
          estimatedDuration: 300,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
        Direction(
          id: '',
          title: 'جبل أحد',
          description: 'رحلة إلى جبل أحد التاريخي',
          basePrice: 120.0,
          pricePerKm: 4.0,
          startLocation: const GeoPoint(24.4672, 39.6112), // Medina
          endLocation: const GeoPoint(24.4822, 39.6291), // Mount Uhud
          estimatedDistance: 8.0,
          estimatedDuration: 20,
          isActive: true,
          createdAt: Timestamp.now(),
        ),
      ];
      
      // Add sample directions to Firestore
      for (final direction in sampleDirections) {
        await _firestore
            .collection('directions')
            .add(direction.toFirestore());
      }
      
      print('Successfully seeded ${sampleDirections.length} directions');
      
      // Reload directions
      await loadDirections();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إضافة البيانات الأولية: $e';
      print('Error seeding directions: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
