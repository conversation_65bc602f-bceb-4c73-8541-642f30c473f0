import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminFAQController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final TextEditingController searchController = TextEditingController();
  final TextEditingController questionController = TextEditingController();
  final TextEditingController answerController = TextEditingController();
  
  final RxList<Map<String, dynamic>> faqs = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> allFaqs = <Map<String, dynamic>>[].obs;
  final RxString searchQuery = ''.obs;
  final RxString selectedCategory = 'all'.obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  
  // For editing
  final RxBool isEditing = false.obs;
  final RxString currentFaqId = ''.obs;
  final RxString selectedFaqCategory = 'general'.obs;
  
  // Available categories
  final List<String> categories = [
    'general',
    'payment',
    'trips',
    'account',
    'drivers',
    'technical',
  ];
  
  @override
  void onInit() {
    super.onInit();
    loadFAQs();
  }
  
  @override
  void onClose() {
    searchController.dispose();
    questionController.dispose();
    answerController.dispose();
    super.onClose();
  }
  
  Future<void> loadFAQs() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      final snapshot = await _firestore
          .collection('faqs')
          .orderBy('order')
          .get();
      
      final List<Map<String, dynamic>> loadedFaqs = [];
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        
        loadedFaqs.add({
          'id': doc.id,
          'question': data['question'] ?? '',
          'answer': data['answer'] ?? '',
          'category': data['category'] ?? 'general',
          'order': data['order'] ?? 0,
          'isActive': data['isActive'] ?? true,
        });
      }
      
      allFaqs.value = loadedFaqs;
      applyFilters();
      
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الأسئلة الشائعة: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل الأسئلة الشائعة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      allFaqs.value = [];
      faqs.value = [];
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> refreshData() async {
    await loadFAQs();
  }
  
  void onSearchChanged(String query) {
    searchQuery.value = query;
    applyFilters();
  }
  
  void clearSearch() {
    searchQuery.value = '';
    searchController.clear();
    applyFilters();
  }
  
  void setCategory(String category) {
    selectedCategory.value = category;
    applyFilters();
  }
  
  void applyFilters() {
    List<Map<String, dynamic>> filteredFaqs = List.from(allFaqs);
    
    // Apply category filter
    if (selectedCategory.value != 'all') {
      filteredFaqs = filteredFaqs.where((faq) {
        return faq['category'] == selectedCategory.value;
      }).toList();
    }
    
    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filteredFaqs = filteredFaqs.where((faq) {
        return faq['question'].toString().toLowerCase().contains(query) ||
               faq['answer'].toString().toLowerCase().contains(query);
      }).toList();
    }
    
    faqs.value = filteredFaqs;
  }
  
  void showSearchDialog() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('البحث في الأسئلة الشائعة'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'اكتب كلمة للبحث في الأسئلة أو الأجوبة',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: onSearchChanged,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                clearSearch();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                onSearchChanged(searchController.text);
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }
  
  void showAddEditFAQDialog({Map<String, dynamic>? faq}) {
    // Reset controllers
    questionController.clear();
    answerController.clear();
    
    // Set editing mode
    isEditing.value = faq != null;
    currentFaqId.value = faq?['id'] ?? '';
    selectedFaqCategory.value = faq?['category'] ?? 'general';
    
    // Fill controllers if editing
    if (isEditing.value) {
      questionController.text = faq!['question'];
      answerController.text = faq['answer'];
    }
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: Text(isEditing.value ? 'تعديل سؤال' : 'إضافة سؤال جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: questionController,
                  decoration: const InputDecoration(
                    labelText: 'السؤال',
                    hintText: 'اكتب السؤال هنا',
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: answerController,
                  decoration: const InputDecoration(
                    labelText: 'الإجابة',
                    hintText: 'اكتب الإجابة هنا',
                  ),
                  maxLines: 5,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedFaqCategory.value,
                  decoration: const InputDecoration(
                    labelText: 'التصنيف',
                  ),
                  items: categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(_getCategoryText(category)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      selectedFaqCategory.value = value;
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (questionController.text.trim().isEmpty || 
                    answerController.text.trim().isEmpty) {
                  Get.snackbar(
                    'خطأ',
                    'يرجى ملء جميع الحقول',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }
                
                if (isEditing.value) {
                  updateFAQ();
                } else {
                  addFAQ();
                }
                
                Get.back();
              },
              child: Text(isEditing.value ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> addFAQ() async {
    isLoading.value = true;
    
    try {
      // Get the next order number
      int nextOrder = 0;
      if (allFaqs.isNotEmpty) {
        nextOrder = allFaqs.map((faq) => faq['order'] as int).reduce((a, b) => a > b ? a : b) + 1;
      }
      
      // Add new FAQ
      await _firestore.collection('faqs').add({
        'question': questionController.text.trim(),
        'answer': answerController.text.trim(),
        'category': selectedFaqCategory.value,
        'order': nextOrder,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      Get.snackbar(
        'تم بنجاح',
        'تمت إضافة السؤال بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload FAQs
      await loadFAQs();
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة السؤال: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> updateFAQ() async {
    isLoading.value = true;
    
    try {
      // Update FAQ
      await _firestore.collection('faqs').doc(currentFaqId.value).update({
        'question': questionController.text.trim(),
        'answer': answerController.text.trim(),
        'category': selectedFaqCategory.value,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      Get.snackbar(
        'تم بنجاح',
        'تم تحديث السؤال بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload FAQs
      await loadFAQs();
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث السؤال: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> toggleFAQStatus(String faqId, bool currentStatus) async {
    isLoading.value = true;
    
    try {
      // Toggle status
      await _firestore.collection('faqs').doc(faqId).update({
        'isActive': !currentStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      Get.snackbar(
        'تم بنجاح',
        'تم تغيير حالة السؤال بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload FAQs
      await loadFAQs();
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تغيير حالة السؤال: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> deleteFAQ(String faqId) async {
    isLoading.value = true;
    
    try {
      // Delete FAQ
      await _firestore.collection('faqs').doc(faqId).delete();
      
      Get.snackbar(
        'تم بنجاح',
        'تم حذف السؤال بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload FAQs
      await loadFAQs();
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حذف السؤال: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> reorderFAQs(int oldIndex, int newIndex) async {
    if (oldIndex == newIndex) return;
    
    // Adjust for removing and inserting
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    
    final item = faqs.removeAt(oldIndex);
    faqs.insert(newIndex, item);
    
    // Update order in Firestore
    isLoading.value = true;
    
    try {
      final batch = _firestore.batch();
      
      for (int i = 0; i < faqs.length; i++) {
        final faq = faqs[i];
        batch.update(
          _firestore.collection('faqs').doc(faq['id']),
          {'order': i},
        );
      }
      
      await batch.commit();
      
      // Reload FAQs to get the updated order
      await loadFAQs();
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إعادة ترتيب الأسئلة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void showDeleteConfirmation(String faqId) {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من رغبتك في حذف هذا السؤال؟ لا يمكن التراجع عن هذا الإجراء.'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                deleteFAQ(faqId);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('حذف'),
            ),
          ],
        ),
      ),
    );
  }
  
  String _getCategoryText(String category) {
    switch (category) {
      case 'general':
        return 'عام';
      case 'payment':
        return 'الدفع';
      case 'trips':
        return 'الرحلات';
      case 'account':
        return 'الحساب';
      case 'drivers':
        return 'السائقين';
      case 'technical':
        return 'مشاكل تقنية';
      default:
        return category;
    }
  }
  
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'general':
        return Colors.blue;
      case 'payment':
        return Colors.green;
      case 'trips':
        return Colors.purple;
      case 'account':
        return Colors.orange;
      case 'drivers':
        return Colors.teal;
      case 'technical':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'general':
        return Icons.info_outline;
      case 'payment':
        return Icons.attach_money;
      case 'trips':
        return Icons.directions_car;
      case 'account':
        return Icons.person;
      case 'drivers':
        return Icons.drive_eta;
      case 'technical':
        return Icons.build;
      default:
        return Icons.help_outline;
    }
  }
}
