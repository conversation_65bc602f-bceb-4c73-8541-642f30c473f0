import 'package:ai_delivery_app/app/data/models/coupon_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminCouponsController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Observable state
  final RxList<Coupon> coupons = <Coupon>[].obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  
  // Form controllers
  late TextEditingController codeController;
  late TextEditingController titleController;
  late TextEditingController descriptionController;
  late TextEditingController discountValueController;
  late TextEditingController minimumOrderController;
  late TextEditingController maximumDiscountController;
  late TextEditingController usageLimitController;
  late TextEditingController usageLimitPerUserController;
  late TextEditingController newUserDaysController;
  
  // Selected values
  final Rx<DiscountType> selectedDiscountType = DiscountType.percentage.obs;
  final Rx<UserType> selectedUserType = UserType.all.obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(DateTime.now());
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxBool isActive = true.obs;
  
  @override
  void onInit() {
    super.onInit();
    initControllers();
    loadCoupons();
  }
  
  @override
  void onClose() {
    disposeControllers();
    super.onClose();
  }
  
  void initControllers() {
    codeController = TextEditingController();
    titleController = TextEditingController();
    descriptionController = TextEditingController();
    discountValueController = TextEditingController();
    minimumOrderController = TextEditingController();
    maximumDiscountController = TextEditingController();
    usageLimitController = TextEditingController();
    usageLimitPerUserController = TextEditingController();
    newUserDaysController = TextEditingController();
  }
  
  void disposeControllers() {
    codeController.dispose();
    titleController.dispose();
    descriptionController.dispose();
    discountValueController.dispose();
    minimumOrderController.dispose();
    maximumDiscountController.dispose();
    usageLimitController.dispose();
    usageLimitPerUserController.dispose();
    newUserDaysController.dispose();
  }
  
  void resetForm() {
    codeController.clear();
    titleController.clear();
    descriptionController.clear();
    discountValueController.clear();
    minimumOrderController.clear();
    maximumDiscountController.clear();
    usageLimitController.clear();
    usageLimitPerUserController.clear();
    newUserDaysController.clear();
    
    selectedDiscountType.value = DiscountType.percentage;
    selectedUserType.value = UserType.all;
    startDate.value = DateTime.now();
    endDate.value = null;
    isActive.value = true;
  }
  
  Future<void> loadCoupons() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      final snapshot = await _firestore
          .collection('coupons')
          .orderBy('createdAt', descending: true)
          .get();
      
      final List<Coupon> loadedCoupons = snapshot.docs
          .map((doc) => Coupon.fromFirestore(doc))
          .toList();
      
      coupons.value = loadedCoupons;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الكوبونات: $e';
      print('Error loading coupons: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> addCoupon() async {
    if (!validateCouponForm()) return;
    
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Check if coupon code already exists
      final existingCoupon = await _firestore
          .collection('coupons')
          .where('code', isEqualTo: codeController.text.trim())
          .get();
      
      if (existingCoupon.docs.isNotEmpty) {
        Get.snackbar(
          'خطأ',
          'كود الكوبون موجود بالفعل، يرجى استخدام كود آخر',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }
      
      // Create new coupon
      final newCoupon = Coupon(
        id: '',
        code: codeController.text.trim(),
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        discountType: selectedDiscountType.value,
        discountValue: double.parse(discountValueController.text),
        minimumOrderAmount: minimumOrderController.text.isNotEmpty
            ? double.parse(minimumOrderController.text)
            : null,
        maximumDiscountAmount: maximumDiscountController.text.isNotEmpty
            ? double.parse(maximumDiscountController.text)
            : null,
        startDate: Timestamp.fromDate(startDate.value!),
        endDate: endDate.value != null ? Timestamp.fromDate(endDate.value!) : null,
        usageLimit: usageLimitController.text.isNotEmpty
            ? int.parse(usageLimitController.text)
            : null,
        usageLimitPerUser: usageLimitPerUserController.text.isNotEmpty
            ? int.parse(usageLimitPerUserController.text)
            : null,
        usageCount: 0,
        isActive: isActive.value,
        userType: selectedUserType.value,
        newUserDays: newUserDaysController.text.isNotEmpty && selectedUserType.value == UserType.new_users
            ? int.parse(newUserDaysController.text)
            : null,
        specificUserIds: selectedUserType.value == UserType.specific_users ? [] : null,
        createdAt: Timestamp.now(),
        createdBy: _auth.currentUser?.uid,
      );
      
      // Add to Firestore
      final docRef = await _firestore
          .collection('coupons')
          .add(newCoupon.toFirestore());
      
      Get.snackbar(
        'تم بنجاح',
        'تمت إضافة الكوبون بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reset form and reload coupons
      resetForm();
      await loadCoupons();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إضافة الكوبون: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة الكوبون: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> updateCoupon(String couponId) async {
    if (!validateCouponForm()) return;
    
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Check if coupon code already exists (excluding current coupon)
      final existingCoupon = await _firestore
          .collection('coupons')
          .where('code', isEqualTo: codeController.text.trim())
          .get();
      
      if (existingCoupon.docs.isNotEmpty && existingCoupon.docs.first.id != couponId) {
        Get.snackbar(
          'خطأ',
          'كود الكوبون موجود بالفعل، يرجى استخدام كود آخر',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }
      
      // Update coupon data
      final updatedData = {
        'code': codeController.text.trim(),
        'title': titleController.text.trim(),
        'description': descriptionController.text.trim(),
        'discountType': Coupon.discountTypeToString(selectedDiscountType.value),
        'discountValue': double.parse(discountValueController.text),
        'minimumOrderAmount': minimumOrderController.text.isNotEmpty
            ? double.parse(minimumOrderController.text)
            : null,
        'maximumDiscountAmount': maximumDiscountController.text.isNotEmpty
            ? double.parse(maximumDiscountController.text)
            : null,
        'startDate': Timestamp.fromDate(startDate.value!),
        'endDate': endDate.value != null ? Timestamp.fromDate(endDate.value!) : null,
        'usageLimit': usageLimitController.text.isNotEmpty
            ? int.parse(usageLimitController.text)
            : null,
        'usageLimitPerUser': usageLimitPerUserController.text.isNotEmpty
            ? int.parse(usageLimitPerUserController.text)
            : null,
        'isActive': isActive.value,
        'userType': Coupon.userTypeToString(selectedUserType.value),
        'newUserDays': newUserDaysController.text.isNotEmpty && selectedUserType.value == UserType.new_users
            ? int.parse(newUserDaysController.text)
            : null,
        'updatedAt': FieldValue.serverTimestamp(),
      };
      
      // Update in Firestore
      await _firestore
          .collection('coupons')
          .doc(couponId)
          .update(updatedData);
      
      Get.snackbar(
        'تم بنجاح',
        'تم تحديث الكوبون بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reset form and reload coupons
      resetForm();
      await loadCoupons();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث الكوبون: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الكوبون: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> toggleCouponStatus(String couponId, bool currentStatus) async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Toggle status
      await _firestore
          .collection('coupons')
          .doc(couponId)
          .update({
            'isActive': !currentStatus,
            'updatedAt': FieldValue.serverTimestamp(),
          });
      
      Get.snackbar(
        'تم بنجاح',
        'تم تغيير حالة الكوبون بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload coupons
      await loadCoupons();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تغيير حالة الكوبون: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تغيير حالة الكوبون: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> deleteCoupon(String couponId) async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Delete from Firestore
      await _firestore
          .collection('coupons')
          .doc(couponId)
          .delete();
      
      Get.snackbar(
        'تم بنجاح',
        'تم حذف الكوبون بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload coupons
      await loadCoupons();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف الكوبون: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حذف الكوبون: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void setFormDataForCoupon(Coupon coupon) {
    codeController.text = coupon.code;
    titleController.text = coupon.title;
    descriptionController.text = coupon.description;
    discountValueController.text = coupon.discountValue.toString();
    minimumOrderController.text = coupon.minimumOrderAmount?.toString() ?? '';
    maximumDiscountController.text = coupon.maximumDiscountAmount?.toString() ?? '';
    usageLimitController.text = coupon.usageLimit?.toString() ?? '';
    usageLimitPerUserController.text = coupon.usageLimitPerUser?.toString() ?? '';
    newUserDaysController.text = coupon.newUserDays?.toString() ?? '';
    
    selectedDiscountType.value = coupon.discountType;
    selectedUserType.value = coupon.userType;
    startDate.value = coupon.startDate.toDate();
    endDate.value = coupon.endDate?.toDate();
    isActive.value = coupon.isActive;
  }
  
  bool validateCouponForm() {
    // Validate required fields
    if (codeController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال كود الكوبون',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    if (titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان الكوبون',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    if (discountValueController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال قيمة الخصم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate discount value
    try {
      final discountValue = double.parse(discountValueController.text);
      
      if (selectedDiscountType.value == DiscountType.percentage && (discountValue <= 0 || discountValue > 100)) {
        Get.snackbar(
          'خطأ',
          'يجب أن تكون نسبة الخصم بين 1 و 100',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
      
      if (selectedDiscountType.value == DiscountType.fixed && discountValue <= 0) {
        Get.snackbar(
          'خطأ',
          'يجب أن تكون قيمة الخصم أكبر من 0',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال قيمة صحيحة للخصم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate start date
    if (startDate.value == null) {
      Get.snackbar(
        'خطأ',
        'يرجى تحديد تاريخ بدء الكوبون',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate end date is after start date
    if (endDate.value != null && endDate.value!.isBefore(startDate.value!)) {
      Get.snackbar(
        'خطأ',
        'يجب أن يكون تاريخ انتهاء الكوبون بعد تاريخ البدء',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    // Validate new user days if user type is new users
    if (selectedUserType.value == UserType.new_users) {
      if (newUserDaysController.text.trim().isEmpty) {
        Get.snackbar(
          'خطأ',
          'يرجى تحديد عدد أيام المستخدم الجديد',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
      
      try {
        final days = int.parse(newUserDaysController.text);
        if (days <= 0) {
          Get.snackbar(
            'خطأ',
            'يجب أن يكون عدد الأيام أكبر من 0',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return false;
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'يرجى إدخال عدد صحيح للأيام',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    }
    
    return true;
  }
  
  // Create a seeder for initial coupons data
  Future<void> seedCoupons() async {
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Check if there are already coupons
      final existingCoupons = await _firestore.collection('coupons').limit(1).get();
      if (existingCoupons.docs.isNotEmpty) {
        Get.snackbar(
          'تنبيه',
          'توجد كوبونات بالفعل في قاعدة البيانات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        isLoading.value = false;
        return;
      }
      
      // Sample coupons data
      final List<Coupon> sampleCoupons = [
        Coupon(
          id: '',
          code: 'WELCOME20',
          title: 'خصم الترحيب',
          description: 'خصم 20% للمستخدمين الجدد',
          discountType: DiscountType.percentage,
          discountValue: 20.0,
          minimumOrderAmount: 50.0,
          maximumDiscountAmount: 100.0,
          startDate: Timestamp.now(),
          endDate: Timestamp.fromDate(DateTime.now().add(const Duration(days: 90))),
          usageLimit: 1000,
          usageLimitPerUser: 1,
          usageCount: 0,
          isActive: true,
          userType: UserType.new_users,
          newUserDays: 30,
          createdAt: Timestamp.now(),
        ),
        Coupon(
          id: '',
          code: 'SUMMER50',
          title: 'خصم الصيف',
          description: 'خصم 50 ريال على الرحلات',
          discountType: DiscountType.fixed,
          discountValue: 50.0,
          minimumOrderAmount: 100.0,
          startDate: Timestamp.now(),
          endDate: Timestamp.fromDate(DateTime.now().add(const Duration(days: 60))),
          usageLimit: 500,
          usageLimitPerUser: 2,
          usageCount: 0,
          isActive: true,
          userType: UserType.all,
          createdAt: Timestamp.now(),
        ),
        Coupon(
          id: '',
          code: 'WEEKEND25',
          title: 'خصم نهاية الأسبوع',
          description: 'خصم 25% على رحلات نهاية الأسبوع',
          discountType: DiscountType.percentage,
          discountValue: 25.0,
          minimumOrderAmount: 75.0,
          maximumDiscountAmount: 150.0,
          startDate: Timestamp.now(),
          endDate: Timestamp.fromDate(DateTime.now().add(const Duration(days: 30))),
          usageLimit: 300,
          usageLimitPerUser: 3,
          usageCount: 0,
          isActive: true,
          userType: UserType.all,
          createdAt: Timestamp.now(),
        ),
      ];
      
      // Add sample coupons to Firestore
      for (final coupon in sampleCoupons) {
        await _firestore
            .collection('coupons')
            .add(coupon.toFirestore());
      }
      
      Get.snackbar(
        'تم بنجاح',
        'تمت إضافة ${sampleCoupons.length} كوبونات بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Reload coupons
      await loadCoupons();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إضافة البيانات الأولية: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة البيانات الأولية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
