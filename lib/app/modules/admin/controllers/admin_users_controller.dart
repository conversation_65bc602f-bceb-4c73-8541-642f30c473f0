import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class AdminUsersController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final TextEditingController searchController = TextEditingController();
  final RxList<Map<String, dynamic>> users = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> allUsers = <Map<String, dynamic>>[].obs;
  final RxString searchQuery = ''.obs;
  final RxString filterOption = 'all'.obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  final RxInt totalUsers = 0.obs;
  final RxInt newUsers = 0.obs;
  final RxInt blockedUsers = 0.obs;

  @override
  void onInit() {
    super.onInit();

    // Check if we have a filter parameter from arguments
    if (Get.arguments != null && Get.arguments['filter'] != null) {
      filterOption.value = Get.arguments['filter'];
    }

    loadUsers();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadUsers() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final snapshot = await _firestore
          .collection('users')
          .orderBy('createdAt', descending: true)
          .get();

      final List<Map<String, dynamic>> loadedUsers = [];
      int blocked = 0;
      int newUsersCount = 0;

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final createdAt =
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now();
        final createdDate =
            DateTime(createdAt.year, createdAt.month, createdAt.day);

        // Check if user was created today
        if (createdDate.isAtSameMomentAs(today)) {
          newUsersCount++;
        }

        // Check if user is blocked
        if (data['isBlocked'] == true) {
          blocked++;
        }

        loadedUsers.add({
          'id': doc.id,
          'name': data['name'] ?? 'Unknown',
          'email': data['email'] ?? '',
          'phoneNumber': data['phoneNumber'],
          'profileImageUrl': data['profileImageUrl'],
          'role': data['role'] ?? 'user',
          'isBlocked': data['isBlocked'] ?? false,
          'joinDate': intl.DateFormat('yyyy/MM/dd').format(createdAt),
          'createdAt': createdAt,
        });
      }

      allUsers.value = loadedUsers;
      users.value = loadedUsers;
      totalUsers.value = loadedUsers.length;
      newUsers.value = newUsersCount;
      blockedUsers.value = blocked;

      // If no users found, just keep the empty list
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل بيانات المستخدمين: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات المستخدمين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      allUsers.value = [];
      users.value = [];
      totalUsers.value = 0;
      newUsers.value = 0;
      blockedUsers.value = 0;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> refreshData() async {
    await loadUsers();
  }

  void onSearchChanged(String query) {
    searchQuery.value = query.trim();
    _applyFilters();
  }

  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    _applyFilters();
  }

  void setFilterOption(String option) {
    filterOption.value = option;
    _applyFilters();
  }

  void _applyFilters() {
    if (searchQuery.isEmpty && filterOption.value == 'all') {
      users.value = allUsers;
      return;
    }

    List<Map<String, dynamic>> filteredUsers = allUsers;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      final searchLower = searchQuery.toLowerCase();
      filteredUsers = filteredUsers.where((user) {
        final name = user['name'].toString().toLowerCase();
        final email = user['email'].toString().toLowerCase();
        final phone = (user['phoneNumber'] ?? '').toString().toLowerCase();

        return name.contains(searchLower) ||
            email.contains(searchLower) ||
            phone.contains(searchLower);
      }).toList();
    }

    // Apply role/status filter
    if (filterOption.value != 'all') {
      switch (filterOption.value) {
        case 'users':
          filteredUsers =
              filteredUsers.where((user) => user['role'] == 'user').toList();
          break;
        case 'drivers':
          filteredUsers =
              filteredUsers.where((user) => user['role'] == 'driver').toList();
          break;
        case 'blocked':
          filteredUsers =
              filteredUsers.where((user) => user['isBlocked'] == true).toList();
          break;
        case 'new':
          final now = DateTime.now();
          final thirtyDaysAgo = now.subtract(const Duration(days: 30));
          filteredUsers = filteredUsers.where((user) {
            final createdAt = user['createdAt'] as DateTime;
            return createdAt.isAfter(thirtyDaysAgo);
          }).toList();
          break;
      }
    }

    users.value = filteredUsers;
  }

  void showSearchDialog() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('البحث عن مستخدم'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'اكتب اسم أو بريد إلكتروني أو رقم هاتف',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: onSearchChanged,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                clearSearch();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                // Search is already handled by onSearchChanged
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }

  void showFilterOptions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'تصفية المستخدمين',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildFilterOption('all', 'الكل'),
              _buildFilterOption('users', 'المستخدمين فقط'),
              _buildFilterOption('drivers', 'السائقين فقط'),
              _buildFilterOption('blocked', 'المحظورين'),
              _buildFilterOption('new', 'المستخدمين الجدد (آخر 30 يوم)'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterOption(String value, String title) {
    return ListTile(
      title: Text(title),
      leading: Radio<String>(
        value: value,
        groupValue: filterOption.value,
        onChanged: (newValue) {
          if (newValue != null) {
            Get.back();
            setFilterOption(newValue);
          }
        },
      ),
      onTap: () {
        Get.back();
        setFilterOption(value);
      },
    );
  }

  void showUserDetails(String userId) {
    final user = users.firstWhere((u) => u['id'] == userId, orElse: () => {});
    if (user.isEmpty) return;

    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: Text('تفاصيل المستخدم: ${user['name']}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailItem('البريد الإلكتروني', user['email']),
              _buildDetailItem(
                  'رقم الهاتف', user['phoneNumber'] ?? 'غير متوفر'),
              _buildDetailItem('نوع الحساب', _getRoleText(user['role'])),
              _buildDetailItem('تاريخ الانضمام', user['joinDate']),
              _buildDetailItem(
                  'حالة الحساب', user['isBlocked'] == true ? 'محظور' : 'نشط'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                navigateToEditUser(userId);
              },
              child: const Text('تعديل'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getRoleText(String role) {
    switch (role) {
      case 'user':
        return 'مستخدم';
      case 'driver':
        return 'سائق';
      case 'admin':
        return 'مشرف';
      default:
        return role;
    }
  }

  Future<void> toggleUserBlock(String userId, bool isCurrentlyBlocked) async {
    try {
      isLoading.value = true;

      if (isCurrentlyBlocked) {
        // Unblock user - set status to active
        await _firestore.collection('users').doc(userId).update({
          'status': 'active',
          'statusReason': null,
          'suspensionEndTime': null,
        });
      } else {
        // Show dialog to get reason
        final reason = await _showReasonDialog(
            'حظر المستخدم', 'يرجى إدخال سبب حظر هذا المستخدم:');
        if (reason != null) {
          // Block user - set status to blocked
          await _firestore.collection('users').doc(userId).update({
            'status': 'blocked',
            'statusReason': reason,
          });
        } else {
          // User cancelled, abort operation
          isLoading.value = false;
          return;
        }
      }

      // Update local data
      final index = allUsers.indexWhere((user) => user['id'] == userId);
      if (index != -1) {
        final updatedUser = Map<String, dynamic>.from(allUsers[index]);

        // Update status in local data
        if (isCurrentlyBlocked) {
          updatedUser['status'] = 'active';
          updatedUser['statusReason'] = null;
          updatedUser['suspensionEndTime'] = null;
        } else {
          updatedUser['status'] = 'blocked';
        }

        allUsers[index] = updatedUser;

        // Also update in filtered list if present
        final filteredIndex = users.indexWhere((user) => user['id'] == userId);
        if (filteredIndex != -1) {
          users[filteredIndex] = updatedUser;
        }

        // Update blocked count
        if (isCurrentlyBlocked) {
          blockedUsers.value--;
        } else {
          blockedUsers.value++;
        }
      }

      Get.snackbar(
        'تم',
        isCurrentlyBlocked
            ? 'تم إلغاء حظر المستخدم بنجاح'
            : 'تم حظر المستخدم بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة المستخدم: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void navigateToAddUser() {
    Get.toNamed(Routes.ADMIN_ADD_USER);
  }

  void navigateToEditUser(String userId) {
    Get.toNamed(Routes.ADMIN_EDIT_USER, arguments: {'userId': userId});
  }

  Future<void> suspendUser(String userId) async {
    try {
      isLoading.value = true;

      // Show dialog to get reason and duration
      final result = await _showSuspendDialog();
      if (result != null) {
        final String reason = result['reason'];
        final int hours = result['hours'];

        // Calculate end time
        final endTime = DateTime.now().add(Duration(hours: hours));

        // Update user status in Firestore
        await _firestore.collection('users').doc(userId).update({
          'status': 'suspended',
          'statusReason': reason,
          'suspensionEndTime': Timestamp.fromDate(endTime),
        });

        // Update local data
        final index = allUsers.indexWhere((user) => user['id'] == userId);
        if (index != -1) {
          final updatedUser = Map<String, dynamic>.from(allUsers[index]);
          updatedUser['status'] = 'suspended';
          updatedUser['statusReason'] = reason;
          updatedUser['suspensionEndTime'] = Timestamp.fromDate(endTime);

          allUsers[index] = updatedUser;

          // Also update in filtered list if present
          final filteredIndex =
              users.indexWhere((user) => user['id'] == userId);
          if (filteredIndex != -1) {
            users[filteredIndex] = updatedUser;
          }
        }

        Get.snackbar(
          'تم',
          'تم إيقاف المستخدم مؤقتاً لمدة $hours ساعة',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إيقاف المستخدم: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<Map<String, dynamic>?> _showSuspendDialog() async {
    final TextEditingController reasonController = TextEditingController();
    final TextEditingController hoursController =
        TextEditingController(text: '24');
    Map<String, dynamic>? result;

    await Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('إيقاف مؤقت للمستخدم'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('يرجى إدخال سبب الإيقاف ومدته:'),
              const SizedBox(height: 16),
              TextField(
                controller: reasonController,
                decoration: const InputDecoration(
                  hintText: 'سبب الإيقاف',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: hoursController,
                decoration: const InputDecoration(
                  hintText: 'المدة بالساعات',
                  border: OutlineInputBorder(),
                  suffixText: 'ساعة',
                ),
                keyboardType: TextInputType.number,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (reasonController.text.trim().isNotEmpty &&
                    hoursController.text.trim().isNotEmpty) {
                  try {
                    final hours = int.parse(hoursController.text.trim());
                    if (hours > 0) {
                      result = {
                        'reason': reasonController.text.trim(),
                        'hours': hours,
                      };
                      Get.back();
                    } else {
                      Get.snackbar(
                        'خطأ',
                        'يجب أن تكون المدة أكبر من صفر',
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                    }
                  } catch (e) {
                    Get.snackbar(
                      'خطأ',
                      'يرجى إدخال رقم صحيح للمدة',
                      backgroundColor: Colors.red,
                      colorText: Colors.white,
                    );
                  }
                } else {
                  Get.snackbar(
                    'خطأ',
                    'يرجى إدخال سبب الإيقاف والمدة',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
              child: const Text('إيقاف'),
            ),
          ],
        ),
      ),
    );

    reasonController.dispose();
    hoursController.dispose();
    return result;
  }

  Future<void> setDriverUnderReview(String userId) async {
    try {
      isLoading.value = true;

      // Show dialog to get reason
      final reason = await _showReasonDialog(
          'وضع السائق قيد المراجعة', 'يرجى إدخال سبب وضع السائق قيد المراجعة:');
      if (reason != null) {
        // Update user status in Firestore
        await _firestore.collection('users').doc(userId).update({
          'status': 'underReview',
          'statusReason': reason,
        });

        // Update local data
        final index = allUsers.indexWhere((user) => user['id'] == userId);
        if (index != -1) {
          final updatedUser = Map<String, dynamic>.from(allUsers[index]);
          updatedUser['status'] = 'underReview';
          updatedUser['statusReason'] = reason;

          allUsers[index] = updatedUser;

          // Also update in filtered list if present
          final filteredIndex =
              users.indexWhere((user) => user['id'] == userId);
          if (filteredIndex != -1) {
            users[filteredIndex] = updatedUser;
          }
        }

        Get.snackbar(
          'تم',
          'تم وضع السائق قيد المراجعة',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء وضع السائق قيد المراجعة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<String?> _showReasonDialog(String title, String message) async {
    final TextEditingController reasonController = TextEditingController();
    String? result;

    await Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(message),
              const SizedBox(height: 16),
              TextField(
                controller: reasonController,
                decoration: const InputDecoration(
                  hintText: 'السبب',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (reasonController.text.trim().isNotEmpty) {
                  result = reasonController.text.trim();
                  Get.back();
                } else {
                  Get.snackbar(
                    'خطأ',
                    'يرجى إدخال السبب',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
              child: const Text('تأكيد'),
            ),
          ],
        ),
      ),
    );

    reasonController.dispose();
    return result;
  }
}
