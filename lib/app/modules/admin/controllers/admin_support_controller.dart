import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class AdminSupportController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final TextEditingController searchController = TextEditingController();
  final TextEditingController replyController = TextEditingController();
  
  final RxList<Map<String, dynamic>> tickets = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> allTickets = <Map<String, dynamic>>[].obs;
  final RxString searchQuery = ''.obs;
  final RxString filterOption = 'all'.obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  // Statistics
  final RxInt totalTickets = 0.obs;
  final RxInt newTickets = 0.obs;
  final RxInt openTickets = 0.obs;
  final RxInt resolvedTickets = 0.obs;

  @override
  void onInit() {
    super.onInit();
    
    // Check if we have a filter parameter from arguments
    if (Get.arguments != null && Get.arguments['filter'] != null) {
      filterOption.value = Get.arguments['filter'];
    }
    
    loadTickets();
  }

  @override
  void onClose() {
    searchController.dispose();
    replyController.dispose();
    super.onClose();
  }

  Future<void> loadTickets() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final snapshot = await _firestore
          .collection('supportTickets')
          .orderBy('createdAt', descending: true)
          .get();

      final List<Map<String, dynamic>> loadedTickets = [];
      int newCount = 0;
      int openCount = 0;
      int resolvedCount = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final createdAt = (data['createdAt'] as Timestamp).toDate();
        final status = data['status'] ?? 'new';
        
        // Count tickets by status
        if (status == 'new') {
          newCount++;
        } else if (status == 'open') {
          openCount++;
        } else if (status == 'resolved') {
          resolvedCount++;
        }

        loadedTickets.add({
          'id': doc.id,
          'userId': data['userId'] ?? '',
          'subject': data['subject'] ?? 'بدون عنوان',
          'message': data['message'] ?? '',
          'status': status,
          'priority': data['priority'] ?? 'medium',
          'category': data['category'] ?? 'general',
          'date': intl.DateFormat('yyyy/MM/dd').format(createdAt),
          'time': intl.DateFormat('HH:mm').format(createdAt),
          'createdAt': createdAt,
          'replies': data['replies'] ?? [],
        });
      }

      allTickets.value = loadedTickets;
      applyFilters();

      totalTickets.value = loadedTickets.length;
      newTickets.value = newCount;
      openTickets.value = openCount;
      resolvedTickets.value = resolvedCount;

    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل بيانات تذاكر الدعم: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات تذاكر الدعم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      allTickets.value = [];
      tickets.value = [];
      totalTickets.value = 0;
      newTickets.value = 0;
      openTickets.value = 0;
      resolvedTickets.value = 0;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> refreshData() async {
    await loadTickets();
  }

  void onSearchChanged(String query) {
    searchQuery.value = query;
    applyFilters();
  }

  void clearSearch() {
    searchQuery.value = '';
    searchController.clear();
    applyFilters();
  }

  void setFilter(String filter) {
    filterOption.value = filter;
    applyFilters();
  }

  void applyFilters() {
    List<Map<String, dynamic>> filteredTickets = List.from(allTickets);

    // Apply status filter
    if (filterOption.value != 'all') {
      filteredTickets = filteredTickets.where((ticket) {
        return ticket['status'] == filterOption.value;
      }).toList();
    }

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filteredTickets = filteredTickets.where((ticket) {
        return ticket['subject'].toString().toLowerCase().contains(query) ||
               ticket['message'].toString().toLowerCase().contains(query) ||
               ticket['id'].toString().toLowerCase().contains(query) ||
               ticket['userId'].toString().toLowerCase().contains(query);
      }).toList();
    }

    tickets.value = filteredTickets;
  }

  void showSearchDialog() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: const Text('البحث عن تذكرة'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'اكتب موضوع التذكرة أو رقمها',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: onSearchChanged,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                clearSearch();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                onSearchChanged(searchController.text);
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }

  void showFilterOptions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'تصفية التذاكر',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.all_inclusive),
                title: const Text('جميع التذاكر'),
                trailing: filterOption.value == 'all'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('all');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.fiber_new),
                title: const Text('التذاكر الجديدة'),
                trailing: filterOption.value == 'new'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('new');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.chat),
                title: const Text('التذاكر المفتوحة'),
                trailing: filterOption.value == 'open'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('open');
                  Get.back();
                },
              ),
              ListTile(
                leading: const Icon(Icons.check_circle_outline),
                title: const Text('التذاكر المحلولة'),
                trailing: filterOption.value == 'resolved'
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : null,
                onTap: () {
                  setFilter('resolved');
                  Get.back();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> updateTicketStatus(String ticketId, String newStatus) async {
    isLoading.value = true;

    try {
      await _firestore.collection('supportTickets').doc(ticketId).update({
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      Get.snackbar(
        'تم بنجاح',
        'تم تحديث حالة التذكرة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      await loadTickets();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة التذكرة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addReply(String ticketId) async {
    if (replyController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'الرجاء كتابة رد',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;

    try {
      // Get the current ticket
      final ticketDoc = await _firestore.collection('supportTickets').doc(ticketId).get();
      final ticketData = ticketDoc.data() ?? {};
      
      // Get existing replies or create empty list
      List<dynamic> replies = List.from(ticketData['replies'] ?? []);
      
      // Add new reply
      replies.add({
        'message': replyController.text.trim(),
        'isAdmin': true,
        'timestamp': FieldValue.serverTimestamp(),
      });
      
      // Update the ticket
      await _firestore.collection('supportTickets').doc(ticketId).update({
        'replies': replies,
        'status': 'open', // Change status to open when admin replies
        'updatedAt': FieldValue.serverTimestamp(),
      });

      Get.snackbar(
        'تم بنجاح',
        'تم إضافة الرد بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      replyController.clear();
      await loadTickets();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة الرد: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void showTicketDetails(String ticketId) {
    final ticket = tickets.firstWhere((t) => t['id'] == ticketId, orElse: () => {});
    if (ticket.isEmpty) return;

    // Reset reply controller
    replyController.clear();

    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: Dialog(
          child: Container(
            width: Get.width * 0.9,
            height: Get.height * 0.8,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'تذكرة #${ticket['id']}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                const Divider(),
                _buildTicketHeader(ticket),
                const Divider(),
                Expanded(
                  child: _buildTicketMessages(ticket),
                ),
                const Divider(),
                _buildReplySection(ticket),
                const SizedBox(height: 16),
                _buildStatusButtons(ticket),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTicketHeader(Map<String, dynamic> ticket) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          ticket['subject'],
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              _getPriorityIcon(ticket['priority']),
              size: 16,
              color: _getPriorityColor(ticket['priority']),
            ),
            const SizedBox(width: 4),
            Text(
              _getPriorityText(ticket['priority']),
              style: TextStyle(
                color: _getPriorityColor(ticket['priority']),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 16),
            Icon(
              _getCategoryIcon(ticket['category']),
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(_getCategoryText(ticket['category'])),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(Icons.person, size: 16),
            const SizedBox(width: 4),
            Text('معرف المستخدم: ${ticket['userId']}'),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(Icons.calendar_today, size: 16),
            const SizedBox(width: 4),
            Text('${ticket['date']} - ${ticket['time']}'),
          ],
        ),
      ],
    );
  }

  Widget _buildTicketMessages(Map<String, dynamic> ticket) {
    final initialMessage = ticket['message'] as String;
    final replies = List<Map<String, dynamic>>.from(ticket['replies'] ?? []);

    return ListView(
      children: [
        _buildMessageBubble(
          message: initialMessage,
          isAdmin: false,
          timestamp: ticket['createdAt'],
        ),
        ...replies.map((reply) {
          final timestamp = reply['timestamp'] is Timestamp
              ? (reply['timestamp'] as Timestamp).toDate()
              : DateTime.now();
          
          return _buildMessageBubble(
            message: reply['message'],
            isAdmin: reply['isAdmin'] ?? false,
            timestamp: timestamp,
          );
        }).toList(),
      ],
    );
  }

  Widget _buildMessageBubble({
    required String message,
    required bool isAdmin,
    required dynamic timestamp,
  }) {
    final time = timestamp is DateTime
        ? intl.DateFormat('yyyy/MM/dd HH:mm').format(timestamp)
        : '';
    
    return Align(
      alignment: isAdmin ? Alignment.centerLeft : Alignment.centerRight,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isAdmin ? Colors.blue.shade100 : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(12),
        ),
        constraints: BoxConstraints(
          maxWidth: Get.width * 0.7,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  isAdmin ? Icons.support_agent : Icons.person,
                  size: 12,
                  color: Colors.grey.shade700,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReplySection(Map<String, dynamic> ticket) {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: replyController,
            decoration: const InputDecoration(
              hintText: 'اكتب ردك هنا...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          icon: const Icon(Icons.send),
          onPressed: () => addReply(ticket['id']),
          color: Colors.blue,
        ),
      ],
    );
  }

  Widget _buildStatusButtons(Map<String, dynamic> ticket) {
    final currentStatus = ticket['status'] as String;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.fiber_new),
          label: const Text('جديدة'),
          onPressed: currentStatus == 'new'
              ? null
              : () {
                  Get.back();
                  updateTicketStatus(ticket['id'], 'new');
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            disabledBackgroundColor: Colors.orange.withOpacity(0.5),
          ),
        ),
        ElevatedButton.icon(
          icon: const Icon(Icons.chat),
          label: const Text('مفتوحة'),
          onPressed: currentStatus == 'open'
              ? null
              : () {
                  Get.back();
                  updateTicketStatus(ticket['id'], 'open');
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            disabledBackgroundColor: Colors.blue.withOpacity(0.5),
          ),
        ),
        ElevatedButton.icon(
          icon: const Icon(Icons.check_circle),
          label: const Text('محلولة'),
          onPressed: currentStatus == 'resolved'
              ? null
              : () {
                  Get.back();
                  updateTicketStatus(ticket['id'], 'resolved');
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            disabledBackgroundColor: Colors.green.withOpacity(0.5),
          ),
        ),
      ],
    );
  }

  String _getPriorityText(String priority) {
    switch (priority) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getPriorityIcon(String priority) {
    switch (priority) {
      case 'high':
        return Icons.priority_high;
      case 'medium':
        return Icons.remove;
      case 'low':
        return Icons.arrow_downward;
      default:
        return Icons.help_outline;
    }
  }

  String _getCategoryText(String category) {
    switch (category) {
      case 'general':
        return 'عام';
      case 'payment':
        return 'الدفع';
      case 'trip':
        return 'رحلة';
      case 'account':
        return 'الحساب';
      case 'technical':
        return 'مشكلة تقنية';
      default:
        return category;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'general':
        return Icons.info_outline;
      case 'payment':
        return Icons.attach_money;
      case 'trip':
        return Icons.directions_car;
      case 'account':
        return Icons.person;
      case 'technical':
        return Icons.build;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'new':
        return Colors.orange;
      case 'open':
        return Colors.blue;
      case 'resolved':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'new':
        return Icons.fiber_new;
      case 'open':
        return Icons.chat;
      case 'resolved':
        return Icons.check_circle;
      default:
        return Icons.help_outline;
    }
  }
}
