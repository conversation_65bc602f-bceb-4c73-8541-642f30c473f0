import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AdminDriversMapController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Map controller
  Rx<GoogleMapController?> mapController = Rx<GoogleMapController?>(null);

  // Driver locations
  final RxMap<String, DriverLocation> driverLocations =
      <String, DriverLocation>{}.obs;

  // Map markers
  final RxSet<Marker> markers = <Marker>{}.obs;

  // Custom marker icons
  final Rx<BitmapDescriptor?> carIconOnline = Rx<BitmapDescriptor?>(null);
  final Rx<BitmapDescriptor?> carIconBusy = Rx<BitmapDescriptor?>(null);
  final Rx<BitmapDescriptor?> carIconOffline = Rx<BitmapDescriptor?>(null);

  // Map camera position
  final Rx<CameraPosition> initialCameraPosition = const CameraPosition(
    target: LatLng(24.7136, 46.6753), // Default to Riyadh, Saudi Arabia
    zoom: 12.0,
  ).obs;

  // Loading state
  final RxBool isLoading = true.obs;

  // Error message
  final Rx<String?> errorMessage = Rx<String?>(null);

  // Filter options
  final RxBool showOnlineDrivers = true.obs;
  final RxBool showOfflineDrivers = false.obs;
  final RxBool showBusyDrivers = true.obs;

  // Search functionality
  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxList<String> searchResults = <String>[].obs;
  final RxBool isSearching = false.obs;
  final RxBool showSearchResults = false.obs;

  // Statistics
  final RxInt totalDrivers = 0.obs;
  final RxInt onlineDrivers = 0.obs;
  final RxInt busyDrivers = 0.obs;

  // Streams
  StreamSubscription<QuerySnapshot>? _driversSubscription;

  @override
  void onInit() {
    super.onInit();
    _loadCustomMarkerIcons();
    startTrackingDrivers();
  }

  Future<void> _loadCustomMarkerIcons() async {
    try {
      // Load custom marker icons using BitmapDescriptor.defaultMarkerWithHue for simplicity
      // In a production app, you would use custom SVG assets converted to bitmaps
      carIconOnline.value =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      carIconBusy.value =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
      carIconOffline.value =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل أيقونات الخريطة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Custom marker creation would go here in a production app

  @override
  void onClose() {
    _driversSubscription?.cancel();
    mapController.value?.dispose();
    searchController.dispose();
    super.onClose();
  }

  void onMapCreated(GoogleMapController controller) {
    mapController.value = controller;

    // Apply custom map style if needed
    // controller.setMapStyle(mapStyle);
  }

  void startTrackingDrivers() {
    isLoading.value = true;

    try {
      // Listen to driver location updates
      _driversSubscription = _firestore
          .collection('driverLocations')
          .snapshots()
          .listen((snapshot) {
        _updateDriverLocations(snapshot);
      }, onError: (error) {
        errorMessage.value = 'حدث خطأ أثناء تتبع مواقع السائقين: $error';
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تتبع مواقع السائقين',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });

      // Also fetch driver details for additional info
      _fetchDriverDetails();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء بدء تتبع مواقع السائقين: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء بدء تتبع مواقع السائقين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _updateDriverLocations(QuerySnapshot snapshot) {
    int online = 0;
    int busy = 0;

    for (final doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      final driverId = doc.id;

      GeoPoint? location = data['location'] as GeoPoint?;
      if (location == null) continue; // Skip if location is not available

      final latitude = location.latitude.toDouble();
      final longitude = location.longitude.toDouble();
      final isOnline = data['isOnline'] as bool? ?? false;
      final isBusy = data['isBusy'] as bool? ?? false;
      final lastUpdated =
          (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now();

      // Count statistics
      if (isOnline) online++;
      if (isBusy) busy++;

      // Location is already validated above

      // Update driver location with smooth transition
      final newPosition = LatLng(latitude, longitude);

      if (driverLocations.containsKey(driverId)) {
        // If driver already exists, update with smooth transition
        driverLocations[driverId] =
            driverLocations[driverId]!.copyWithNewPosition(newPosition);
      } else {
        // If this is a new driver, create a new entry
        driverLocations[driverId] = DriverLocation(
          driverId: driverId,
          position: newPosition,
          isOnline: isOnline,
          isBusy: isBusy,
          lastUpdated: lastUpdated,
          driverName: 'سائق',
          driverPhone: '',
          driverRating: 0.0,
          driverPhoto: '',
          vehicleInfo: '',
        );
      }
    }

    // Update statistics
    totalDrivers.value = snapshot.docs.length;
    onlineDrivers.value = online;
    busyDrivers.value = busy;

    // Update markers
    _updateMapMarkers();
  }

  Future<void> _fetchDriverDetails() async {
    try {
      final driversSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'driver')
          .get();

      for (final doc in driversSnapshot.docs) {
        final data = doc.data();
        final driverId = doc.id;

        // If we already have this driver's location, update with additional details
        if (driverLocations.containsKey(driverId)) {
          final existingLocation = driverLocations[driverId]!;

          driverLocations[driverId] = DriverLocation(
            driverId: driverId,
            position: existingLocation.position,
            isOnline: existingLocation.isOnline,
            isBusy: existingLocation.isBusy,
            lastUpdated: existingLocation.lastUpdated,
            driverName: data['name'] ?? 'سائق',
            driverPhone: data['phoneNumber'] ?? '',
            driverRating: (data['rating'] as num?)?.toDouble() ?? 0.0,
            driverPhoto: data['profileImageUrl'] ?? '',
            vehicleInfo: data['vehicleInfo'] ?? '',
          );
        } else {
          // If we don't have location yet, store the details anyway
          driverLocations[driverId] = DriverLocation(
            driverId: driverId,
            position: const LatLng(0, 0), // Default position
            isOnline: false,
            isBusy: false,
            lastUpdated: DateTime.now(),
            driverName: data['name'] ?? 'سائق',
            driverPhone: data['phoneNumber'] ?? '',
            driverRating: (data['rating'] as num?)?.toDouble() ?? 0.0,
            driverPhoto: data['profileImageUrl'] ?? '',
            vehicleInfo: data['vehicleInfo'] ?? '',
          );
        }
      }

      // Update markers with new details
      _updateMapMarkers();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء جلب بيانات السائقين: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء جلب بيانات السائقين',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _updateMapMarkers() {
    final Set<Marker> newMarkers = {};

    driverLocations.forEach((driverId, location) {
      // Apply filters
      if (!showOnlineDrivers.value && location.isOnline) return;
      if (!showOfflineDrivers.value && !location.isOnline) return;
      if (!showBusyDrivers.value && location.isBusy) return;

      print('///////////////////////////');
      print(location);
      print('///////////////////////////');

      // Create marker with rotation based on heading
      final marker = Marker(
        markerId: MarkerId(driverId),
        position: location.position,
        icon: _getMarkerIcon(location),
        rotation: location.heading, // Apply rotation based on heading
        anchor: const Offset(0.5, 0.5), // Center the marker for proper rotation
        flat: true, // Keep marker flat on the map
        infoWindow: InfoWindow(
          title: location.driverName,
          snippet: _getDriverStatusText(location),
          onTap: () => showDriverDetails(driverId),
        ),
        onTap: () => showDriverDetails(driverId),
      );

      newMarkers.add(marker);
    });

    markers.clear();
    markers.addAll(newMarkers);
  }

  BitmapDescriptor _getMarkerIcon(DriverLocation location) {
    // Use custom car icons if available
    if (location.isBusy && carIconBusy.value != null) {
      return carIconBusy.value!;
    } else if (location.isOnline && carIconOnline.value != null) {
      return carIconOnline.value!;
    } else if (!location.isOnline && carIconOffline.value != null) {
      return carIconOffline.value!;
    }

    // Fallback to default markers if custom icons failed to load
    if (location.isBusy) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueYellow);
    } else if (location.isOnline) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } else {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    }
  }

  String _getDriverStatusText(DriverLocation location) {
    if (location.isBusy) {
      return 'مشغول في رحلة';
    } else if (location.isOnline) {
      return 'متصل ومتاح';
    } else {
      return 'غير متصل';
    }
  }

  void showDriverDetails(String driverId) {
    final driver = driverLocations[driverId];
    if (driver == null) return;

    Get.dialog(
      Directionality(
        textDirection: TextDirection.ltr,
        child: AlertDialog(
          title: Text('معلومات السائق: ${driver.driverName}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDriverInfoRow('الحالة', _getDriverStatusText(driver)),
              if (driver.driverPhone.isNotEmpty)
                _buildDriverInfoRow('رقم الهاتف', driver.driverPhone),
              if (driver.driverRating > 0)
                _buildDriverInfoRow(
                    'التقييم', '${driver.driverRating.toStringAsFixed(1)} ★'),
              if (driver.vehicleInfo.isNotEmpty)
                _buildDriverInfoRow('معلومات المركبة', driver.vehicleInfo),
              _buildDriverInfoRow(
                'آخر تحديث',
                '${_formatDate(driver.lastUpdated)} ${_formatTime(driver.lastUpdated)}',
              ),
              _buildDriverInfoRow(
                'الإحداثيات',
                '${driver.position.latitude.toStringAsFixed(6)}, ${driver.position.longitude.toStringAsFixed(6)}',
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                centerMapOnDriver(driverId);
              },
              child: const Text('عرض على الخريطة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void centerMapOnDriver(String driverId) {
    final driver = driverLocations[driverId];
    if (driver == null || mapController.value == null) return;

    mapController.value!.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: driver.position,
          zoom: 16.0,
        ),
      ),
    );
  }

  void toggleOnlineDrivers(bool value) {
    showOnlineDrivers.value = value;
    _updateMapMarkers();
  }

  void toggleOfflineDrivers(bool value) {
    showOfflineDrivers.value = value;
    _updateMapMarkers();
  }

  void toggleBusyDrivers(bool value) {
    showBusyDrivers.value = value;
    _updateMapMarkers();
  }

  void centerMapOnAllDrivers() {
    if (driverLocations.isEmpty || mapController.value == null) return;

    // Calculate bounds that include all drivers
    final bounds = _calculateBounds();
    if (bounds == null) return;

    // Animate camera to show all drivers
    mapController.value!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 50), // 50 is padding
    );
  }

  LatLngBounds? _calculateBounds() {
    if (driverLocations.isEmpty) return null;

    double? minLat, maxLat, minLng, maxLng;

    driverLocations.forEach((_, location) {
      // Apply filters
      if (!showOnlineDrivers.value && location.isOnline) return;
      if (!showOfflineDrivers.value && !location.isOnline) return;
      if (!showBusyDrivers.value && location.isBusy) return;

      // Skip invalid locations
      if (location.position.latitude == 0 && location.position.longitude == 0) {
        return;
      }

      // Update bounds
      minLat = minLat == null
          ? location.position.latitude
          : (location.position.latitude < minLat!
              ? location.position.latitude
              : minLat);

      maxLat = maxLat == null
          ? location.position.latitude
          : (location.position.latitude > maxLat!
              ? location.position.latitude
              : maxLat);

      minLng = minLng == null
          ? location.position.longitude
          : (location.position.longitude < minLng!
              ? location.position.longitude
              : minLng);

      maxLng = maxLng == null
          ? location.position.longitude
          : (location.position.longitude > maxLng!
              ? location.position.longitude
              : maxLng);
    });

    // If no valid locations found
    if (minLat == null || maxLat == null || minLng == null || maxLng == null) {
      return null;
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')}';
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Search methods
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      searchResults.clear();
      showSearchResults.value = false;
      _updateMapMarkers();
      return;
    }

    showSearchResults.value = true;
    _performSearch(query);
  }

  void _performSearch(String query) {
    final List<String> results = [];
    final lowercaseQuery = query.toLowerCase();

    driverLocations.forEach((driverId, location) {
      final driverName = location.driverName.toLowerCase();
      final driverPhone = location.driverPhone.toLowerCase();
      final vehicleInfo = location.vehicleInfo.toLowerCase();

      if (driverName.contains(lowercaseQuery) ||
          driverPhone.contains(lowercaseQuery) ||
          vehicleInfo.contains(lowercaseQuery) ||
          driverId.toLowerCase().contains(lowercaseQuery)) {
        results.add(driverId);
      }
    });

    searchResults.value = results;
    _updateMapMarkersForSearch();
  }

  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    searchResults.clear();
    showSearchResults.value = false;
    _updateMapMarkers();
  }

  void _updateMapMarkersForSearch() {
    final Set<Marker> newMarkers = {};

    // If we have search results, only show those drivers
    if (searchResults.isNotEmpty) {
      for (final driverId in searchResults) {
        final location = driverLocations[driverId];
        if (location == null) continue;

        // Apply filters
        if (!showOnlineDrivers.value && location.isOnline) continue;
        if (!showOfflineDrivers.value && !location.isOnline) continue;
        if (!showBusyDrivers.value && location.isBusy) continue;

        // Create marker with rotation based on heading
        final marker = Marker(
          markerId: MarkerId(driverId),
          position: location.position,
          icon: _getMarkerIcon(location),
          rotation: location.heading, // Apply rotation based on heading
          anchor:
              const Offset(0.5, 0.5), // Center the marker for proper rotation
          flat: true, // Keep marker flat on the map
          infoWindow: InfoWindow(
            title: location.driverName,
            snippet: _getDriverStatusText(location),
            onTap: () => showDriverDetails(driverId),
          ),
          onTap: () => showDriverDetails(driverId),
        );

        newMarkers.add(marker);
      }
    }

    markers.clear();
    markers.addAll(newMarkers);

    // Center map on search results if we have any
    if (searchResults.isNotEmpty && mapController.value != null) {
      final bounds = _calculateBoundsForSearch();
      if (bounds != null) {
        mapController.value!.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 50), // 50 is padding
        );
      } else if (searchResults.length == 1) {
        // If we only have one result, center on that driver
        final driverId = searchResults.first;
        final location = driverLocations[driverId];
        if (location != null) {
          mapController.value!.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: location.position,
                zoom: 16.0,
              ),
            ),
          );
        }
      }
    }
  }

  LatLngBounds? _calculateBoundsForSearch() {
    if (searchResults.isEmpty) return null;

    double? minLat, maxLat, minLng, maxLng;

    for (final driverId in searchResults) {
      final location = driverLocations[driverId];
      if (location == null) continue;

      // Apply filters
      if (!showOnlineDrivers.value && location.isOnline) continue;
      if (!showOfflineDrivers.value && !location.isOnline) continue;
      if (!showBusyDrivers.value && location.isBusy) continue;

      // Skip invalid locations
      if (location.position.latitude == 0 && location.position.longitude == 0) {
        continue;
      }

      // Update bounds
      minLat = minLat == null
          ? location.position.latitude
          : (location.position.latitude < minLat
              ? location.position.latitude
              : minLat);

      maxLat = maxLat == null
          ? location.position.latitude
          : (location.position.latitude > maxLat
              ? location.position.latitude
              : maxLat);

      minLng = minLng == null
          ? location.position.longitude
          : (location.position.longitude < minLng
              ? location.position.longitude
              : minLng);

      maxLng = maxLng == null
          ? location.position.longitude
          : (location.position.longitude > maxLng
              ? location.position.longitude
              : maxLng);
    }

    // If no valid locations found
    if (minLat == null || maxLat == null || minLng == null || maxLng == null) {
      return null;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }
}

class DriverLocation {
  final String driverId;
  final LatLng position;
  final LatLng? previousPosition; // For animation
  final double heading; // Direction in degrees (0-360)
  final bool isOnline;
  final bool isBusy;
  final DateTime lastUpdated;
  final String driverName;
  final String driverPhone;
  final double driverRating;
  final String driverPhoto;
  final String vehicleInfo;

  DriverLocation({
    required this.driverId,
    required this.position,
    this.previousPosition,
    this.heading = 0.0,
    required this.isOnline,
    required this.isBusy,
    required this.lastUpdated,
    required this.driverName,
    required this.driverPhone,
    required this.driverRating,
    required this.driverPhoto,
    required this.vehicleInfo,
  });

  // Create a new instance with updated position and calculate heading
  DriverLocation copyWithNewPosition(LatLng newPosition) {
    // Calculate heading if we have a previous position
    double newHeading = heading;
    if (previousPosition != null) {
      final double deltaLng = newPosition.longitude - position.longitude;
      final double deltaLat = newPosition.latitude - position.latitude;

      // Only update heading if there's significant movement
      if (deltaLng != 0 || deltaLat != 0) {
        newHeading = (atan2(deltaLng, deltaLat) * 180 / pi) % 360;
      }
    }

    return DriverLocation(
      driverId: driverId,
      position: newPosition,
      previousPosition: position, // Current position becomes previous
      heading: newHeading,
      isOnline: isOnline,
      isBusy: isBusy,
      lastUpdated: DateTime.now(),
      driverName: driverName,
      driverPhone: driverPhone,
      driverRating: driverRating,
      driverPhoto: driverPhoto,
      vehicleInfo: vehicleInfo,
    );
  }
}
