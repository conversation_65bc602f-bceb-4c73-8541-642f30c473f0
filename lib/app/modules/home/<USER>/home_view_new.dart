// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
//
// import '../controllers/home_controller_new.dart';
// import 'widgets/coupon_widget.dart';
// import 'widgets/home_drawer_widget.dart';
// import 'widgets/payment_method_widget.dart';
// import 'widgets/payment_summary_widget.dart';
// import 'widgets/trip_summary_widget.dart';
//
// class HomeViewNew extends GetView<HomeControllerNew> {
//   const HomeViewNew({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);
//
//     return Scaffold(
//       appBar: AppBar(
//         title: Obx(() => Text(_getAppBarTitle(controller.homeState.value))),
//         leading: Obx(() {
//           if (controller.homeState.value == HomeState.idle ||
//               controller.homeState.value == HomeState.showingDirections) {
//             return IconButton(
//               icon: const Icon(Icons.menu),
//               onPressed: () {
//                 Scaffold.of(context).openDrawer();
//               },
//             );
//           } else {
//             return IconButton(
//               icon: const Icon(Icons.arrow_back),
//               onPressed: () {
//                 _handleBackButton();
//               },
//             );
//           }
//         }),
//         actions: [
//           Obx(() {
//             if (controller.homeState.value == HomeState.selectingPickup ||
//                 controller.homeState.value == HomeState.showingRoutePreview) {
//               return IconButton(
//                 icon: const Icon(Icons.refresh),
//                 onPressed: () {
//                   controller.locationController.recenterMapOnUser();
//                 },
//               );
//             } else {
//               return const SizedBox.shrink();
//             }
//           }),
//         ],
//       ),
//       drawer: const HomeDrawerWidget(),
//       body: Obx(() {
//         // Show loading indicator if loading
//         if (controller.isLoading.value) {
//           return const Center(child: CircularProgressIndicator());
//         }
//
//         // Show error message if there's an error
//         if (controller.errorMessage.value != null) {
//           return Center(
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   const Icon(Icons.error_outline, size: 48, color: Colors.red),
//                   const SizedBox(height: 16),
//                   Text(
//                     controller.errorMessage.value!,
//                     textAlign: TextAlign.center,
//                     style: theme.textTheme.titleMedium,
//                   ),
//                   const SizedBox(height: 24),
//                   ElevatedButton(
//                     onPressed: () {
//                       controller.initializeApp();
//                     },
//                     child: const Text('Retry'),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }
//
//         // Show appropriate UI based on state
//         switch (controller.homeState.value) {
//           case HomeState.idle:
//             return const Center(child: CircularProgressIndicator());
//
//           case HomeState.showingDirections:
//             return _buildDirectionsList();
//
//           case HomeState.selectingPickup:
//             return _buildMapPickupView();
//
//           case HomeState.showingRoutePreview:
//             return _buildRoutePreviewView();
//
//           case HomeState.enteringDetails:
//             return _buildTripDetailsView();
//
//           case HomeState.requestingRide:
//             return _buildRequestingRideView();
//
//           case HomeState.trackingRide:
//             return _buildTrackingRideView();
//
//           default:
//             return const Center(child: Text('Unknown state'));
//         }
//       }),
//     );
//   }
//
//   // Get the appropriate app bar title based on the current state
//   String _getAppBarTitle(HomeState state) {
//     switch (state) {
//       case HomeState.idle:
//         return 'Loading...';
//       case HomeState.showingDirections:
//         return 'Select Destination';
//       case HomeState.selectingPickup:
//         return 'Select Pickup Location';
//       case HomeState.showingRoutePreview:
//         return 'Confirm Route';
//       case HomeState.enteringDetails:
//         return 'Trip Details';
//       case HomeState.requestingRide:
//         return 'Finding Driver';
//       case HomeState.trackingRide:
//         return 'Trip in Progress';
//       default:
//         return 'AI Delivery';
//     }
//   }
//
//   // Handle back button press based on current state
//   void _handleBackButton() {
//     switch (controller.homeState.value) {
//       case HomeState.selectingPickup:
//         controller.homeState.value = HomeState.showingDirections;
//         controller.directionsController.reset();
//         break;
//       case HomeState.showingRoutePreview:
//         controller.homeState.value = HomeState.selectingPickup;
//         controller.directionsController.clearRoute();
//         break;
//       case HomeState.enteringDetails:
//         controller.homeState.value = HomeState.showingRoutePreview;
//         break;
//       case HomeState.requestingRide:
//         // Show confirmation dialog before cancelling
//         Get.dialog(
//           AlertDialog(
//             title: const Text('Cancel Request?'),
//             content: const Text('Are you sure you want to cancel this trip request?'),
//             actions: [
//               TextButton(
//                 onPressed: () => Get.back(),
//                 child: const Text('No'),
//               ),
//               TextButton(
//                 onPressed: () {
//                   Get.back();
//                   controller.cancelTripRequest();
//                 },
//                 child: const Text('Yes'),
//               ),
//             ],
//           ),
//         );
//         break;
//       case HomeState.trackingRide:
//         // Show confirmation dialog before cancelling
//         Get.dialog(
//           AlertDialog(
//             title: const Text('Cancel Trip?'),
//             content: const Text('Are you sure you want to cancel this trip?'),
//             actions: [
//               TextButton(
//                 onPressed: () => Get.back(),
//                 child: const Text('No'),
//               ),
//               TextButton(
//                 onPressed: () {
//                   Get.back();
//                   controller.cancelTripRequest();
//                 },
//                 child: const Text('Yes'),
//               ),
//             ],
//           ),
//         );
//         break;
//       default:
//         break;
//     }
//   }
//
//   // Build the directions list view
//   Widget _buildDirectionsList() {
//     return ListView.builder(
//       padding: const EdgeInsets.all(16),
//       itemCount: controller.directionsController.availableDirections.length,
//       itemBuilder: (context, index) {
//         final direction = controller.directionsController.availableDirections[index];
//         return Card(
//           margin: const EdgeInsets.only(bottom: 12),
//           child: ListTile(
//             title: Text(direction.title),
//             subtitle: Text('Base Price: ${direction.basePrice.toStringAsFixed(2)} SAR'),
//             trailing: const Icon(Icons.arrow_forward_ios),
//             onTap: () => controller.onDirectionSelected(index),
//           ),
//         );
//       },
//     );
//   }
//
//   // Build the map view for selecting pickup location
//   Widget _buildMapPickupView() {
//     return Stack(
//       children: [
//         // Google Map
//         GoogleMap(
//           initialCameraPosition: CameraPosition(
//             target: controller.locationController.userLocation.value ?? const LatLng(24.7136, 46.6753), // Default to Riyadh
//             zoom: 14,
//           ),
//           myLocationEnabled: true,
//           myLocationButtonEnabled: false,
//           mapToolbarEnabled: false,
//           zoomControlsEnabled: false,
//           markers: controller.locationController.mapMarkers,
//           polylines: controller.locationController.mapPolylines,
//           onMapCreated: controller.locationController.onMapCreated,
//           onTap: controller.onMapTap,
//         ),
//
//         // Search bar at the top
//         Positioned(
//           top: 16,
//           left: 16,
//           right: 16,
//           child: Container(
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(8),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.1),
//                   blurRadius: 8,
//                   offset: const Offset(0, 2),
//                 ),
//               ],
//             ),
//             child: TextField(
//               controller: controller.searchController.searchController,
//               decoration: InputDecoration(
//                 hintText: 'Search for a location',
//                 prefixIcon: const Icon(Icons.search),
//                 suffixIcon: IconButton(
//                   icon: const Icon(Icons.clear),
//                   onPressed: () {
//                     controller.searchController.searchController.clear();
//                     controller.searchController.clearSearchResults();
//                   },
//                 ),
//                 border: InputBorder.none,
//                 contentPadding: const EdgeInsets.symmetric(vertical: 15),
//               ),
//               onChanged: controller.searchController.onSearchInputChanged,
//             ),
//           ),
//         ),
//
//         // Search results
//         Positioned(
//           top: 70,
//           left: 16,
//           right: 16,
//           child: Obx(() {
//             if (controller.searchController.isSearching.value) {
//               return const Card(
//                 child: Padding(
//                   padding: EdgeInsets.all(16.0),
//                   child: Center(child: CircularProgressIndicator()),
//                 ),
//               );
//             } else if (controller.searchController.placePredictions.isNotEmpty) {
//               return Card(
//                 child: ListView.builder(
//                   shrinkWrap: true,
//                   physics: const ClampingScrollPhysics(),
//                   itemCount: controller.searchController.placePredictions.length,
//                   itemBuilder: (context, index) {
//                     final prediction = controller.searchController.placePredictions[index];
//                     return ListTile(
//                       title: Text(prediction.description),
//                       subtitle: Text(prediction.description),
//                       onTap: () {
//                         controller.searchController.onPredictionSelected(prediction);
//                       },
//                     );
//                   },
//                 ),
//               );
//             } else {
//               return const SizedBox.shrink();
//             }
//           }),
//         ),
//
//         // Bottom buttons
//         Positioned(
//           bottom: 16,
//           left: 16,
//           right: 16,
//           child: Row(
//             children: [
//               Expanded(
//                 child: ElevatedButton.icon(
//                   icon: const Icon(Icons.my_location),
//                   label: const Text('Use My Location'),
//                   style: ElevatedButton.styleFrom(
//                     padding: const EdgeInsets.symmetric(vertical: 12),
//                   ),
//                   onPressed: () {
//                     controller.directionsController.useCurrentLocationAsPickup();
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   // Build the route preview view
//   Widget _buildRoutePreviewView() {
//     return Stack(
//       children: [
//         // Google Map
//         GoogleMap(
//           initialCameraPosition: CameraPosition(
//             target: controller.locationController.userLocation.value ?? const LatLng(24.7136, 46.6753),
//             zoom: 14,
//           ),
//           myLocationEnabled: true,
//           myLocationButtonEnabled: false,
//           mapToolbarEnabled: false,
//           zoomControlsEnabled: false,
//           markers: controller.locationController.mapMarkers,
//           polylines: controller.locationController.mapPolylines,
//           onMapCreated: controller.locationController.onMapCreated,
//         ),
//
//         // Route info card
//         Positioned(
//           bottom: 16,
//           left: 16,
//           right: 16,
//           child: Card(
//             child: Padding(
//               padding: const EdgeInsets.all(16),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             controller.directionsController.selectedDirection.value?.title ?? '',
//                             style: const TextStyle(
//                               fontWeight: FontWeight.bold,
//                               fontSize: 18,
//                             ),
//                           ),
//                           const SizedBox(height: 4),
//                           Obx(() => Text(
//                                 'Distance: ${controller.directionsController.estimatedDistance.value}',
//                                 style: const TextStyle(color: Colors.grey),
//                               )),
//                           Obx(() => Text(
//                                 'Duration: ${controller.directionsController.estimatedDuration.value}',
//                                 style: const TextStyle(color: Colors.grey),
//                               )),
//                         ],
//                       ),
//                       Obx(() {
//                         final price = controller.directionsController.estimatedPrice.value;
//                         return Text(
//                           price != null ? '${price.toStringAsFixed(2)} SAR' : '',
//                           style: const TextStyle(
//                             fontWeight: FontWeight.bold,
//                             fontSize: 20,
//                           ),
//                         );
//                       }),
//                     ],
//                   ),
//                   const SizedBox(height: 16),
//                   Row(
//                     children: [
//                       Expanded(
//                         child: OutlinedButton(
//                           onPressed: () {
//                             controller.homeState.value = HomeState.selectingPickup;
//                           },
//                           child: const Text('Edit'),
//                         ),
//                       ),
//                       const SizedBox(width: 16),
//                       Expanded(
//                         child: ElevatedButton(
//                           onPressed: () {
//                             controller.confirmRoute();
//                           },
//                           child: const Text('Confirm'),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   // Build the trip details view
//   Widget _buildTripDetailsView() {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Trip summary
//           // TripSummaryWidget(
//           //   destination: controller.directionsController.selectedDirection.value?.title ?? '',
//           //   distance: controller.directionsController.estimatedDistance.value,
//           //   duration: controller.directionsController.estimatedDuration.value, trip: null,
//           // ),
//
//           const SizedBox(height: 24),
//
//           // Passenger details
//           Card(
//             child: Padding(
//               padding: const EdgeInsets.all(16),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const Text(
//                     'Passenger Details',
//                     style: TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 18,
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   Row(
//                     children: [
//                       Expanded(
//                         child: TextField(
//                           controller: controller.tripRequestController.passengersController,
//                           decoration: const InputDecoration(
//                             labelText: 'Passengers',
//                             border: OutlineInputBorder(),
//                             prefixIcon: Icon(Icons.person),
//                           ),
//                           keyboardType: TextInputType.number,
//                         ),
//                       ),
//                       const SizedBox(width: 16),
//                       Expanded(
//                         child: TextField(
//                           controller: controller.tripRequestController.bagsController,
//                           decoration: const InputDecoration(
//                             labelText: 'Bags',
//                             border: OutlineInputBorder(),
//                             prefixIcon: Icon(Icons.luggage),
//                           ),
//                           keyboardType: TextInputType.number,
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 16),
//                   TextField(
//                     controller: controller.tripRequestController.notesController,
//                     decoration: const InputDecoration(
//                       labelText: 'Notes for Driver',
//                       border: OutlineInputBorder(),
//                       prefixIcon: Icon(Icons.note),
//                     ),
//                     maxLines: 2,
//                   ),
//                 ],
//               ),
//             ),
//           ),
//
//           const SizedBox(height: 24),
//
//           // Payment method
//           const PaymentMethodWidget(),
//
//           const SizedBox(height: 24),
//
//           // Coupon
//           const CouponWidget(),
//
//           const SizedBox(height: 24),
//
//           // Payment summary
//           PaymentSummaryWidget(
//             basePrice: controller.directionsController.basePrice.value,
//             appFee: controller.directionsController.appFee.value,
//             taxAmount: controller.directionsController.taxAmount.value,
//             discountAmount: controller.directionsController.discountAmount.value,
//             totalPrice: controller.directionsController.estimatedPrice.value ?? 0.0,
//           ),
//
//           const SizedBox(height: 24),
//
//           // Request buttons
//           Row(
//             children: [
//               Expanded(
//                 child: ElevatedButton(
//                   onPressed: () {
//                     controller.requestRide(
//                       isScheduled: false,
//                       paymentMethod: 'cash', // Replace with actual selected payment method
//                     );
//                   },
//                   style: ElevatedButton.styleFrom(
//                     padding: const EdgeInsets.symmetric(vertical: 16),
//                   ),
//                   child: const Text('Request Now'),
//                 ),
//               ),
//               const SizedBox(width: 16),
//               Expanded(
//                 child: OutlinedButton(
//                   onPressed: () {
//                     // Show date/time picker for scheduling
//                     _showSchedulePicker();
//                   },
//                   style: OutlinedButton.styleFrom(
//                     padding: const EdgeInsets.symmetric(vertical: 16),
//                   ),
//                   child: const Text('Schedule'),
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   // Build the requesting ride view
//   Widget _buildRequestingRideView() {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const CircularProgressIndicator(),
//           const SizedBox(height: 24),
//           const Text(
//             'Finding a driver...',
//             style: TextStyle(fontSize: 18),
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton(
//             onPressed: () {
//               controller.cancelTripRequest();
//             },
//             child: const Text('Cancel Request'),
//           ),
//         ],
//       ),
//     );
//   }
//
//   // Build the tracking ride view
//   Widget _buildTrackingRideView() {
//     return Stack(
//       children: [
//         // Google Map
//         GoogleMap(
//           initialCameraPosition: CameraPosition(
//             target: controller.locationController.userLocation.value ?? const LatLng(24.7136, 46.6753),
//             zoom: 14,
//           ),
//           myLocationEnabled: true,
//           myLocationButtonEnabled: false,
//           mapToolbarEnabled: false,
//           zoomControlsEnabled: false,
//           markers: controller.locationController.mapMarkers,
//           polylines: controller.locationController.mapPolylines,
//           onMapCreated: controller.locationController.onMapCreated,
//         ),
//
//         // Trip info card
//         Positioned(
//           bottom: 16,
//           left: 16,
//           right: 16,
//           child: Obx(() {
//             final trip = controller.currentTrip.value;
//             if (trip == null) return const SizedBox.shrink();
//
//             return Card(
//               child: Padding(
//                 padding: const EdgeInsets.all(16),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Row(
//                       children: [
//                         const CircleAvatar(
//                           radius: 24,
//                           child: Icon(Icons.person),
//                         ),
//                         const SizedBox(width: 16),
//                         Expanded(
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Text(
//                                 trip.driverName ?? 'Driver',
//                                 style: const TextStyle(
//                                   fontWeight: FontWeight.bold,
//                                   fontSize: 18,
//                                 ),
//                               ),
//                               if (trip.driverRating != null)
//                                 Row(
//                                   children: [
//                                     const Icon(Icons.star, size: 16, color: Colors.amber),
//                                     Text(' ${trip.driverRating!.toStringAsFixed(1)}'),
//                                   ],
//                                 ),
//                             ],
//                           ),
//                         ),
//                         IconButton(
//                           icon: const Icon(Icons.phone),
//                           onPressed: () {
//                             // Call driver
//                           },
//                         ),
//                         IconButton(
//                           icon: const Icon(Icons.chat),
//                           onPressed: () {
//                             // Open chat
//                           },
//                         ),
//                       ],
//                     ),
//                     const Divider(),
//                     Obx(() {
//                       final status = trip.status;
//                       final eta = controller.tripTrackingController.estimatedArrivalTime.value;
//
//                       String statusText;
//                       switch (status) {
//                         case TripStatus.assigned:
//                         case TripStatus.enRouteToPickup:
//                           statusText = 'Driver is on the way';
//                           break;
//                         case TripStatus.arrivedAtPickup:
//                           statusText = 'Driver has arrived';
//                           break;
//                         case TripStatus.ongoing:
//                           statusText = 'En route to destination';
//                           break;
//                         default:
//                           statusText = 'Trip in progress';
//                       }
//
//                       return Column(
//                         children: [
//                           Text(
//                             statusText,
//                             style: const TextStyle(fontSize: 16),
//                           ),
//                           if (eta.isNotEmpty)
//                             Text(
//                               'ETA: $eta',
//                               style: const TextStyle(color: Colors.grey),
//                             ),
//                         ],
//                       );
//                     }),
//                     const SizedBox(height: 16),
//                     ElevatedButton(
//                       onPressed: () {
//                         controller.cancelTripRequest();
//                       },
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: Colors.red,
//                       ),
//                       child: const Text('Cancel Trip'),
//                     ),
//                   ],
//                 ),
//               ),
//             );
//           }),
//         ),
//       ],
//     );
//   }
//
//   // Show date/time picker for scheduling a trip
//   void _showSchedulePicker() async {
//     final now = DateTime.now();
//
//     // Show date picker
//     final date = await showDatePicker(
//       context: Get.context!,
//       initialDate: now.add(const Duration(days: 1)),
//       firstDate: now,
//       lastDate: now.add(const Duration(days: 30)),
//     );
//
//     if (date != null) {
//       // Show time picker
//       final time = await showTimePicker(
//         context: Get.context!,
//         initialTime: TimeOfDay.fromDateTime(now.add(const Duration(hours: 1))),
//       );
//
//       if (time != null) {
//         // Combine date and time
//         final scheduledDateTime = DateTime(
//           date.year,
//           date.month,
//           date.day,
//           time.hour,
//           time.minute,
//         );
//
//         // Request scheduled trip
//         controller.requestRide(
//           isScheduled: true,
//           scheduledDateTime: scheduledDateTime,
//           paymentMethod: 'cash', // Replace with actual selected payment method
//         );
//       }
//     }
//   }
// }
