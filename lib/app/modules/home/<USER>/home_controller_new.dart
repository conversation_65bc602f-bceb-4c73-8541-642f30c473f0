// import 'dart:async';
//
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
//
// import '../../../data/models/trip_request_model.dart';
// import '../../../routes/app_pages.dart';
// import 'directions_controller.dart';
// import 'location_controller.dart';
// import 'payment_controller.dart';
// import 'search_controller.dart';
// import 'trip_controller.dart';
// import 'trip_request_controller.dart';
// import 'trip_tracking_controller.dart';
//
// // Enum to manage the different stages of the home screen UI
// enum HomeState {
//   idle, // Initializing, getting location, fetching directions
//   showingDirections, // Displaying list of predefined destinations
//   selectingPickup, // Direction chosen, waiting for user to tap map for START point
//   showingRoutePreview, // Start point tapped, route calculated, preview shown
//   enteringDetails, // Route confirmed, entering passenger/bag details
//   requestingRide, // Request sent, searching for driver
//   trackingRide // Driver assigned or trip ongoing
// }
//
// class HomeControllerNew extends GetxController {
//   // --- Dependencies ---
//   final FirebaseAuth _auth = FirebaseAuth.instance;
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;
//
//   // --- Child Controllers ---
//   late LocationController locationController;
//   late DirectionsController directionsController;
//   late SearchPlacesController searchController;
//   late TripRequestController tripRequestController;
//   late TripTrackingController tripTrackingController;
//   late PaymentController paymentController;
//   late TripController tripController;
//
//   // --- Observables ---
//   final Rx<HomeState> homeState = HomeState.idle.obs;
//   final RxBool isLoading = false.obs;
//   final Rx<String?> errorMessage = Rx<String?>(null);
//   final Rxn<TripRequest> currentTrip = Rxn<TripRequest>();
//
//   // --- Private Variables ---
//   User? get currentUser => _auth.currentUser;
//   StreamSubscription? _tripSubscription;
//
//   @override
//   void onInit() {
//     super.onInit();
//     _initializeControllers();
//     initializeApp();
//   }
//
//   @override
//   void onClose() {
//     _tripSubscription?.cancel();
//     super.onClose();
//   }
//
//   // Initialize all child controllers
//   void _initializeControllers() {
//     // Initialize controllers
//     locationController = Get.put(LocationController());
//     directionsController = Get.put(DirectionsController());
//     searchController = Get.put(SearchPlacesController());
//     tripRequestController = Get.put(TripRequestController());
//     tripTrackingController = Get.put(TripTrackingController());
//     paymentController = Get.put(PaymentController());
//     tripController = Get.find<TripController>();
//   }
//
//   // Initialize the app
//   Future<void> initializeApp() async {
//     isLoading.value = true;
//     errorMessage.value = null;
//
//     try {
//       // Check for active trips first
//       await _checkForActiveTrips();
//
//       // If no active trip, proceed with normal initialization
//       if (currentTrip.value == null) {
//         // Get user location
//         await locationController.getCurrentLocation();
//
//         // Fetch available directions
//         await directionsController.fetchAvailableDirections();
//
//         // Update UI state based on initialization results
//         if (locationController.userLocation.value != null &&
//             directionsController.availableDirections.isNotEmpty) {
//           homeState.value = HomeState.showingDirections; // Ready to show list
//         } else if (currentTrip.value == null) {
//           // If directions fetch failed but location succeeded, show error
//           errorMessage.value ??= "No destinations currently available.";
//           homeState.value = HomeState.idle; // Stay idle with error message
//         }
//         // If active trip was found, state is already set by _checkForActiveTrips
//       }
//     } catch (e) {
//       debugPrint("Initialization Error: $e");
//       errorMessage.value =
//           "Initialization failed: ${e.toString()}. Please check permissions and connection.";
//       homeState.value = HomeState.idle; // Stay idle on critical error
//     } finally {
//       isLoading.value = false;
//     }
//   }
//
//   // Check for active trips
//   Future<void> _checkForActiveTrips() async {
//     if (currentUser == null) return;
//
//     try {
//       // Query for active trips (not completed or cancelled)
//       final tripQuery = await _firestore
//           .collection('tripRequests')
//           .where('userId', isEqualTo: currentUser!.uid)
//           .where('status', whereNotIn: [
//             TripRequest.statusToString(TripStatus.completed),
//             TripRequest.statusToString(TripStatus.cancelledByUser),
//             TripRequest.statusToString(TripStatus.cancelledByDriver),
//             TripRequest.statusToString(TripStatus.noDriversAvailable),
//           ])
//           .orderBy('createdAt', descending: true)
//           .limit(1)
//           .get();
//
//       if (tripQuery.docs.isNotEmpty) {
//         // Found an active trip
//         final tripDoc = tripQuery.docs.first;
//         currentTrip.value = TripRequest.fromFirestore(tripDoc);
//
//         // Set appropriate UI state
//         if (currentTrip.value!.status == TripStatus.searching) {
//           homeState.value = HomeState.requestingRide;
//         } else if (currentTrip.value!.status == TripStatus.scheduled) {
//           // For scheduled trips, show directions list but keep trip info
//           homeState.value = HomeState.showingDirections;
//         } else {
//           homeState.value = HomeState.trackingRide;
//         }
//
//         // Start listening to trip updates
//         tripTrackingController.listenToTrip(currentTrip.value!.id);
//
//         // Pass trip to trip controller for rating
//         tripController.currentTrip.value = currentTrip.value;
//       }
//     } catch (e) {
//       debugPrint("Error checking for active trips: $e");
//       // Don't show error to user, just continue with normal initialization
//     }
//   }
//
//   // Handle direction selection
//   void onDirectionSelected(int index) {
//     if (index < 0 || index >= directionsController.availableDirections.length) return;
//
//     final direction = directionsController.availableDirections[index];
//     directionsController.selectDirection(direction);
//     homeState.value = HomeState.selectingPickup;
//     errorMessage.value = null;
//   }
//
//   // Handle map tap for pickup location
//   void onMapTap(LatLng position) {
//     if (homeState.value == HomeState.selectingPickup) {
//       directionsController.setPickupLocationAndCalculateRoute(position);
//       homeState.value = HomeState.showingRoutePreview;
//     } else {
//       debugPrint(
//           "Map tapped in non-pickup-selection state: ${homeState.value}. Ignoring.");
//     }
//   }
//
//   // Confirm route and proceed to entering details
//   void confirmRoute() {
//     if (homeState.value == HomeState.showingRoutePreview &&
//         directionsController.routePoints.isNotEmpty) {
//       homeState.value = HomeState.enteringDetails;
//       // Reset trip details form
//       tripRequestController.resetForm();
//     } else {
//       debugPrint("Cannot proceed: Route not finalized or state mismatch.");
//       Get.snackbar(
//           'Error', 'Please select pickup and wait for route calculation.');
//     }
//   }
//
//   // Request a ride
//   Future<void> requestRide({
//     bool isScheduled = false,
//     DateTime? scheduledDateTime,
//     String paymentMethod = 'cash',
//   }) async {
//     if (homeState.value != HomeState.enteringDetails) {
//       errorMessage.value = 'Please complete trip details first.';
//       return;
//     }
//
//     isLoading.value = true;
//     errorMessage.value = null;
//
//     try {
//       final tripId = await tripRequestController.requestTrip(
//         isScheduled: isScheduled,
//         scheduledDateTime: scheduledDateTime,
//         paymentMethod: paymentMethod,
//       );
//
//       if (tripId != null) {
//         // Start listening to trip updates
//         tripTrackingController.listenToTrip(tripId);
//
//         // Update UI state
//         if (isScheduled) {
//           Get.snackbar(
//             'تم جدولة الرحلة',
//             'تم جدولة الرحلة بنجاح',
//             backgroundColor: Colors.green,
//             colorText: Colors.white,
//           );
//
//           // For scheduled trips, go back to directions list
//           homeState.value = HomeState.showingDirections;
//         } else {
//           homeState.value = HomeState.requestingRide;
//           // isLoading is handled by the status listener for immediate trips
//         }
//       }
//     } catch (e) {
//       debugPrint("Error requesting ride: $e");
//       errorMessage.value = "Could not request ride. Please try again.";
//       homeState.value =
//           HomeState.enteringDetails; // Revert state to allow retry
//     } finally {
//       isLoading.value = false;
//     }
//   }
//
//   // Cancel trip request
//   Future<void> cancelTripRequest({bool showMessage = true}) async {
//     if (currentTrip.value == null) return;
//
//     try {
//       await tripTrackingController.cancelTripRequest(
//         currentTrip.value!.id,
//         showMessage: showMessage,
//       );
//
//       // Reset UI
//       _resetAfterTrip();
//     } catch (e) {
//       errorMessage.value = 'Error cancelling trip: $e';
//     }
//   }
//
//   // Reset UI after trip completion or cancellation
//   void _resetAfterTrip() {
//     // Clear trip data
//     currentTrip.value = null;
//
//     // Reset map
//     locationController.clearMap();
//
//     // Reset directions
//     directionsController.reset();
//
//     // Reset trip request form
//     tripRequestController.resetForm();
//
//     // Reset UI state
//     homeState.value = HomeState.showingDirections;
//     errorMessage.value = null;
//   }
//
//   // Check if payment has been processed and show rating dialog
//   Future<void> checkPaymentAndShowRating() async {
//     if (currentTrip.value == null) return;
//
//     try {
//       // Check if payment has already been processed
//       final paymentProcessed = await paymentController.checkPaymentStatus(currentTrip.value!.id);
//
//       // Pass the completed trip to the trip controller for rating
//       tripController.currentTrip.value = currentTrip.value;
//
//       // Reset the rating dialog flag to ensure we can show the dialog
//       tripController.ratingDialogShown.value = false;
//
//       // If payment method is wallet and payment hasn't been processed yet
//       if (!paymentProcessed &&
//           currentTrip.value!.paymentMethod == 'wallet') {
//         // Process wallet payment first, then show rating dialog
//         await paymentController.processWalletPayment(currentTrip.value!);
//
//         // Show rating dialog after payment processing is complete
//         Future.delayed(const Duration(seconds: 1), () {
//           tripController.showRatingDialog();
//         });
//       } else {
//         // Payment already processed or not wallet payment, show rating dialog immediately
//         Future.delayed(const Duration(seconds: 1), () {
//           tripController.showRatingDialog();
//         });
//       }
//     } catch (e) {
//       debugPrint("Error checking payment status: $e");
//       // Still show rating dialog even if there's an error
//       tripController.currentTrip.value = currentTrip.value;
//
//       // Reset the rating dialog flag to ensure we can show the dialog
//       tripController.ratingDialogShown.value = false;
//
//       Future.delayed(const Duration(seconds: 1), () {
//         tripController.showRatingDialog();
//       });
//     }
//   }
//
//   // Format date for scheduled trips
//   String formatDateTime(DateTime dateTime) {
//     final now = DateTime.now();
//     final tomorrow = DateTime(now.year, now.month, now.day + 1);
//
//     String dateStr;
//     if (dateTime.year == now.year &&
//         dateTime.month == now.month &&
//         dateTime.day == now.day) {
//       dateStr = 'اليوم'; // Today
//     } else if (dateTime.year == tomorrow.year &&
//         dateTime.month == tomorrow.month &&
//         dateTime.day == tomorrow.day) {
//       dateStr = 'غداً'; // Tomorrow
//     } else {
//       dateStr = '${dateTime.year}/${dateTime.month}/${dateTime.day}';
//     }
//
//     // Format time as HH:MM
//     final timeStr =
//         '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
//
//     return '$dateStr $timeStr';
//   }
//
//   // Logout
//   Future<void> logout() async {
//     isLoading.value = true;
//     try {
//       debugPrint("Logging out user: ${currentUser?.uid}");
//       // Cancel all listeners before logging out
//       _tripSubscription?.cancel();
//
//       // Reset all controllers
//       locationController.clearMap();
//       directionsController.reset();
//       tripRequestController.resetForm();
//
//       // Sign out
//       await _auth.signOut();
//       Get.offAllNamed(Routes.LOGIN); // Navigate to login and clear stack
//     } catch (e) {
//       debugPrint("Error during logout: $e");
//       Get.snackbar('Logout Error', 'Could not log out. Please try again.');
//     } finally {
//       isLoading.value = false;
//     }
//   }
// }
