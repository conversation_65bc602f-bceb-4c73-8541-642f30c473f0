import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart' as loc;

class LocationController extends GetxController {
  // --- Dependencies ---
  final loc.Location _locationService = loc.Location();

  // --- Observables ---
  final Rx<LatLng?> userLocation = Rx<LatLng?>(null);
  final RxBool isLoadingLocation = false.obs;
  final Rx<String?> locationErrorMessage = Rx<String?>(null);
  
  // --- Map Controller ---
  GoogleMapController? mapController;
  final RxSet<Marker> mapMarkers = <Marker>{}.obs;
  final RxSet<Polyline> mapPolylines = <Polyline>{}.obs;
  final RxSet<Circle> mapCircles = <Circle>{}.obs;
  
  // --- Private Variables ---
  loc.LocationData? _currentLocationData;
  StreamSubscription<loc.LocationData>? _locationSubscription;
  BitmapDescriptor? _userMarkerIcon;
  BitmapDescriptor? _destinationMarkerIcon;
  BitmapDescriptor? _carIcon;

  @override
  void onInit() {
    super.onInit();
    _initLocationService();
    _loadCustomMarkerIcons();
  }

  @override
  void onClose() {
    _locationSubscription?.cancel();
    mapController?.dispose();
    super.onClose();
  }

  // Initialize location service and request permissions
  Future<void> _initLocationService() async {
    isLoadingLocation.value = true;
    locationErrorMessage.value = null;

    try {
      // Check if location service is enabled
      final serviceEnabled = await _locationService.serviceEnabled();
      if (!serviceEnabled) {
        final serviceRequest = await _locationService.requestService();
        if (!serviceRequest) {
          locationErrorMessage.value = 'Location services are disabled. Please enable location services.';
          isLoadingLocation.value = false;
          return;
        }
      }

      // Check location permission
      var permissionStatus = await _locationService.hasPermission();
      if (permissionStatus == loc.PermissionStatus.denied) {
        permissionStatus = await _locationService.requestPermission();
        if (permissionStatus != loc.PermissionStatus.granted) {
          locationErrorMessage.value = 'Location permission denied. Please grant location permission.';
          isLoadingLocation.value = false;
          return;
        }
      }

      // Get current location
      await getCurrentLocation();
    } catch (e) {
      locationErrorMessage.value = 'Error initializing location service: $e';
    } finally {
      isLoadingLocation.value = false;
    }
  }

  // Load custom marker icons
  Future<void> _loadCustomMarkerIcons() async {
    try {
      _userMarkerIcon = await BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(10, 10)),
        'assets/images/user_marker.png',
      );
      
      _destinationMarkerIcon = await BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(10, 10)),
        'assets/images/destination_marker.png',
      );
      
      _carIcon = await BitmapDescriptor.fromAssetImage(
        const ImageConfiguration(size: Size(10, 10)),
        'assets/images/car.png',
      );
    } catch (e) {
      debugPrint('Error loading custom marker icons: $e');
      // Use default markers if custom ones fail to load
    }
  }

  // Get current location
  Future<void> getCurrentLocation() async {
    isLoadingLocation.value = true;
    locationErrorMessage.value = null;

    try {
      _currentLocationData = await _locationService.getLocation();
      
      if (_currentLocationData != null &&
          _currentLocationData!.latitude != null &&
          _currentLocationData!.longitude != null) {
        userLocation.value = LatLng(
          _currentLocationData!.latitude!, 
          _currentLocationData!.longitude!
        );
        _updateUserMarker();
        _listenToLocationChanges();
        debugPrint("User location obtained: ${userLocation.value}");
      } else {
        throw Exception('Could not get current location.');
      }
    } catch (e) {
      locationErrorMessage.value = 'Error getting current location: $e';
    } finally {
      isLoadingLocation.value = false;
    }
  }

  // Listen to location changes
  void _listenToLocationChanges() {
    _locationSubscription?.cancel();
    _locationSubscription = _locationService.onLocationChanged.listen((locationData) {
      if (locationData.latitude != null && locationData.longitude != null) {
        userLocation.value = LatLng(locationData.latitude!, locationData.longitude!);
        _updateUserMarker();
      }
    }, onError: (error) {
      debugPrint("Error listening to location changes: $error");
      _locationSubscription?.cancel();
    });
  }

  // Update user marker on map
  void _updateUserMarker() {
    if (userLocation.value == null) return;
    
    final markerId = const MarkerId('user');
    final marker = Marker(
      markerId: markerId,
      position: userLocation.value!,
      icon: _userMarkerIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
      infoWindow: const InfoWindow(title: 'Your Location'),
    );
    
    // Replace existing user marker or add new one
    mapMarkers.removeWhere((m) => m.markerId == markerId);
    mapMarkers.add(marker);
  }

  // Add or update a marker on the map
  void addOrUpdateMarker(
    LatLng position,
    String markerId,
    BitmapDescriptor? icon,
    {
      bool draggable = false,
      String info = '',
      Function(LatLng)? onDragEnd,
    }
  ) {
    final markerIdVal = MarkerId(markerId);
    final marker = Marker(
      markerId: markerIdVal,
      position: position,
      draggable: draggable,
      icon: icon ?? BitmapDescriptor.defaultMarker,
      infoWindow: InfoWindow(title: info),
      onDragEnd: draggable
        ? (newPosition) {
            debugPrint("Marker '$markerId' dragged to $newPosition");
            if (onDragEnd != null) {
              onDragEnd(newPosition);
            }
          }
        : null,
    );
    
    // Replace existing marker or add new one
    mapMarkers.removeWhere((m) => m.markerId == markerIdVal);
    mapMarkers.add(marker);
  }

  // Add a polyline to the map
  void addPolyline(
    List<LatLng> points,
    String polylineId,
    {
      Color color = Colors.blue,
      int width = 5,
      bool geodesic = true,
    }
  ) {
    final polyline = Polyline(
      polylineId: PolylineId(polylineId),
      points: points,
      color: color,
      width: width,
      geodesic: geodesic,
    );
    
    // Replace existing polyline or add new one
    mapPolylines.removeWhere((p) => p.polylineId.value == polylineId);
    mapPolylines.add(polyline);
  }

  // Clear all map elements
  void clearMap() {
    mapMarkers.clear();
    mapPolylines.clear();
    mapCircles.clear();
    // Keep user marker
    _updateUserMarker();
  }

  // Initialize map controller
  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
    debugPrint("Map Controller Initialized.");
    
    // Set initial camera position based on user location (if available)
    if (userLocation.value != null) {
      updateMapCamera(userLocation.value!, zoom: 14.0);
    }
  }

  // Update map camera position
  void updateMapCamera(LatLng target, {double zoom = 15.0}) {
    mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: target,
          zoom: zoom,
        ),
      ),
    );
  }

  // Recenter map on user location
  void recenterMapOnUser() {
    if (userLocation.value != null) {
      updateMapCamera(userLocation.value!, zoom: 15.0);
    } else {
      getCurrentLocation().then((_) {
        if (userLocation.value != null) {
          updateMapCamera(userLocation.value!, zoom: 15.0);
        }
      });
    }
  }

  // Zoom map to show user and destination
  void zoomToShowUserAndDestination(LatLng destination) {
    if (userLocation.value == null) return;
    
    try {
      final bounds = LatLngBounds(
        southwest: LatLng(
          userLocation.value!.latitude < destination.latitude
              ? userLocation.value!.latitude
              : destination.latitude,
          userLocation.value!.longitude < destination.longitude
              ? userLocation.value!.longitude
              : destination.longitude,
        ),
        northeast: LatLng(
          userLocation.value!.latitude > destination.latitude
              ? userLocation.value!.latitude
              : destination.latitude,
          userLocation.value!.longitude > destination.longitude
              ? userLocation.value!.longitude
              : destination.longitude,
        ),
      );
      
      mapController?.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 80.0)
      );
    } catch (e) {
      debugPrint("Error zooming to user/destination: $e");
      recenterMapOnUser();
    }
  }

  // Zoom map to fit a route
  void zoomToFitRoute(List<LatLng> points) {
    if (points.isEmpty) return;
    
    try {
      if (points.length == 1) {
        updateMapCamera(points.first, zoom: 15.0);
      } else {
        final bounds = boundsFromLatLngList(points);
        mapController?.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 60.0)
        );
      }
    } catch (e) {
      debugPrint("Error zooming to fit route: $e");
      if (points.isNotEmpty) {
        updateMapCamera(points.first, zoom: 13.0);
      }
    }
  }

  // Calculate bounds from a list of LatLng points
  LatLngBounds boundsFromLatLngList(List<LatLng> points) {
    double? x0, x1, y0, y1;
    
    for (final point in points) {
      if (x0 == null || point.latitude < x0) x0 = point.latitude;
      if (x1 == null || point.latitude > x1) x1 = point.latitude;
      if (y0 == null || point.longitude < y0) y0 = point.longitude;
      if (y1 == null || point.longitude > y1) y1 = point.longitude;
    }
    
    // Add padding
    double latPadding = (x1! - x0!) * 0.05;
    double lngPadding = (y1! - y0!) * 0.05;
    
    if (latPadding == 0) {
      latPadding = 0.001; // Minimum padding if points are identical vertically
    }
    if (lngPadding == 0) {
      lngPadding = 0.001; // Minimum padding if points are identical horizontally
    }
    
    return LatLngBounds(
      northeast: LatLng(x1 + latPadding, y1 + lngPadding),
      southwest: LatLng(x0 - latPadding, y0 - lngPadding),
    );
  }

  // Get car icon for driver markers
  BitmapDescriptor? getCarIcon() {
    return _carIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
  }

  // Get destination icon for destination markers
  BitmapDescriptor? getDestinationIcon() {
    return _destinationMarkerIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
  }
}
