// import 'dart:async';
// import 'dart:convert';
//
// import 'package:ai_delivery_app/app/modules/home/<USER>/location_controller.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:http/http.dart' as http;
//
// import '../../../config/constants.dart';
// import '../../../data/models/trip_request_model.dart';
//
// class TripTrackingController extends GetxController {
//   // --- Dependencies ---
//   final FirebaseAuth _auth = FirebaseAuth.instance;
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;
//   final LocationController locationController = Get.find<LocationController>();
//
//   // --- Observables ---
//   final Rxn<TripRequest> currentTrip = Rxn<TripRequest>();
//   final Rx<LatLng?> driverLocation = Rx<LatLng?>(null);
//   final RxString estimatedArrivalTime = ''.obs;
//   final RxBool isTrackingTrip = false.obs;
//   final Rx<String?> trackingErrorMessage = Rx<String?>(null);
//
//   // --- Private Variables ---
//   User? get currentUser => _auth.currentUser;
//   StreamSubscription? _tripSubscription;
//   StreamSubscription? _driverLocationSubscription;
//   final String _googleApiKey = Constants.GOOGLE_MAPS_API_KEY;
//
//   @override
//   void onClose() {
//     _tripSubscription?.cancel();
//     _stopListeningToDriverLocation();
//     super.onClose();
//   }
//
//   // Listen to trip updates
//   void listenToTrip(String tripId) {
//     isTrackingTrip.value = true;
//     trackingErrorMessage.value = null;
//
//     _tripSubscription?.cancel(); // Cancel previous listener
//     _tripSubscription = _firestore
//         .collection('tripRequests')
//         .doc(tripId)
//         .snapshots()
//         .listen((snapshot) {
//       if (snapshot.exists) {
//         try {
//           currentTrip.value = TripRequest.fromFirestore(snapshot);
//           debugPrint("Trip Status Updated: ${currentTrip.value?.status}");
//           _handleStatusChange(currentTrip.value!.status);
//         } catch (e) {
//           debugPrint("Error parsing trip snapshot: $e");
//           trackingErrorMessage.value = "Error reading trip update.";
//           cancelTripRequest(tripId, showMessage: false);
//         }
//       } else {
//         debugPrint("Trip $tripId not found or deleted.");
//         // If trip disappears, cancel locally
//         cancelTripRequest(tripId, showMessage: false);
//         trackingErrorMessage.value = "The trip request could not be found.";
//       }
//     }, onError: (error) {
//       debugPrint("Error listening to trip status: $error");
//       trackingErrorMessage.value = "Connection error while tracking trip.";
//     });
//   }
//
//   // Handle trip status changes
//   void _handleStatusChange(TripStatus status) {
//     isTrackingTrip.value = true;
//
//     // Stop listening to driver location if trip is completed or cancelled
//     if (status == TripStatus.completed ||
//         status == TripStatus.cancelledByUser ||
//         status == TripStatus.cancelledByDriver ||
//         status == TripStatus.noDriversAvailable) {
//       _stopListeningToDriverLocation();
//       driverLocation.value = null;
//       locationController.mapMarkers.removeWhere((m) => m.markerId.value.startsWith('driver_'));
//     }
//
//     // Update UI based on status
//     switch (status) {
//       case TripStatus.assigned:
//       case TripStatus.enRouteToPickup:
//       case TripStatus.arrivedAtPickup:
//       case TripStatus.ongoing:
//         // Start listening to the driver's location if we have a driver ID
//         if (currentTrip.value?.driverId != null) {
//           _listenToDriverLocation(currentTrip.value!.driverId!);
//         }
//         _updateMapForTrip();
//         break;
//       default:
//         break;
//     }
//   }
//
//   // Listen to driver location updates
//   void _listenToDriverLocation(String driverId) {
//     _stopListeningToDriverLocation(); // Stop previous listener
//
//     _driverLocationSubscription = _firestore
//         .collection('drivers')
//         .doc(driverId)
//         .snapshots()
//         .listen((snapshot) {
//       if (snapshot.exists) {
//         final data = snapshot.data();
//         if (data != null && data['location'] != null) {
//           final location = data['location'] as GeoPoint;
//           driverLocation.value = LatLng(location.latitude, location.longitude);
//
//           // Update driver marker on map
//           locationController.addOrUpdateMarker(
//             driverLocation.value!,
//             'driver_$driverId',
//             locationController.getCarIcon(),
//             info: data['name'] ?? 'Your Driver',
//           );
//
//           // Update route between driver and user
//           _updateDriverRoute();
//
//           // Calculate ETA
//           _calculateETA();
//         }
//       } else {
//         // Driver document doesn't exist or has no location
//         debugPrint("Driver $driverId location data not available");
//       }
//     }, onError: (error) {
//       debugPrint("Error listening to driver location: $error");
//       trackingErrorMessage.value = "Error tracking driver location";
//     });
//   }
//
//   // Stop listening to driver location updates
//   void _stopListeningToDriverLocation() {
//     if (_driverLocationSubscription != null) {
//       _driverLocationSubscription?.cancel();
//       _driverLocationSubscription = null;
//       debugPrint("Stopped listening to driver location updates");
//     }
//   }
//
//   // Update map for trip tracking
//   void _updateMapForTrip() {
//     if (currentTrip.value == null) return;
//
//     // Clear previous markers and routes
//     locationController.mapMarkers.removeWhere((m) =>
//         m.markerId.value == 'pickup' ||
//         m.markerId.value == 'destination' ||
//         m.markerId.value.startsWith('driver_'));
//     locationController.mapPolylines.removeWhere((p) =>
//         p.polylineId.value == 'route' ||
//         p.polylineId.value == 'driver_route');
//
//     // Add pickup marker
//     final pickupLocation = LatLng(
//       currentTrip.value!.pickupLocation.latitude,
//       currentTrip.value!.pickupLocation.longitude,
//     );
//     locationController.addOrUpdateMarker(
//       pickupLocation,
//       'pickup',
//       BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
//       info: 'Pickup Location',
//     );
//
//     // Add destination marker
//     final destinationLocation = LatLng(
//       currentTrip.value!..latitude,
//       currentTrip.value!.destinationLocation.longitude,
//     );
//     locationController.addOrUpdateMarker(
//       destinationLocation,
//       'destination',
//       locationController.getDestinationIcon(),
//       info: currentTrip.value!.destinationName,
//     );
//
//     // Add route polyline if available
//     if (currentTrip.value!.routePolyline != null &&
//         currentTrip.value!.routePolyline!.isNotEmpty) {
//       final routePoints = currentTrip.value!.routePolyline!
//           .map((point) => LatLng(point['lat'] as double, point['lng'] as double))
//           .toList();
//
//       locationController.addPolyline(
//         routePoints,
//         'route',
//         color: Colors.blue,
//         width: 5,
//       );
//     }
//
//     // Add driver marker if available
//     if (driverLocation.value != null && currentTrip.value!.driverId != null) {
//       locationController.addOrUpdateMarker(
//         driverLocation.value!,
//         'driver_${currentTrip.value!.driverId}',
//         locationController.getCarIcon(),
//         info: currentTrip.value!.driverName ?? 'Your Driver',
//       );
//     }
//
//     // Zoom to show relevant points
//     _zoomToShowRelevantPoints();
//   }
//
//   // Update route between driver and user/destination
//   Future<void> _updateDriverRoute() async {
//     if (driverLocation.value == null || currentTrip.value == null) return;
//
//     try {
//       // Determine target location based on trip status
//       LatLng targetLocation;
//       if (currentTrip.value!.status == TripStatus.ongoing) {
//         // If trip is ongoing, route from driver to destination
//         targetLocation = LatLng(
//           currentTrip.value!.destinationLocation.latitude,
//           currentTrip.value!.destinationLocation.longitude,
//         );
//       } else {
//         // Otherwise, route from driver to pickup
//         targetLocation = LatLng(
//           currentTrip.value!.pickupLocation.latitude,
//           currentTrip.value!.pickupLocation.longitude,
//         );
//       }
//
//       // Get route from Google Directions API
//       final url = 'https://maps.googleapis.com/maps/api/directions/json'
//           '?origin=${driverLocation.value!.latitude},${driverLocation.value!.longitude}'
//           '&destination=${targetLocation.latitude},${targetLocation.longitude}'
//           '&mode=driving'
//           '&key=$_googleApiKey';
//
//       final response = await http.get(Uri.parse(url));
//
//       if (response.statusCode == 200) {
//         final decodedJson = json.decode(response.body);
//
//         if (decodedJson['status'] == 'OK') {
//           final routes = decodedJson['routes'] as List;
//
//           if (routes.isNotEmpty) {
//             // Extract polyline points
//             final encodedPolyline = routes[0]['overview_polyline']['points'] as String;
//             final decodedPolyline = decodePolyline(encodedPolyline);
//
//             // Draw polyline on map
//             locationController.addPolyline(
//               decodedPolyline,
//               'driver_route',
//               color: Colors.green,
//               width: 5,
//             );
//
//             // Zoom to fit driver route
//             _zoomToFitDriverRoute(decodedPolyline);
//           }
//         }
//       }
//     } catch (e) {
//       debugPrint("Error updating driver route: $e");
//       // Don't show error to user for route calculation failures
//     }
//   }
//
//   // Zoom to fit driver route
//   void _zoomToFitDriverRoute(List<LatLng> polylineCoordinates) {
//     try {
//       // Include all relevant points
//       final allPoints = <LatLng>[...polylineCoordinates];
//
//       final bounds = locationController.boundsFromLatLngList(allPoints);
//       locationController.mapController?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 80));
//     } catch (e) {
//       debugPrint("Error zooming to fit driver route: $e");
//       // Fallback to showing user and driver
//       _zoomToShowUserAndDriver();
//     }
//   }
//
//   // Zoom to show user and driver
//   void _zoomToShowUserAndDriver() {
//     if (locationController.userLocation.value == null || driverLocation.value == null) return;
//
//     try {
//       final bounds = locationController.boundsFromLatLngList(
//           [locationController.userLocation.value!, driverLocation.value!]);
//       locationController.mapController?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
//     } catch (e) {
//       debugPrint("Error zooming to show user and driver: $e");
//       // Fallback to just showing the driver
//       if (driverLocation.value != null) {
//         locationController.mapController?.animateCamera(
//           CameraUpdate.newLatLngZoom(driverLocation.value!, 15),
//         );
//       }
//     }
//   }
//
//   // Zoom to show relevant points based on trip status
//   void _zoomToShowRelevantPoints() {
//     if (currentTrip.value == null) return;
//
//     final points = <LatLng>[];
//
//     // Add pickup location
//     points.add(LatLng(
//       currentTrip.value!.pickupLocation.latitude,
//       currentTrip.value!.pickupLocation.longitude,
//     ));
//
//     // Add destination location
//     points.add(LatLng(
//       currentTrip.value!.destinationLocation.latitude,
//       currentTrip.value!.destinationLocation.longitude,
//     ));
//
//     // Add driver location if available
//     if (driverLocation.value != null) {
//       points.add(driverLocation.value!);
//     }
//
//     // Add user location if available
//     if (locationController.userLocation.value != null) {
//       points.add(locationController.userLocation.value!);
//     }
//
//     // Zoom to fit all points
//     if (points.isNotEmpty) {
//       locationController.zoomToFitRoute(points);
//     }
//   }
//
//   // Calculate ETA (Estimated Time of Arrival)
//   Future<void> _calculateETA() async {
//     if (driverLocation.value == null || currentTrip.value == null) return;
//
//     try {
//       // Determine target location based on trip status
//       LatLng targetLocation;
//       if (currentTrip.value!.status == TripStatus.ongoing) {
//         // If trip is ongoing, calculate ETA to destination
//         targetLocation = LatLng(
//           currentTrip.value!.destinationLocation.latitude,
//           currentTrip.value!.destinationLocation.longitude,
//         );
//       } else {
//         // Otherwise, calculate ETA to pickup
//         targetLocation = LatLng(
//           currentTrip.value!.pickupLocation.latitude,
//           currentTrip.value!.pickupLocation.longitude,
//         );
//       }
//
//       // Get ETA from Google Directions API
//       final url = 'https://maps.googleapis.com/maps/api/directions/json'
//           '?origin=${driverLocation.value!.latitude},${driverLocation.value!.longitude}'
//           '&destination=${targetLocation.latitude},${targetLocation.longitude}'
//           '&mode=driving'
//           '&key=$_googleApiKey';
//
//       final response = await http.get(Uri.parse(url));
//
//       if (response.statusCode == 200) {
//         final decodedJson = json.decode(response.body);
//
//         if (decodedJson['status'] == 'OK') {
//           final routes = decodedJson['routes'] as List;
//
//           if (routes.isNotEmpty) {
//             final legs = routes[0]['legs'] as List;
//
//             if (legs.isNotEmpty) {
//               // Extract duration
//               final durationText = legs[0]['duration']['text'];
//
//               // Update the ETA
//               estimatedArrivalTime.value = durationText;
//               debugPrint("Updated ETA: $durationText");
//
//               // Update trip with route information
//               await _firestore
//                   .collection('tripRequests')
//                   .doc(currentTrip.value!.id)
//                   .update({
//                 'driverEta': durationText,
//                 'lastEtaUpdate': FieldValue.serverTimestamp(),
//               });
//             }
//           }
//         }
//       }
//     } catch (e) {
//       debugPrint("Error calculating ETA: $e");
//       // Don't show error to user for ETA calculation failures
//     }
//   }
//
//   // Cancel trip request
//   Future<void> cancelTripRequest(String tripId, {bool showMessage = true}) async {
//     if (currentTrip.value == null) return;
//
//     isTrackingTrip.value = true;
//     trackingErrorMessage.value = null;
//
//     try {
//       // Update trip status in Firestore
//       await _firestore.collection('tripRequests').doc(tripId).update({
//         'status': TripRequest.statusToString(TripStatus.cancelledByUser),
//         'cancelledAt': FieldValue.serverTimestamp(),
//         'cancelledBy': currentUser?.uid,
//       });
//
//       // Stop listening to trip and driver
//       _tripSubscription?.cancel();
//       _stopListeningToDriverLocation();
//
//       // Clear trip data
//       currentTrip.value = null;
//       driverLocation.value = null;
//
//       // Show success message
//       if (showMessage) {
//         Get.snackbar(
//           'تم الإلغاء',
//           'تم إلغاء طلب الرحلة بنجاح',
//           backgroundColor: Colors.green,
//           colorText: Colors.white,
//         );
//       }
//     } catch (e) {
//       trackingErrorMessage.value = 'حدث خطأ أثناء إلغاء الرحلة: $e';
//       if (showMessage) {
//         Get.snackbar(
//           'خطأ',
//           'حدث خطأ أثناء إلغاء الرحلة',
//           backgroundColor: Colors.red,
//           colorText: Colors.white,
//         );
//       }
//     } finally {
//       isTrackingTrip.value = false;
//     }
//   }
//
//   // Decode polyline points
//   List<LatLng> decodePolyline(String encoded) {
//     List<LatLng> points = [];
//     int index = 0, len = encoded.length;
//     int lat = 0, lng = 0;
//
//     while (index < len) {
//       int b, shift = 0, result = 0;
//       do {
//         b = encoded.codeUnitAt(index++) - 63;
//         result |= (b & 0x1f) << shift;
//         shift += 5;
//       } while (b >= 0x20);
//       int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
//       lat += dlat;
//
//       shift = 0;
//       result = 0;
//       do {
//         b = encoded.codeUnitAt(index++) - 63;
//         result |= (b & 0x1f) << shift;
//         shift += 5;
//       } while (b >= 0x20);
//       int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
//       lng += dlng;
//
//       points.add(LatLng(lat / 1E5, lng / 1E5));
//     }
//
//     return points;
//   }
// }
