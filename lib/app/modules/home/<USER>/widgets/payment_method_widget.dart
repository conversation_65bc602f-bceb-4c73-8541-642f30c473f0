import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/home_controller.dart';

class PaymentMethodWidget extends GetView<HomeController> {
  const PaymentMethodWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'طريقة الدفع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  isExpanded: true,
                  value: controller.selectedPaymentMethod.value,
                  icon: const Icon(Icons.keyboard_arrow_down),
                  elevation: 16,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                    fontSize: 16,
                  ),
                  onChanged: (String? value) {
                    if (value != null) {
                      // Check if wallet has enough balance
                      if (value == 'wallet' &&
                          controller.walletBalance.value <
                              controller.totalPrice.value) {
                        Get.snackbar(
                          'خطأ',
                          'رصيد المحفظة غير كافي',
                          backgroundColor: Colors.red,
                          colorText: Colors.white,
                        );
                        return;
                      }
                      controller.selectedPaymentMethod.value = value;
                    }
                  },
                  items: [
                    _buildDropdownMenuItem(
                      'cash',
                      'الدفع عند الاستلام',
                      Icons.money,
                      context,
                    ),
                    _buildDropdownMenuItem(
                      'wallet',
                      'المحفظة (${controller.walletBalance.value.toStringAsFixed(2)} ${controller.currencySymbol.value})',
                      Icons.account_balance_wallet,
                      context,
                      isDisabled: controller.walletBalance.value <
                          controller.totalPrice.value,
                    ),
                    _buildDropdownMenuItem(
                      'card',
                      'بطاقة الائتمان',
                      Icons.credit_card,
                      context,
                    ),
                    _buildDropdownMenuItem(
                      'apple_pay',
                      'Apple Pay',
                      Icons.apple,
                      context,
                    ),
                    _buildDropdownMenuItem(
                      'stc_pay',
                      'STC Pay',
                      Icons.payment,
                      context,
                    ),
                    _buildDropdownMenuItem(
                      'mada',
                      'مدى',
                      Icons.credit_card,
                      context,
                    ),
                  ],
                ),
              ),
            ),

            // Show wallet balance if wallet is selected
            if (controller.selectedPaymentMethod.value == 'wallet')
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('رصيد المحفظة:'),
                    Text(
                      '${controller.walletBalance.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
                      style: TextStyle(
                        color: controller.walletBalance.value >=
                                controller.totalPrice.value
                            ? Colors.green
                            : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      );
    });
  }

  DropdownMenuItem<String> _buildDropdownMenuItem(
    String value,
    String title,
    IconData icon,
    BuildContext context, {
    bool isDisabled = false,
  }) {
    return DropdownMenuItem<String>(
      value: value,
      enabled: !isDisabled,
      child: Opacity(
        opacity: isDisabled ? 0.5 : 1.0,
        child: Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: isDisabled ? Colors.grey : null,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
