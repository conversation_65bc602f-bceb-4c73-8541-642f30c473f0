import 'package:ai_delivery_app/app/data/models/trip_request_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TripSummaryWidget extends StatelessWidget {
  final TripRequest trip;

  const TripSummaryWidget({
    Key? key,
    required this.trip,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'ملخص الرحلة',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const Divider(height: 24),
          
          // Trip details
          _buildDetailRow(
            context,
            Icons.location_on_outlined,
            'الوجهة: ${trip.directionTitle}',
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            context,
            Icons.access_time_outlined,
            'المدة: ${trip.durationText ?? 'غير متوفر'}',
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            context,
            Icons.route_outlined,
            'المسافة: ${trip.distanceText ?? 'غير متوفر'}',
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            context,
            Icons.people_outline,
            'الركاب: ${trip.passengers}',
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            context,
            Icons.work_outline,
            'الحقائب: ${trip.bags}',
          ),
          
          const Divider(height: 24),
          
          // Price breakdown
          _buildPriceRow(
            context,
            'السعر الأساسي',
            '${trip.basePrice.toStringAsFixed(2)} ريال',
          ),
          const SizedBox(height: 8),
          _buildPriceRow(
            context,
            'رسوم التطبيق',
            '${trip.appFee.toStringAsFixed(2)} ريال',
          ),
          const SizedBox(height: 8),
          _buildPriceRow(
            context,
            'الضريبة',
            '${trip.taxAmount.toStringAsFixed(2)} ريال',
          ),
          
          // Discount (if applied)
          if (trip.discountAmount > 0) ...[
            const SizedBox(height: 8),
            _buildPriceRow(
              context,
              'الخصم ${trip.couponCode != null ? '(${trip.couponCode})' : ''}',
              '- ${trip.discountAmount.toStringAsFixed(2)} ريال',
              isDiscount: true,
            ),
          ],
          
          const Divider(height: 24),
          
          // Total price
          _buildPriceRow(
            context,
            'المجموع',
            '${trip.totalPrice.toStringAsFixed(2)} ريال',
            isTotal: true,
          ),
          
          const SizedBox(height: 8),
          
          // Payment method
          _buildDetailRow(
            context,
            Icons.payment,
            'طريقة الدفع: ${_getPaymentMethodText(trip.paymentMethod)}',
          ),
        ],
      ),
    );
  }
  
  Widget _buildDetailRow(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Theme.of(context).colorScheme.secondary),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }
  
  Widget _buildPriceRow(BuildContext context, String label, String value, {bool isTotal = false, bool isDiscount = false}) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal
              ? theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)
              : theme.textTheme.bodyMedium,
        ),
        Text(
          value,
          style: isTotal
              ? theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                )
              : isDiscount
                  ? theme.textTheme.bodyMedium?.copyWith(color: Colors.green)
                  : theme.textTheme.bodyMedium,
        ),
      ],
    );
  }
  
  String _getPaymentMethodText(String method) {
    switch (method) {
      case 'cash':
        return 'نقدًا';
      case 'card':
        return 'بطاقة ائتمان';
      case 'wallet':
        return 'المحفظة';
      default:
        return method;
    }
  }
}
