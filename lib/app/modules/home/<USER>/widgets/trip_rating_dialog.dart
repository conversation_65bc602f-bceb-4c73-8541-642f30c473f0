import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TripRatingDialog extends StatefulWidget {
  final String title;
  final String subtitle;
  final String entityName;
  final String entityImageUrl;
  final List<String> ratingTags;
  final Function(double rating, String? comment, List<String> selectedTags) onSubmit;
  
  const TripRatingDialog({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.entityName,
    this.entityImageUrl = '',
    required this.ratingTags,
    required this.onSubmit,
  }) : super(key: key);

  @override
  State<TripRatingDialog> createState() => _TripRatingDialogState();
}

class _TripRatingDialogState extends State<TripRatingDialog> {
  double _rating = 0;
  final TextEditingController _commentController = TextEditingController();
  final List<String> _selectedTags = [];
  
  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Text(
                  widget.title,
                  style: theme.textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                
                // Entity Image and Name
                CircleAvatar(
                  radius: 40,
                  backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                  backgroundImage: widget.entityImageUrl.isNotEmpty
                      ? NetworkImage(widget.entityImageUrl)
                      : null,
                  child: widget.entityImageUrl.isEmpty
                      ? Icon(
                          Icons.person,
                          size: 40,
                          color: theme.colorScheme.primary,
                        )
                      : null,
                ),
                const SizedBox(height: 12),
                Text(
                  widget.entityName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Star Rating
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _rating = index + 1;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Icon(
                          index < _rating ? Icons.star : Icons.star_border,
                          color: index < _rating ? Colors.amber : Colors.grey,
                          size: 36,
                        ),
                      ),
                    );
                  }),
                ),
                const SizedBox(height: 8),
                Text(
                  _getRatingText(),
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: _rating > 0 ? theme.colorScheme.primary : Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Rating Tags
                if (widget.ratingTags.isNotEmpty) ...[
                  Text(
                    'ما الذي أعجبك؟',
                    style: theme.textTheme.titleSmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    alignment: WrapAlignment.center,
                    children: widget.ratingTags.map((tag) {
                      final isSelected = _selectedTags.contains(tag);
                      return FilterChip(
                        label: Text(tag),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedTags.add(tag);
                            } else {
                              _selectedTags.remove(tag);
                            }
                          });
                        },
                        backgroundColor: theme.colorScheme.surface,
                        selectedColor: theme.colorScheme.primary.withOpacity(0.2),
                        checkmarkColor: theme.colorScheme.primary,
                        labelStyle: TextStyle(
                          color: isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface,
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 20),
                ],
                
                // Comment Field
                TextField(
                  controller: _commentController,
                  decoration: InputDecoration(
                    hintText: 'أضف تعليقًا (اختياري)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: 3,
                  textCapitalization: TextCapitalization.sentences,
                ),
                const SizedBox(height: 24),
                
                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _rating > 0
                        ? () {
                            final comment = _commentController.text.trim();
                            widget.onSubmit(
                              _rating,
                              comment.isNotEmpty ? comment : null,
                              _selectedTags,
                            );
                            Get.back();
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'إرسال التقييم',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () => Get.back(),
                  child: Text(
                    'تخطي',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  String _getRatingText() {
    if (_rating == 0) return 'اختر تقييمك';
    if (_rating == 1) return 'سيء جدًا';
    if (_rating == 2) return 'سيء';
    if (_rating == 3) return 'مقبول';
    if (_rating == 4) return 'جيد';
    return 'ممتاز';
  }
}

// Helper function to show driver rating dialog
void showDriverRatingDialog(BuildContext context, String driverName, String? driverImageUrl, String tripId) {
  final driverRatingTags = [
    'سائق مهذب',
    'قيادة آمنة',
    'سيارة نظيفة',
    'وصل في الوقت المحدد',
    'مساعد',
    'يعرف الطريق جيدًا',
  ];
  
  Get.dialog(
    TripRatingDialog(
      title: 'تقييم السائق',
      subtitle: 'كيف كانت تجربتك مع السائق؟',
      entityName: driverName,
      entityImageUrl: driverImageUrl ?? '',
      ratingTags: driverRatingTags,
      onSubmit: (rating, comment, tags) {
        // TODO: Implement rating submission to Firestore
        print('Driver Rating: $rating, Comment: $comment, Tags: $tags');
      },
    ),
    barrierDismissible: false,
  );
}

// Helper function to show app rating dialog
void showAppRatingDialog(BuildContext context) {
  final appRatingTags = [
    'سهل الاستخدام',
    'تصميم جميل',
    'سريع',
    'أسعار معقولة',
    'خدمة عملاء ممتازة',
    'تطبيق موثوق',
  ];
  
  Get.dialog(
    TripRatingDialog(
      title: 'تقييم التطبيق',
      subtitle: 'كيف كانت تجربتك مع تطبيقنا؟',
      entityName: 'تطبيق التوصيل',
      entityImageUrl: '',
      ratingTags: appRatingTags,
      onSubmit: (rating, comment, tags) {
        // TODO: Implement app rating submission
        print('App Rating: $rating, Comment: $comment, Tags: $tags');
      },
    ),
    barrierDismissible: false,
  );
}
