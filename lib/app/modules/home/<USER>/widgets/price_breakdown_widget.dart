import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/home_controller.dart';

class PriceBreakdownWidget extends GetView<HomeController> {
  const PriceBreakdownWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Price Breakdown',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          
          // Base price
          _buildPriceRow(
            'Base Price',
            '${controller.currencySymbol.value} ${controller.basePrice.value.toStringAsFixed(2)}',
            theme,
          ),
          
          // App fee
          _buildPriceRow(
            'App Fee (10%)',
            '${controller.currencySymbol.value} ${controller.appFee.value.toStringAsFixed(2)}',
            theme,
          ),
          
          // Tax
          _buildPriceRow(
            'Tax (15%)',
            '${controller.currencySymbol.value} ${controller.taxAmount.value.toStringAsFixed(2)}',
            theme,
          ),
          
          // Discount (if applied)
          if (controller.discountAmount.value > 0)
            _buildPriceRow(
              'Discount',
              '- ${controller.currencySymbol.value} ${controller.discountAmount.value.toStringAsFixed(2)}',
              theme,
              isDiscount: true,
            ),
          
          const Divider(height: 16),
          
          // Total
          _buildPriceRow(
            'Total',
            '${controller.currencySymbol.value} ${controller.totalPrice.value.toStringAsFixed(2)}',
            theme,
            isTotal: true,
          ),
          
          const SizedBox(height: 15),
          
          // Coupon section
          _buildCouponSection(context, theme),
          
          const SizedBox(height: 15),
          
          // Payment method selection
          _buildPaymentMethodSection(theme),
        ],
      );
    });
  }
  
  Widget _buildPriceRow(String label, String value, ThemeData theme, {bool isTotal = false, bool isDiscount = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal 
                ? theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)
                : theme.textTheme.bodyMedium,
          ),
          Text(
            value,
            style: isTotal 
                ? theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)
                : theme.textTheme.bodyMedium?.copyWith(
                    color: isDiscount ? Colors.green : null,
                    fontWeight: isDiscount ? FontWeight.bold : null,
                  ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCouponSection(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Apply Coupon',
          style: theme.textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller.couponController,
                decoration: InputDecoration(
                  hintText: 'Enter coupon code',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  isDense: true,
                  enabled: !controller.isCouponApplied.value,
                ),
              ),
            ),
            const SizedBox(width: 10),
            Obx(() {
              if (controller.isCouponLoading.value) {
                return const SizedBox(
                  height: 24,
                  width: 24,
                  child: CircularProgressIndicator(strokeWidth: 2),
                );
              }
              
              return TextButton(
                onPressed: controller.isCouponApplied.value 
                    ? _removeCoupon 
                    : _applyCoupon,
                style: TextButton.styleFrom(
                  backgroundColor: controller.isCouponApplied.value 
                      ? Colors.red.shade50 
                      : theme.colorScheme.primary.withOpacity(0.1),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                ),
                child: Text(
                  controller.isCouponApplied.value ? 'Remove' : 'Apply',
                  style: TextStyle(
                    color: controller.isCouponApplied.value 
                        ? Colors.red 
                        : theme.colorScheme.primary,
                  ),
                ),
              );
            }),
          ],
        ),
        
        // Coupon message
        Obx(() {
          if (controller.couponMessage.value.isEmpty) {
            return const SizedBox.shrink();
          }
          
          return Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              controller.couponMessage.value,
              style: TextStyle(
                color: controller.isCouponApplied.value ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          );
        }),
      ],
    );
  }
  
  Widget _buildPaymentMethodSection(ThemeData theme) {
    final walletBalance = controller.walletBalance.value;
    final totalPrice = controller.totalPrice.value;
    final hasEnoughBalance = walletBalance >= totalPrice;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Method',
          style: theme.textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        
        // Wallet option
        _buildPaymentOption(
          'wallet',
          'Wallet (${controller.currencySymbol.value} ${walletBalance.toStringAsFixed(2)})',
          Icons.account_balance_wallet,
          theme,
          enabled: hasEnoughBalance,
          subtitle: hasEnoughBalance ? null : 'Insufficient balance',
        ),
        
        // Cash option
        _buildPaymentOption(
          'cash',
          'Cash on Delivery',
          Icons.money,
          theme,
        ),
        
        // Credit card option
        _buildPaymentOption(
          'card',
          'Credit/Debit Card',
          Icons.credit_card,
          theme,
        ),
      ],
    );
  }
  
  Widget _buildPaymentOption(
    String value, 
    String title, 
    IconData icon, 
    ThemeData theme, 
    {bool enabled = true, String? subtitle}
  ) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.5,
      child: RadioListTile<String>(
        value: value,
        groupValue: controller.selectedPaymentMethod.value,
        onChanged: enabled ? (newValue) {
          if (newValue != null) {
            controller.selectedPaymentMethod.value = newValue;
          }
        } : null,
        title: Row(
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        subtitle: subtitle != null ? Text(
          subtitle,
          style: TextStyle(color: Colors.red.shade300, fontSize: 12),
        ) : null,
        contentPadding: EdgeInsets.zero,
        dense: true,
      ),
    );
  }
  
  void _applyCoupon() async {
    final couponCode = controller.couponController.text.trim();
    if (couponCode.isEmpty) {
      controller.couponMessage.value = 'Please enter a coupon code';
      return;
    }
    
    controller.isCouponLoading.value = true;
    
    try {
      // Call the payment service to apply the coupon
      final result = await controller.applyCoupon(couponCode);
      
      if (result['valid']) {
        controller.isCouponApplied.value = true;
        controller.couponMessage.value = 'Coupon applied successfully!';
        controller.discountAmount.value = result['discountAmount'];
        controller.couponId.value = result['couponId'];
        controller.couponCode.value = couponCode;
        
        // Recalculate total price
        controller.totalPrice.value = controller.basePrice.value + 
                                     controller.appFee.value + 
                                     controller.taxAmount.value - 
                                     controller.discountAmount.value;
      } else {
        controller.couponMessage.value = result['message'] ?? 'Invalid coupon';
      }
    } catch (e) {
      controller.couponMessage.value = 'Error applying coupon';
    } finally {
      controller.isCouponLoading.value = false;
    }
  }
  
  void _removeCoupon() {
    controller.couponController.clear();
    controller.isCouponApplied.value = false;
    controller.couponMessage.value = '';
    controller.discountAmount.value = 0.0;
    controller.couponId.value = '';
    controller.couponCode.value = '';
    
    // Recalculate total price
    controller.totalPrice.value = controller.basePrice.value + 
                                 controller.appFee.value + 
                                 controller.taxAmount.value;
  }
}
