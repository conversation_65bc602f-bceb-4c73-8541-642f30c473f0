import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/home_controller.dart';

class PaymentSummaryWidget extends GetView<HomeController> {
  const PaymentSummaryWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الدفع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            _buildPriceRow(
              'السعر الأساسي',
              '${controller.basePrice.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
              context,
            ),
            _buildPriceRow(
              'رسوم التطبيق',
              '${controller.appFee.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
              context,
            ),
            _buildPriceRow(
              'الضريبة',
              '${controller.taxAmount.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
              context,
            ),
            if (controller.discountAmount.value > 0)
              _buildPriceRow(
                'الخصم',
                '- ${controller.discountAmount.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
                context,
                isDiscount: true,
              ),
            const Divider(height: 24),
            _buildPriceRow(
              'المجموع',
              '${controller.totalPrice.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
              context,
              isTotal: true,
            ),
            const SizedBox(height: 16),
            _buildPaymentMethod(context),
          ],
        ),
      );
    });
  }

  Widget _buildPriceRow(String label, String value, BuildContext context,
      {bool isTotal = false, bool isDiscount = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isDiscount
                  ? Colors.green
                  : isTotal
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethod(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'طريقة الدفع',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              _getPaymentIcon(controller.selectedPaymentMethod.value),
              const SizedBox(width: 12),
              Text(
                _getPaymentMethodName(controller.selectedPaymentMethod.value),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              if (controller.selectedPaymentMethod.value == 'wallet')
                Text(
                  '${controller.walletBalance.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
                  style: TextStyle(
                    fontSize: 14,
                    color: controller.walletBalance.value >= controller.totalPrice.value
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _getPaymentIcon(String paymentMethod) {
    switch (paymentMethod) {
      case 'cash':
        return const Icon(Icons.money, color: Colors.green);
      case 'wallet':
        return const Icon(Icons.account_balance_wallet, color: Colors.blue);
      case 'card':
        return const Icon(Icons.credit_card, color: Colors.orange);
      case 'apple_pay':
        return const Icon(Icons.apple, color: Colors.black);
      case 'stc_pay':
        return const Icon(Icons.payment, color: Colors.purple);
      case 'mada':
        return const Icon(Icons.credit_card, color: Colors.teal);
      default:
        return const Icon(Icons.money, color: Colors.grey);
    }
  }

  String _getPaymentMethodName(String paymentMethod) {
    switch (paymentMethod) {
      case 'cash':
        return 'الدفع عند الاستلام';
      case 'wallet':
        return 'المحفظة';
      case 'card':
        return 'بطاقة الائتمان';
      case 'apple_pay':
        return 'Apple Pay';
      case 'stc_pay':
        return 'STC Pay';
      case 'mada':
        return 'مدى';
      default:
        return 'الدفع عند الاستلام';
    }
  }
}
