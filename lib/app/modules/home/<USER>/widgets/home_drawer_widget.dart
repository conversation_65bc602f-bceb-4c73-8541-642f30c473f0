import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:ai_delivery_app/app/data/models/wallet_transaction_model.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/home_controller.dart';
import 'package:ai_delivery_app/app/modules/widgets/language_selector.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:ai_delivery_app/app/theme/app_colors.dart';
import 'package:ai_delivery_app/app/theme/app_dimensions.dart';
import 'package:ai_delivery_app/app/theme/app_text_styles.dart';
import 'package:ai_delivery_app/app/widgets/modern_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';

class HomeDrawerWidget extends GetView<HomeController> {
  const HomeDrawerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: <PERSON><PERSON><PERSON>(
        child: Drawer(
          backgroundColor: AppColors.background,
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.backgroundGradient,
            ),
            child: Column(
            children: [
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: <Widget>[
                    _buildHeader(context),
                    const SizedBox(height: 8),

                    // User Profile Section
                    _buildSectionHeader(context, 'الحساب الشخصي'),
                    _buildMenuItem(
                      icon: Icons.admin_panel_settings_outlined,
                      title: 'admin',
                      onTap: () => Get.toNamed(Routes.ADMIN_DASHBOARD),
                    ),
                    _buildMenuItem(
                      icon: Icons.person,
                      title: 'الملف الشخصي',
                      onTap: () => _navigateToProfile(),
                    ),
                    _buildMenuItem(
                      icon: Icons.edit,
                      title: 'تعديل الملف الشخصي',
                      onTap: () => _navigateToEditProfile(),
                    ),
                    _buildMenuItem(
                      icon: Icons.history,
                      title: 'سجل الرحلات',
                      onTap: () => _navigateToTripHistory(),
                    ),
                    _buildMenuItem(
                      icon: Icons.directions_car_outlined,
                      title: 'رحلاتي',
                      onTap: () => _navigateToUserTrips(),
                    ),

                    const Divider(),

                    // Wallet Section
                    _buildSectionHeader(context, 'المحفظة'),
                    _buildWalletSummary(context),
                    _buildMenuItem(
                      icon: Icons.account_balance_wallet,
                      title: 'المعاملات المالية',
                      onTap: () => _navigateToWalletTransactions(),
                    ),
                    _buildMenuItem(
                      icon: Icons.add_card,
                      title: 'إضافة رصيد',
                      onTap: () => _navigateToAddFunds(),
                    ),

                    const Divider(),

                    // Support Section
                    _buildSectionHeader(context, 'الدعم والمساعدة'),
                    _buildMenuItem(
                      icon: Icons.question_answer,
                      title: 'الأسئلة الشائعة',
                      onTap: () => _navigateToFAQ(),
                    ),
                    _buildMenuItem(
                      icon: Icons.support_agent,
                      title: 'التحدث مع الدعم',
                      badge: '1',
                      onTap: () => _navigateToSupportChat(),
                    ),
                    _buildMenuItem(
                      icon: Icons.help_outline,
                      title: 'مركز المساعدة',
                      onTap: () => _navigateToHelpCenter(),
                    ),

                    const Divider(),

                    // Settings & Language
                    _buildMenuItem(
                      icon: Icons.settings,
                      title: 'الإعدادات',
                      onTap: () => _navigateToSettings(),
                    ),
                    // Language Selector
                    const LanguageSelector(isDrawer: true),
                    _buildMenuItem(
                      icon: Icons.logout,
                      title: 'تسجيل الخروج',
                      onTap: () => _logout(),
                      textColor: Colors.red,
                    ),
                  ],
                ),
              ),
              // App Version
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'الإصدار 1.0.0',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Obx(() {
      final user = controller.currentUserModel.value;
      final walletBalance = user?.walletBalance ?? 0.0;
      final completedTrips = user?.completedTrips ?? 0;
      final rating = user?.rating ?? 0.0;

      return Container(
        padding: const EdgeInsets.fromLTRB(16, 40, 16, 20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.primary.withOpacity(0.8),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // User Avatar and Name
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  backgroundImage: user?.profileImageUrl != null
                      ? NetworkImage(user!.profileImageUrl!)
                      : null,
                  child: user?.profileImageUrl == null
                      ? Icon(Icons.person,
                          color: theme.colorScheme.primary, size: 40)
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user?.name ?? controller.userDisplayName,
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        controller.userEmail,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Rating
                      if (rating > 0)
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              rating.toStringAsFixed(1),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Stats Row
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  // Trips Count
                  _buildStatItem(
                    icon: Icons.directions_car,
                    value: completedTrips.toString(),
                    label: 'الرحلات',
                  ),
                  // Wallet Balance
                  _buildStatItem(
                    icon: Icons.account_balance_wallet,
                    value: '${walletBalance.toStringAsFixed(2)} ر.س',
                    label: 'الرصيد',
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildWalletSummary(BuildContext context) {
    final theme = Theme.of(context);

    return Obx(() {
      final walletBalance =
          controller.currentUserModel.value?.walletBalance ?? 0.0;
      final transactions = controller.walletTransactions;

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'رصيد المحفظة',
                    style: theme.textTheme.titleMedium,
                  ),
                  Text(
                    '${walletBalance.toStringAsFixed(2)} ر.س',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Recent Transactions
              if (transactions.isNotEmpty) ...[
                Text(
                  'آخر المعاملات',
                  style: theme.textTheme.titleSmall,
                ),
                const SizedBox(height: 8),
                ...transactions.take(2).map((transaction) =>
                    _buildTransactionItem(context, transaction)),
              ],
            ],
          ),
        ),
      );
    });
  }

  Widget _buildTransactionItem(
      BuildContext context, WalletTransaction transaction) {
    final theme = Theme.of(context);
    final isPositive = transaction.type == TransactionType.deposit ||
        transaction.type == TransactionType.refund ||
        transaction.type == TransactionType.bonus;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          // Transaction Icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isPositive
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getTransactionIcon(transaction.type),
              color: isPositive ? Colors.green : Colors.red,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _formatDate(transaction.timestamp.toDate()),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          // Amount
          Text(
            '${isPositive ? '+' : '-'} ${transaction.amount.toStringAsFixed(2)} ر.س',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isPositive ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return Icons.add_circle_outline;
      case TransactionType.withdrawal:
        return Icons.remove_circle_outline;
      case TransactionType.tripPayment:
        return Icons.directions_car;
      case TransactionType.refund:
        return Icons.replay;
      case TransactionType.bonus:
        return Icons.card_giftcard;
      case TransactionType.payment:
        return Icons.payment;
      case TransactionType.earning:
        return Icons.account_balance_wallet;
      case TransactionType.other:
        return Icons.swap_horiz;
      case TransactionType.all:
        return Icons.all_inclusive;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    String? subtitle,
    String? badge,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor ?? Colors.black87),
      title: Text(
        title,
        style: TextStyle(fontSize: 14, color: textColor),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey),
            )
          : null,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (badge != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                badge,
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          const SizedBox(width: 4),
          const Icon(Icons.chevron_left, size: 20),
        ],
      ),
      onTap: onTap,
    );
  }

  // Navigation Methods
  void _navigateToProfile() {
    Get.back();
    Get.toNamed(Routes.PROFILE);
  }

  void _navigateToEditProfile() {
    Get.back();
    Get.toNamed(Routes.EDIT_PROFILE);
  }

  void _navigateToTripHistory() {
    Get.back();
    Get.toNamed(Routes.TRIP_HISTORY);
  }

  void _navigateToUserTrips() {
    Get.back();
    Get.toNamed(Routes.USER_TRIPS);
  }

  void _navigateToWalletTransactions() {
    Get.back();
    Get.toNamed(Routes.WALLET);
  }

  void _navigateToAddFunds() {
    Get.back();
    Get.toNamed(Routes.ADD_FUNDS);
  }

  void _navigateToFAQ() {
    Get.back();
    Get.toNamed(Routes.FAQ);
  }

  void _navigateToSupportChat() {
    Get.back();
    Get.toNamed(Routes.SUPPORT_CHAT);
  }

  void _navigateToHelpCenter() {
    Get.back();
    Get.toNamed(Routes.HELP_CENTER);
  }

  void _navigateToSettings() {
    Get.back();
    Get.toNamed(Routes.SETTINGS);
  }

  void _logout() {
    Get.back();
    controller.logout();
  }
}
