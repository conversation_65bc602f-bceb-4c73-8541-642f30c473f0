import 'package:ai_delivery_app/app/data/models/cancellation_reason_model.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TripCancellationDialog extends StatefulWidget {
  final Function(String reason, String? customReason) onConfirm;
  
  const TripCancellationDialog({
    Key? key,
    required this.onConfirm,
  }) : super(key: key);

  @override
  State<TripCancellationDialog> createState() => _TripCancellationDialogState();
}

class _TripCancellationDialogState extends State<TripCancellationDialog> {
  final List<CancellationReason> _reasons = getPredefinedCancellationReasons()
      .where((reason) => reason.category == 'user' || reason.category == 'both')
      .toList();
  
  String? _selectedReasonId;
  final TextEditingController _customReasonController = TextEditingController();
  bool _showCustomReasonField = false;
  
  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        title: Text(
          'سبب إلغاء الرحلة',
          style: theme.textTheme.titleLarge,
          textAlign: TextAlign.center,
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'يرجى اختيار سبب إلغاء الرحلة',
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // Reasons List
              ...List.generate(_reasons.length, (index) {
                final reason = _reasons[index];
                return _buildReasonOption(reason, theme);
              }),
              
              // Other Reason Option
              _buildReasonOption(
                CancellationReason(
                  id: 'other',
                  reason: 'سبب آخر',
                  category: 'both',
                ),
                theme,
              ),
              
              // Custom Reason Text Field
              if (_showCustomReasonField) ...[
                const SizedBox(height: 16),
                TextField(
                  controller: _customReasonController,
                  decoration: InputDecoration(
                    hintText: 'يرجى كتابة السبب',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  maxLines: 2,
                  textCapitalization: TextCapitalization.sentences,
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'إلغاء',
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
          ElevatedButton(
            onPressed: _selectedReasonId == null
                ? null
                : () {
                    final String? customReason = _selectedReasonId == 'other'
                        ? _customReasonController.text.trim()
                        : null;
                    
                    if (_selectedReasonId == 'other' && 
                        (customReason == null || customReason.isEmpty)) {
                      // Show error if custom reason is empty
                      Get.snackbar(
                        'خطأ',
                        'يرجى كتابة سبب الإلغاء',
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                      return;
                    }
                    
                    // Get the reason text
                    String reasonText = _reasons
                        .firstWhere(
                          (r) => r.id == _selectedReasonId,
                          orElse: () => _reasons.first,
                        )
                        .reason;
                    
                    widget.onConfirm(reasonText, customReason);
                    Get.back();
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
            child: const Text('تأكيد الإلغاء'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildReasonOption(CancellationReason reason, ThemeData theme) {
    final isSelected = _selectedReasonId == reason.id;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedReasonId = reason.id;
          _showCustomReasonField = reason.id == 'other';
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? theme.colorScheme.primary.withOpacity(0.1) : null,
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? theme.colorScheme.primary : theme.disabledColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                reason.reason,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Function to show the cancellation dialog
void showTripCancellationDialog(BuildContext context, HomeController controller) {
  Get.dialog(
    TripCancellationDialog(
      onConfirm: (reason, customReason) {
        // Handle cancellation with reason
        controller.cancelTripRequest(
          reason: reason,
          customReason: customReason,
        );
      },
    ),
    barrierDismissible: false,
  );
}
