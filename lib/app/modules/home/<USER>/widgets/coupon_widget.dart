import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/home_controller.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_dimensions.dart';
import '../../../widgets/modern_button.dart';
import '../../../widgets/modern_card.dart';
import '../../../widgets/modern_text_field.dart';

class CouponWidget extends GetView<HomeController> {
  const CouponWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'كوبون الخصم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            if (controller.isCouponApplied.value)
              _buildAppliedCoupon(context)
            else
              _buildCouponInput(context),
            if (controller.couponMessage.value.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  controller.couponMessage.value,
                  style: TextStyle(
                    color: controller.isCouponApplied.value
                        ? Colors.green
                        : Colors.red,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildAppliedCoupon(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.green,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تم تطبيق الكوبون: ${controller.couponCode.value}',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'خصم: ${controller.discountAmount.value.toStringAsFixed(2)} ${controller.currencySymbol.value}',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.green),
            onPressed: () => _removeCoupon(),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponInput(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller.couponController,
                decoration: InputDecoration(
                  hintText: 'أدخل كود الكوبون',
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  suffixIcon: controller.isCouponLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: Padding(
                            padding: EdgeInsets.all(8.0),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                        )
                      : null,
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.spacing8),
            ModernButton(
              text: 'تطبيق',
              onPressed: controller.isCouponLoading.value
                  ? null
                  : () => _applyCoupon(controller.couponController.text),
              type: ModernButtonType.primary,
              size: ModernButtonSize.medium,
              isLoading: controller.isCouponLoading.value,
            ),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _showAvailableCoupons,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.local_offer,
                color: Theme.of(context).primaryColor,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                'عرض الكوبونات المتاحة',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _applyCoupon(String couponCode) async {
    if (couponCode.isEmpty) {
      controller.couponMessage.value = 'الرجاء إدخال كود الكوبون';
      return;
    }

    controller.isCouponLoading.value = true;
    controller.couponMessage.value = '';

    try {
      // Call the payment service to apply the coupon
      final result = await controller.applyCoupon(couponCode);

      if (result['valid']) {
        controller.isCouponApplied.value = true;
        controller.couponId.value = result['couponId'];
        controller.couponCode.value = couponCode;
        controller.discountAmount.value = result['discountAmount'];
        controller.couponMessage.value = result['message'];

        // Recalculate total price
        controller.totalPrice.value = controller.basePrice.value +
            controller.appFee.value +
            controller.taxAmount.value -
            controller.discountAmount.value;
      } else {
        controller.isCouponApplied.value = false;
        controller.couponMessage.value = result['message'];
      }
    } catch (e) {
      controller.couponMessage.value = 'حدث خطأ أثناء تطبيق الكوبون';
    } finally {
      controller.isCouponLoading.value = false;
    }
  }

  void _removeCoupon() {
    controller.couponCode.value = '';
    controller.couponId.value = '';
    controller.discountAmount.value = 0.0;
    controller.isCouponApplied.value = false;
    controller.couponMessage.value = '';
    controller.couponController.clear();

    // Recalculate total price
    controller.totalPrice.value = controller.basePrice.value +
        controller.appFee.value +
        controller.taxAmount.value;
  }

  Future<void> _showAvailableCoupons() async {
    // Show loading dialog
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // Get available coupons using the HomeController
      final coupons = await controller.getAvailableCoupons();

      // Close loading dialog
      Get.back();

      if (coupons.isEmpty) {
        Get.snackbar(
          'معلومات',
          'لا توجد كوبونات متاحة حالياً',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.blue,
          colorText: Colors.white,
        );
        return;
      }

      // Show coupons dialog
      Get.dialog(
        Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'الكوبونات المتاحة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Get.back(),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: Get.height * 0.5,
                  ),
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: coupons.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final coupon = coupons[index];
                      return ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(
                          coupon['title'] ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(coupon['description'] ?? ''),
                            const SizedBox(height: 4),
                            Text(
                              'خصم: ${coupon['discountAmount'].toStringAsFixed(2)} ${controller.currencySymbol.value}',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (coupon['endDate'] != null)
                              Text(
                                'ينتهي في: ${_formatDate(coupon['endDate'].toDate())}',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        trailing: ElevatedButton(
                          onPressed: () {
                            Get.back(); // Close dialog
                            controller.couponController.text = coupon['code'];
                            _applyCoupon(coupon['code']);
                          },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          child: const Text('استخدام'),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } catch (e) {
      // Close loading dialog if open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل الكوبونات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
