import 'package:ai_delivery_app/app/modules/home/<USER>/widgets/coupon_widget.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/widgets/home_drawer_widget.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/widgets/payment_method_widget.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/widgets/payment_summary_widget.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/widgets/trip_summary_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../data/models/trip_request_model.dart';
import '../../../routes/app_pages.dart';
import '../controllers/chat_controller.dart';
import '../controllers/home_controller.dart';
import 'chat_view.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context); // Access theme data for styling

    return Scaffold(
      // AppBar changes based on state
      appBar: AppBar(
        title: Obx(() => Text(_getAppBarTitle(controller.homeState.value),style: TextStyle(color: Colors.white),)),
        // Conditional Back Button or Drawer Icon
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),        actions: [
          // Conditional Reset Button
          Obx(() {
            // Show reset only when a pickup point is placed or route is shown
            if (controller.homeState.value == HomeState.showingRoutePreview ||
                (controller.homeState.value == HomeState.selectingPickup &&
                    controller.selectedPickupLocation.value != null)) {
              return IconButton(
                icon: const Icon(Icons.refresh),
                tooltip: 'Reset Pickup/Route',
                onPressed: controller
                    .backToPickupSelection, // Reset to pickup selection state
              );
            }
            return const SizedBox.shrink();
          }),
          Obx(() {
            // Show back button when selecting pickup, viewing route, or entering details
            if ([
              HomeState.selectingPickup,
              HomeState.showingRoutePreview,
              HomeState.enteringDetails
            ].contains(controller.homeState.value)) {
              return IconButton(
                icon: const Icon(Icons.arrow_back),
                tooltip: 'Go Back',
                onPressed: () {
                  // Determine correct back action based on current state
                  if (controller.homeState.value == HomeState.enteringDetails) {
                    controller.backToRoutePreview();
                  } else if (controller.homeState.value ==
                      HomeState.showingRoutePreview) {
                    controller.backToPickupSelection();
                  } else if (controller.homeState.value ==
                      HomeState.selectingPickup) {
                    controller.backToDirections();
                  }
                },
              );
            }
            // Otherwise, return null to let Scaffold show the default drawer icon
            return SizedBox();
          }),
          // Logout Button (always visible for now)
        ],
      ),
      // Add the Drawer
      drawer: HomeDrawerWidget(),

      // Floating Action Button for chat when in tracking state or trips button in other states
      // floatingActionButton: Obx(() {
      //   if (controller.homeState.value == HomeState.trackingRide &&
      //       controller.currentTrip.value != null) {
      //     // Chat button during active trips
      //     return FloatingActionButton(
      //       onPressed: () {
      //         // Navigate to chat view
      //         Get.to(() => ChatView(
      //           tripId: controller.currentTrip.value!.id,
      //         ));
      //       },
      //       backgroundColor: theme.colorScheme.primary,
      //       child: Stack(
      //         clipBehavior: Clip.none,
      //         children: [
      //           const Icon(Icons.chat, color: Colors.white),
      //           // Show badge if there are unread messages
      //           if (controller.currentTrip.value!.unreadMessageCount > 0)
      //             Positioned(
      //               right: -5,
      //               top: -5,
      //               child: Container(
      //                 padding: const EdgeInsets.all(4),
      //                 decoration: BoxDecoration(
      //                   color: Colors.red,
      //                   borderRadius: BorderRadius.circular(10),
      //                 ),
      //                 constraints: const BoxConstraints(
      //                   minWidth: 16,
      //                   minHeight: 16,
      //                 ),
      //                 child: Text(
      //                   '${controller.currentTrip.value!.unreadMessageCount}',
      //                   style: const TextStyle(
      //                     color: Colors.white,
      //                     fontSize: 10,
      //                   ),
      //                   textAlign: TextAlign.center,
      //                 ),
      //               ),
      //             ),
      //         ],
      //       ),
      //     );
      //   } else if (controller.homeState.value == HomeState.idle ||
      //       controller.homeState.value == HomeState.showingDirections) {
      //     // My Trips button in idle or directions state
      //     return FloatingActionButton.extended(
      //       onPressed: () => Get.toNamed(Routes.USER_TRIPS),
      //       backgroundColor: theme.colorScheme.primary,
      //       icon: const Icon(Icons.directions_car, color: Colors.white),
      //       label: const Text('رحلاتي', style: TextStyle(color: Colors.white)),
      //     );
      //   }
      //   return const SizedBox.shrink(); // No FAB in other states
      // }),

      // Body rebuilds based on controller's reactive state
      body: Obx(() {
        // --- Initial Loading State ---
        if (controller.homeState.value == HomeState.idle &&
            controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        // --- Initial Error State ---
        if (controller.homeState.value == HomeState.idle &&
            controller.errorMessage.value != null) {
          return _buildErrorWidget(controller.errorMessage.value!,
                  () => controller.loadUserData()); // Offer logout on init fail
        }

        // --- Main Content Stack (Map + Overlays) ---
        return WillPopScope(
          onWillPop: () async {
            // Handle back press based on state
            if (controller.homeState.value == HomeState.selectingPickup) {
              if (controller.placePredictions.isNotEmpty) {
                controller
                    .backToPickupSelection(); // Clear search results first
                return false; // Don't pop route yet
              } else {
                controller.backToDirections(); // Go back to directions list
                return false; // Don't pop route
              }
            } else if (controller.homeState.value ==
                HomeState.showingRoutePreview) {
              controller.backToPickupSelection();
              return false;
            } else if (controller.homeState.value ==
                HomeState.enteringDetails) {
              controller.backToRoutePreview();
              return false;
            }
            // Allow normal back press in other states (like showingDirections or tracking)
            return true;
          },
          child: Stack(
            children: [
              // --- Google Map View ---
              // Show map once user location is available and we are past idle state
              if (controller.userLocation.value != null &&
                  controller.homeState.value != HomeState.idle)
                Obx(() => GoogleMap(
                  onMapCreated: controller.onMapCreated,
                  initialCameraPosition: CameraPosition(
                    target: controller
                        .userLocation.value!, // Center on user initially
                    zoom: 14.0,
                  ),
                  markers: controller.mapMarkers, // Bind markers reactively
                  polylines:
                  controller.mapPolylines, // Bind polylines reactively
                  myLocationEnabled:
                  false, // Use custom marker via controller
                  myLocationButtonEnabled:
                  true, // Allow user to recenter map
                  onTap: controller
                      .handleMapTap, // Handle taps for pickup selection
                  padding: EdgeInsets.only(
                    // Adjust bottom padding based on overlay height
                    bottom: _getMapBottomPadding(
                        controller.homeState.value, context),
                    // Add top padding only when instruction banner is visible
                    top: controller.homeState.value ==
                        HomeState.selectingPickup
                        ? 80
                        : 10,
                  ),
                  zoomControlsEnabled: false, // Disable default +/- buttons
                )),

              // --- Top Instruction Banner ---
              // Shown only during the pickup selection phase
              // _buildInstructionBanner(context, theme),
              _buildTopSection(context, theme),

              // --- Bottom Overlay Widgets ---
              // Dynamically builds the correct bottom sheet/card based on state
              _buildBottomOverlayWidget(context, theme),

              // --- Search Results Overlay --- NEW ---
              // Shown on top of the map when predictions are available
              _buildSearchResultsList(context, theme),
              // --- Global Loading Indicator (for actions like requesting ride) ---
              if (controller.isLoading.value &&
                  controller.homeState.value != HomeState.idle)
                Positioned.fill(
                  child: Container(
                    color: Colors.black
                        .withOpacity(0.3), // Semi-transparent overlay
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                ),

              // --- Error Snackbar Trigger ---
              // Shows transient errors without blocking UI
              if (controller.errorMessage.value != null &&
                  controller.homeState.value != HomeState.idle)
                _showErrorSnackbar(controller.errorMessage.value!, theme),
            ],
          ),
        );
      }),
    );
  }

  // === Helper Widgets for Building UI Sections ===

  // Builds the title shown in the AppBar
  String _getAppBarTitle(HomeState state) {
    switch (state) {
      case HomeState.idle:
        return 'loading_app'.tr;
      case HomeState.showingDirections:
        return 'choose_destination'.tr;
      case HomeState.selectingPickup:
        return 'set_pickup'.tr;
      case HomeState.showingRoutePreview:
        return 'confirm_route'.tr;
      case HomeState.enteringDetails:
        return 'trip_details'.tr;
      case HomeState.requestingRide:
        return 'finding_driver'.tr;
      case HomeState.trackingRide:
        return 'tracking_trip'.tr;
      default:
        return 'ai_delivery'.tr;

    }
  }

  // Calculates bottom padding for the map to avoid overlay obstruction
  double _getMapBottomPadding(HomeState state, BuildContext context) {
    // Estimate the height of the bottom overlay widget for each state
    switch (state) {
      case HomeState.showingDirections:
        return MediaQuery.of(context).size.height *
            0.45; // Draggable sheet typical height
      case HomeState.showingRoutePreview:
        return 190; // Height of route preview card + padding
      case HomeState.enteringDetails:
        return 320; // Estimated height of details form
      case HomeState.requestingRide:
      case HomeState.trackingRide:
        return 230; // Estimated height of status widget
      case HomeState.selectingPickup:
        return 60; // Minimal padding when map is main focus
      default:
        return 0; // No overlay in idle state
    }
  }

  // Builds the banner shown at the top during pickup selection
  Widget _buildInstructionBanner(BuildContext context, ThemeData theme) {
    // Only show when selecting pickup and a direction is chosen
    if (controller.homeState.value != HomeState.selectingPickup ||
        controller.selectedDirection.value == null) {
      return const SizedBox.shrink(); // Hide otherwise
    }

    return Positioned(
      top: 10,
      left: 10,
      right: 10,
      child: Material(
        // Use Material for elevation, shape, and theme adherence
        elevation: 4.0,
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary
                .withOpacity(0.95), // Use primary color
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Text(
            'Tap on the map to set PICKUP for ${controller.selectedDirection.value!.title}',
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.onPrimary, // Ensure contrast
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // Main switcher for the bottom overlay content
  Widget _buildBottomOverlayWidget(BuildContext context, ThemeData theme) {
    switch (controller.homeState.value) {
      case HomeState.showingDirections:
        return _buildDirectionsList(context, theme);
      case HomeState.showingRoutePreview:
        return _buildRoutePreviewCard(context, theme);
      case HomeState.enteringDetails:
        return _buildTripDetailsForm(context, theme);
      case HomeState.requestingRide:
      case HomeState.trackingRide:
        return _buildTripStatusUI(context, theme);
    // No persistent bottom overlay during idle or pickup selection
      case HomeState.idle:
      case HomeState.selectingPickup:
      default:
        return const SizedBox.shrink();
    }
  }

  // --- UI Section: Directions List (Draggable Sheet) ---
  Widget _buildDirectionsList(BuildContext context, ThemeData theme) {
    return DraggableScrollableSheet(
      initialChildSize: 0.45, // Start at 45% height
      minChildSize: 0.2, // Allow shrinking
      maxChildSize: 0.85, // Allow expanding
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: theme.cardColor, // Use theme's card color
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                // Subtle shadow
                color: Colors.black.withOpacity(0.15),
                blurRadius: 10,
                spreadRadius: 2,
              )
            ],
          ),
          child: Column(
            children: [
              // Drag Handle indicator
              Container(
                width: 40,
                height: 5,
                margin: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                    color: theme.dividerColor,
                    borderRadius: BorderRadius.circular(10)),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text("available_destinations".tr,
                    style: theme.textTheme.titleLarge),
              ),
              // The list itself
              Expanded(
                child: Obx(() {
                  // Make the list reactive
                  if (controller.availableDirections.isEmpty &&
                      !controller.isLoading.value) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Text(
                          controller.errorMessage.value ??
                              "No destinations found.",
                          textAlign: TextAlign.center,
                          style: TextStyle(color: theme.hintColor),
                        ),
                      ),
                    );
                  }
                  // Show shimmer or loading indicator while fetching? (Optional)
                  // if (controller.isLoading.value) return Center(child: CircularProgressIndicator());

                  return ListView.builder(
                    controller: scrollController, // Link sheet's scroll controller
                    itemCount: controller.availableDirections.length + 1, // +1 for custom direction option
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    itemBuilder: (context, index) {
                      // Custom direction option at the top
                      if (index == 0) {
                        return Card(
                          elevation: 3,
                          color: theme.colorScheme.primaryContainer,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          clipBehavior: Clip.antiAlias,
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            leading: Icon(
                              Icons.add_location_alt_rounded,
                              color: theme.colorScheme.primary,
                              size: 30,
                            ),
                            title: Text(
                              'custom_destination'.tr,
                              style: theme.textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Text(
                              'custom_destination_desc'.tr,
                              style: theme.textTheme.bodyMedium
                                  ?.copyWith(color: theme.colorScheme.onPrimaryContainer.withOpacity(0.7)),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: Icon(Icons.map_outlined,
                                size: 24, color: theme.colorScheme.primary),
                            onTap: () => controller.createCustomDirection(),
                          ),
                        );
                      }
                      
                      // Regular direction items
                      final direction = controller.availableDirections[index - 1];
                      return Padding(
                        padding: const EdgeInsets.only(top: 10),
                        child: Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          clipBehavior: Clip.antiAlias, // Ensures splash effect respects border radius
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            title: Text(
                              direction.title,
                              style: theme.textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Text(
                              '${direction.description}${'fixed_dropoff'.tr}',
                              style: theme.textTheme.bodyMedium
                                  ?.copyWith(color: theme.hintColor),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: Icon(Icons.arrow_forward_ios,
                                size: 18, color: theme.colorScheme.primary),
                            onTap: () => controller.selectDirection(direction), // Call controller action
                          ),
                        ),
                      );
                    },
                  );
                }),
              ),
            ],
          ),
        );
      },
    );
  }

  // --- UI Section: Route Preview Card ---
  Widget _buildRoutePreviewCard(BuildContext context, ThemeData theme) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: 200, // Fixed height for predictable layout
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)
          ],
        ),
        child: Obx(() => SingleChildScrollView(
          child: Column(
            // Use Obx for reactive route details
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Route Info Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _infoItem(
                      Icons.access_time_filled,
                      controller.estimatedDuration.value ?? '--',
                      'duration'.tr,
                      theme),
                  _infoItem(
                      Icons.route_outlined,
                      controller.estimatedDistance.value ?? '--',
                      'distance'.tr,
                      theme),
                  _infoItem(
                      Icons.price_change_outlined,
                      controller.estimatedPrice.value != null
                          ? 'ر.س${controller.estimatedPrice.value!.toStringAsFixed(2)}'
                          : '--',
                      'est_price'.tr,
                      theme),
                ],
              ),
              const SizedBox(height: 15),
              // Confirm Button
              ElevatedButton.icon(
                icon: const Icon(Icons.check_circle_outline),
                label:  Text('confirm_route_button'.tr),
                onPressed: controller.confirmRouteAndProceed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  textStyle: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
              ),
              // Change Pickup Button
              TextButton(
                onPressed: controller.backToPickupSelection,
                child: Text('change_pickup'.tr,
                    style: TextStyle(color: theme.hintColor)),
              ),
            ],
          ),
        )),
      ),
    );
  }

  // Helper for info items in the preview card
  Widget _infoItem(IconData icon, String value, String label, ThemeData theme) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: theme.colorScheme.secondary, size: 28),
        const SizedBox(height: 5),
        Text(value,
            style: theme.textTheme.titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        Text(label,
            style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
      ],
    );
  }

  // --- UI Section: Trip Details Form ---
  Widget _buildTripDetailsForm(BuildContext context, ThemeData theme) {
    // Get reactive price value
    final price = controller.estimatedPrice.value;

    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        // Make padding dynamic to avoid keyboard overlap
        padding: EdgeInsets.only(
            left: 20,
            right: 20,
            top: 20,
            bottom: MediaQuery.of(context).viewInsets.bottom +
                20 // Adjust for keyboard
        ),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)
          ],
        ),
        child: SingleChildScrollView(
          // Allows content to scroll when keyboard appears
          child: Form(
            key: controller.tripDetailsFormKey, // Link form key
            child: Column(
              mainAxisSize: MainAxisSize.min, // Take minimum required height
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Title and Price
                Text(
                  'Trip to: ${controller.selectedDirection.value?.title ?? 'Destination'}',
                  style: theme.textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                if (price != null) // Show price if available
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5.0),
                    child: Text(
                      '${'estimated_price'.tr}: \$${price.toStringAsFixed(2)}',
                      style: theme.textTheme.titleMedium
                          ?.copyWith(color: theme.colorScheme.primary),
                      textAlign: TextAlign.center,
                    ),
                  ),
                const Divider(height: 25),

                // Passengers & Bags Input Row
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controller.passengersController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'passengers'.tr,
                          prefixIcon: const Icon(Icons.person_outline),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10)),
                        ),
                        validator: (value) {
                          // Basic validation
                          if (value == null || value.isEmpty) return 'required'.tr;
                          final num = int.tryParse(value);
                          if (num == null || num < 1) return 'Min 1';
                          if (num > 10) return 'Max 10'; // Example limit
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: TextFormField(
                        controller: controller.bagsController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'bags'.tr,
                          prefixIcon: const Icon(Icons.luggage_outlined),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty)
                            return null; // Optional
                          final num = int.tryParse(value);
                          if (num == null || num < 0) return 'Invalid';
                          if (num > 10) return 'Max 10'; // Example limit
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),

                // Notes Text Field
                TextFormField(
                  controller: controller.notesController,
                  maxLines: 2,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    labelText: 'notes_for_driver'.tr,
                    hintText: 'notes_hint'.tr,
                    prefixIcon: const Icon(Icons.notes_outlined),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10)),
                  ),
                ),
                const SizedBox(height: 15),

                // Scheduling Options
                Row(
                  children: [
                    Obx(() => Checkbox(
                      value: controller.isScheduledTrip.value,
                      onChanged: (value) {
                        controller.isScheduledTrip.value = value ?? false;
                        if (!controller.isScheduledTrip.value) {
                          controller.scheduledDateTime.value = null;
                        }
                      },
                    )),
                     Text('schedule_trip'.tr),
                    const Spacer(),
                    Obx(() => controller.isScheduledTrip.value
                        ? ElevatedButton.icon(
                      icon: const Icon(Icons.calendar_today),
                      label: Text(controller.scheduledDateTime.value !=
                          null
                          ? '${controller.scheduledDateTime.value!.day}/${controller.scheduledDateTime.value!.month} ${controller.scheduledDateTime.value!.hour}:${controller.scheduledDateTime.value!.minute.toString().padLeft(2, '0')}'
                          : 'select_date_time'.tr),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                          DateTime.now().add(const Duration(days: 1)),
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now()
                              .add(const Duration(days: 30)),
                        );
                        if (date != null) {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: TimeOfDay.now(),
                          );
                          if (time != null) {
                            controller.scheduledDateTime.value = DateTime(
                              date.year,
                              date.month,
                              date.day,
                              time.hour,
                              time.minute,
                            );
                          }
                        }
                      },
                    )
                        : const SizedBox.shrink()),
                  ],
                ),
                const SizedBox(height: 15),

                // Price Breakdown Widget
                Obx(() => controller.estimatedPrice.value != null
                    ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Divider(),
                    const SizedBox(height: 5),


                    // Payment Summary Widget (includes all price details)
                    const PaymentSummaryWidget(),

                    const SizedBox(height: 15),


                    // Coupon Widget
                    const CouponWidget(),

                    const SizedBox(height: 15),

                    // Payment Method Widget
                    const PaymentMethodWidget(),

                    const Divider(),
                  ],
                )
                    : const SizedBox.shrink()),

                const SizedBox(height: 15),

                // Action Buttons Row
                Row(
                  children: [
                    // Back Button
                    Expanded(
                      child: OutlinedButton.icon(
                        icon: const Icon(Icons.arrow_back_ios_new, size: 16),
                        label:  Text('back'.tr),
                        onPressed: controller
                            .backToRoutePreview, // Go back to map/route preview
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.secondary,
                          side: BorderSide(
                              color:
                              theme.colorScheme.secondary.withOpacity(0.5)),
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    // Request Ride Button
                    Expanded(
                      flex: 2, // Make request button wider
                      child: Obx(() => ElevatedButton.icon(
                        icon: Icon(controller.isScheduledTrip.value
                            ? Icons.schedule_send_outlined
                            : Icons.send_outlined),
                        label: Text(controller.isScheduledTrip.value
                            ? 'schedule_trip'.tr
                            : 'request_ride_now'.tr),
                        onPressed:
                        controller.requestRide, // Trigger ride request
                        style: ElevatedButton.styleFrom(
                          backgroundColor: controller.isScheduledTrip.value
                              ? Colors.amber.shade700
                              : theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          textStyle: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                        ),
                      )),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // --- UI Section: Trip Status (Searching/Tracking) ---
  Widget _buildTripStatusUI(BuildContext context, ThemeData theme) {
    // This UI shows information based on the currentTrip state
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10)
          ],
        ),
        child: Obx(() {
          // Rebuild based on trip status changes
          final trip = controller.currentTrip.value;
          // Handle case where trip becomes null unexpectedly (shouldn't happen if logic is right)
          if (trip == null) {
            return const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("Waiting for trip data..."),
                SizedBox(height: 15),
                LinearProgressIndicator(),
              ],
            );
          }

          // Determine Icon, Text, and Actions based on TripStatus
          String statusText = '';
          IconData statusIcon = Icons.hourglass_empty_rounded;
          bool showProgress = false;
          bool showCancel = false;
          bool showDoneButton = false;
          Widget? driverInfoWidget; // Placeholder for driver details

          switch (trip.status) {
            case TripStatus.searching:
              statusText = 'finding_driver'.tr;
              statusIcon = Icons.search_rounded;
              showProgress = true;
              showCancel = true;
              break;
            case TripStatus.assigned:
            case TripStatus.enRouteToPickup:
              statusText = 'Driver is on the way!'.tr;
              statusIcon = Icons.directions_car_filled_outlined;
              showCancel = true; // Allow cancellation before pickup?
              driverInfoWidget = _buildDriverInfo(trip, theme);
              break;
            case TripStatus.arrivedAtPickup:
              statusText = 'Your driver has arrived!'.tr;
              statusIcon = Icons.location_on_rounded;
              showCancel = false; // Typically cannot cancel now
              driverInfoWidget = _buildDriverInfo(trip, theme);
              break;
            case TripStatus.ongoing:
              statusText = 'Trip to ${trip.directionTitle} in progress';
              statusIcon = Icons.navigation_rounded;
              showCancel = false;
              driverInfoWidget =
                  _buildDriverInfo(trip, theme); // Show ETA to destination
              break;
            case TripStatus.completed:
              statusText = 'Trip Completed!';
              statusIcon = Icons.check_circle_outline_rounded;
              showDoneButton = true; // Button to go back to directions list
              // Show trip summary for completed trips
              driverInfoWidget = _buildTripSummary(trip, theme);
              break;
            case TripStatus.cancelledByUser:
            case TripStatus.cancelledByDriver:
              statusText = 'Trip Cancelled';
              statusIcon = Icons.cancel_outlined;
              showDoneButton = true;
              break;
            case TripStatus
                .noDriversAvailable: // Should be handled by reset, but as fallback
              statusText = 'No drivers were available.';
              statusIcon = Icons.sentiment_dissatisfied_outlined;
              showDoneButton = true;
              break;
            case TripStatus.scheduled:
              // TODO: Handle this case.
          }

          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Status Icon and Text Row
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(statusIcon, size: 32, color: theme.colorScheme.primary),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      statusText,
                      style: theme.textTheme.titleMedium
                          ?.copyWith(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),

              // Linear Progress Bar (only when searching)
              if (showProgress)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 15.0),
                  child: LinearProgressIndicator(),
                ),

              // Driver Info Section (Placeholder)
              if (driverInfoWidget != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15.0),
                  child: driverInfoWidget,
                ),

              // Action Buttons
              const SizedBox(height: 20),
              if (showCancel)
                OutlinedButton.icon(
                  icon:
                  const Icon(Icons.cancel_schedule_send_outlined, size: 18),
                  label: const Text('Cancel Request'),
                  onPressed: () => controller.cancelTripRequest(
                      showMessage: true), // Pass true to show snackbar
                  style: OutlinedButton.styleFrom(
                    foregroundColor: theme.colorScheme.error,
                    side: BorderSide(
                        color: theme.colorScheme.error.withOpacity(0.6)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              if (trip.status != TripStatus.searching &&
                  trip.status != TripStatus.completed &&
                  trip.status != TripStatus.cancelledByUser &&
                  trip.status != TripStatus.cancelledByDriver &&
                  trip.status != TripStatus.noDriversAvailable)
                Padding(
                  padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Open chat view
                      final chatController = Get.find<ChatController>();
                      chatController.initializeChat(trip);
                      Get.to(() => const ChatView());
                    },
                    icon: Stack(
                      children: [
                        const Icon(Icons.chat),
                        if (trip.unreadMessageCount > 0)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 14,
                                minHeight: 14,
                              ),
                              child: Text(
                                '${trip.unreadMessageCount}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                    label: const Text('Chat with Driver'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              if (showDoneButton)
                ElevatedButton.icon(
                  icon: const Icon(Icons.done_all_rounded),
                  label: const Text('Done'),
                  onPressed: controller
                      .backToDirections, // Go back to list after completion/cancellation
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.secondary,
                    foregroundColor: theme.colorScheme.onSecondary,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                ),
            ],
          );
        }),
      ),
    );
  }

  // --- Builds Top Section (Search Bar or Instruction) --- NEW ---
  Widget _buildTopSection(BuildContext context, ThemeData theme) {
    // Show Search Bar and "Use Current Location" button only during pickup selection
    if (controller.homeState.value == HomeState.selectingPickup) {
      return Positioned(
          top: 10,
          left: 10,
          right: 10,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSearchBar(context, theme),
              const SizedBox(height: 8),
              // Location buttons row
              Row(
                children: [
                  // Use current location button
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.my_location_rounded, size: 18),
                      label: Text('use_current_location'.tr),
                      onPressed: controller.useCurrentLocationForPickup,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.secondaryContainer,
                        foregroundColor: theme.colorScheme.onSecondaryContainer,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        textStyle: theme.textTheme.labelLarge,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Map search button
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.map_outlined, size: 18),
                      label: Text('search_on_map'.tr),
                      onPressed: controller.openMapSearch,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primaryContainer,
                        foregroundColor: theme.colorScheme.onPrimaryContainer,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        textStyle: theme.textTheme.labelLarge,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ));
    }
    // Hide in other states
    return const SizedBox.shrink();
  }

  // --- Search Bar Widget --- NEW ---
  Widget _buildSearchBar(BuildContext context, ThemeData theme) {
    return Visibility(
      visible: false,
      child: Material(
        elevation: 4.0,
        borderRadius: BorderRadius.circular(30.0), // Rounded corners
        child: TextField(
            controller: controller.searchController,
            decoration: InputDecoration(
              hintText: 'search_pickup'.tr,
              prefixIcon: Icon(Icons.search, color: theme.hintColor),
              suffixIcon: controller.searchController.text.isNotEmpty
                  ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  controller
                      .clearSearchResults(); // Clear search via controller method
                },
              )
                  : null,
              filled: true,
              fillColor: theme.cardColor, // Use card color for background
              contentPadding: const EdgeInsets.symmetric(vertical: 15.0),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30.0),
                borderSide: BorderSide.none, // No border needed due to elevation
              ),
              focusedBorder: OutlineInputBorder(
                // Subtle border on focus
                borderRadius: BorderRadius.circular(30.0),
                borderSide:
                BorderSide(color: theme.colorScheme.primary, width: 1.0),
              ),
            ),
            onChanged: (_) {}, // Handled by listener + debounce in controller
            onTap: () {
              // Ensure session token is ready when user taps
              if (controller.placesSessionToken == null) {
                controller.placesSessionToken = controller.uuid.v4();
              }
            }
          // onSubmitted: (value) => controller._onSearchChanged(), // Optional: Trigger search on submit too
        ),
      ),
    );
  }

  // --- Search Results List Overlay --- NEW ---
  Widget _buildSearchResultsList(BuildContext context, ThemeData theme) {
    // Show only when searching and predictions are available
    if (!(controller.isSearching.value ||
        controller.placePredictions.isNotEmpty)) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: 75, // Position below search bar (adjust based on layout)
      left: 15,
      right: 15,
      child: Material(
        elevation: 6.0,
        borderRadius: BorderRadius.circular(10.0),
        child: Container(
          constraints: BoxConstraints(
            maxHeight:
            MediaQuery.of(context).size.height * 0.35, // Limit height
          ),
          decoration: BoxDecoration(
            color: theme.canvasColor, // Use canvas or card color
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Obx(() {
            // Make list reactive
            if (controller.isSearching.value &&
                controller.placePredictions.isEmpty) {
              return const Center(
                  heightFactor: 3,
                  child: CircularProgressIndicator()); // Loading indicator
            }
            if (!controller.isSearching.value &&
                controller.placePredictions.isEmpty &&
                controller.searchController.text.length >= 3) {
              return const Padding(
                  padding: EdgeInsets.all(15.0),
                  child: Text("No results found."));
            }

            return ListView.separated(
              shrinkWrap: true, // Important for constrained height
              padding: EdgeInsets.zero,
              itemCount: controller.placePredictions.length,
              separatorBuilder: (_, __) =>
              const Divider(height: 1, thickness: 1),
              itemBuilder: (context, index) {
                final prediction = controller.placePredictions[index];
                return ListTile(
                  leading: Icon(Icons.location_pin,
                      color: theme.colorScheme.secondary),
                  title: Text(prediction.description,
                      maxLines: 2, overflow: TextOverflow.ellipsis),
                  onTap: () => controller.onPredictionSelected(prediction),
                );
              },
            );
          }),
        ),
      ),
    );
  }

  // --- Utility Widgets ---

  // Shows a transient snackbar for errors
  Widget _showErrorSnackbar(String message, ThemeData theme) {
    // Use WidgetsBinding to show snackbar after build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (Get.isSnackbarOpen) return; // Prevent duplicate snackbars
      Get.snackbar(
        'Error',
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: theme.colorScheme.errorContainer,
        colorText: theme.colorScheme.onErrorContainer,
        margin: const EdgeInsets.all(12),
        borderRadius: 10,
        duration:
        const Duration(seconds: 4), // Slightly longer duration for errors
        isDismissible: true,
        icon: Icon(Icons.error_outline_rounded, color: theme.colorScheme.error),
      );
      // Clear the error in controller AFTER showing snackbar to prevent re-triggering
      Future.delayed(const Duration(milliseconds: 100),
              () => controller.errorMessage.value = null);
    });
    return const SizedBox
        .shrink(); // Return empty widget, side effect handles the snackbar
  }

  // Builds a widget to display driver information and ETA
  Widget _buildDriverInfo(TripRequest trip, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Driver name and rating
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.person, color: theme.colorScheme.primary),
                  const SizedBox(width: 8),
                  Text(
                    trip.driverName != null && trip.driverName!.isNotEmpty
                        ? trip.driverName!
                        : 'Your Driver',
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              // Rating stars
              Row(
                children: [
                  ...List.generate(
                    5,
                        (index) => Icon(
                      index < (trip.driverRating ?? 0).floor()
                          ? Icons.star
                          : (trip.driverRating != null &&
                          index < trip.driverRating! &&
                          index >= (trip.driverRating ?? 0).floor())
                          ? Icons.star_half
                          : Icons.star_border,
                      size: 18,
                      color: Colors.amber,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    trip.driverRating != null
                        ? trip.driverRating!.toStringAsFixed(1)
                        : '-',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // ETA information
          Row(
            children: [
              Icon(Icons.access_time, color: theme.colorScheme.secondary),
              const SizedBox(width: 8),
              Text(
                'Estimated arrival: ${controller.estimatedArrivalTime.value ?? 'Calculating...'}',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Vehicle information (placeholder)
          Row(
            children: [
              Icon(Icons.directions_car, color: theme.colorScheme.secondary),
              const SizedBox(width: 8),
              Text(
                'Vehicle: Toyota Camry (White)',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Builds a widget to display when an unrecoverable error occurs during initialization
  Widget _buildErrorWidget(String message, VoidCallback onRetry) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.cloud_off_rounded, size: 60, color: Colors.red.shade300),
            const SizedBox(height: 15),
            Text("Initialization Failed",
                style: Get.textTheme.headlineSmall
                    ?.copyWith(color: Colors.red.shade700)),
            const SizedBox(height: 10),
            Text(message,
                textAlign: TextAlign.center, style: Get.textTheme.bodyMedium),
            const SizedBox(height: 25),
            ElevatedButton.icon(
                icon: const Icon(Icons.logout),
                label: const Text('Logout and Exit'),
                onPressed: onRetry, // Pass the logout function
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade100,
                    foregroundColor: Colors.red.shade900)),
          ],
        ),
      ),
    );
  }

  // Helper to build trip summary widget for completed trips
  Widget _buildTripSummary(TripRequest trip, ThemeData theme) {
    return TripSummaryWidget(trip: trip);
  }

  Widget _buildTripStatusCard() {
    return Obx(() {
      if (controller.currentTrip.value == null) return const SizedBox.shrink();
      
      final trip = controller.currentTrip.value!;
      final status = trip.status;
      
      return Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Trip Status: ${status.name.toUpperCase()}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (status == TripStatus.ongoing)
                    TextButton.icon(
                      onPressed: () => controller.showCancellationDialog(trip.id),
                      icon: const Icon(Icons.cancel, color: Colors.red),
                      label: const Text(
                        'Cancel Trip',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              // Add more trip details here
            ],
          ),
        ),
      );
    });
  }
} // End of HomeView Class