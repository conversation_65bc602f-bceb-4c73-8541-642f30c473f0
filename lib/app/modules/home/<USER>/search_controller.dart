import 'dart:async';
import 'dart:convert';

import 'package:ai_delivery_app/app/modules/home/<USER>/directions_controller.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/location_controller.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';

import '../../../config/constants.dart';
import '../../../data/models/place_autocomplete_model.dart';

class SearchPlacesController extends GetxController {
  // --- Dependencies ---
  final Uuid uuid = const Uuid();
  final LocationController locationController = Get.find<LocationController>();
  final DirectionsController directionsController = Get.find<DirectionsController>();
  
  // --- Observables ---
  final RxList<PlaceAutocompletePrediction> placePredictions = <PlaceAutocompletePrediction>[].obs;
  final RxBool isSearching = false.obs;
  final Rx<String?> searchErrorMessage = Rx<String?>(null);
  
  // --- Controllers ---
  late TextEditingController searchController;
  
  // --- Private Variables ---
  String? placesSessionToken;
  final String _googleApiKey = Constants.GOOGLE_MAPS_API_KEY;
  
  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
  }
  
  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
  
  // Handle search input with debounce
  void onSearchInputChanged(String value) {
    // Debounce to avoid too many API calls
    EasyDebounce.debounce(
      'search-debounce',
      const Duration(milliseconds: 500),
      () {
        // Function to execute after debounce
        final query = searchController.text.trim();
        debugPrint("Debounced Search Query: $query");
        
        if (query.length < 3) {
          // Don't search for very short strings
          clearSearchResults();
          return;
        }
        
        // Generate a new session token if needed
        if (placesSessionToken == null) {
          // Start a new session
          placesSessionToken = uuid.v4();
          debugPrint("Generated new Places session token: $placesSessionToken");
        }
        
        // Fetch predictions
        _fetchPlacePredictions(query);
      },
    );
  }
  
  // Fetches place predictions from Google Places Autocomplete API
  Future<void> _fetchPlacePredictions(String input) async {
    if (placesSessionToken == null) {
      debugPrint("Error: Places session token is null.");
      return; // Should not happen if logic is correct
    }
    
    // Show loading/searching indicator for search results
    isSearching.value = true;
    searchErrorMessage.value = null;
    
    // Bias towards user's current location if available
    String locationBias = '';
    if (locationController.userLocation.value != null) {
      final lat = locationController.userLocation.value!.latitude;
      final lng = locationController.userLocation.value!.longitude;
      locationBias = '&location=$lat,$lng&radius=50000'; // 50km radius
    }
    
    // Optionally restrict to country/region
    const components = '&components=country:sa'; // Saudi Arabia
    
    // Build URL for Places Autocomplete API
    final url = 'https://maps.googleapis.com/maps/api/place/autocomplete/json'
        '?input=${Uri.encodeComponent(input)}'
        '&key=$_googleApiKey'
        '&sessiontoken=$placesSessionToken'
        '&language=ar' // Arabic language
        '$locationBias'
        '$components'; // Add components if needed
    
    debugPrint("Places Autocomplete URL: $url");
    
    try {
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);
        
        if (decodedJson['status'] == 'OK') {
          final predictionsJson = decodedJson['predictions'] as List;
          placePredictions.value = predictionsJson
              .map((p) => PlaceAutocompletePrediction.fromJson(p))
              .toList();
          debugPrint("Fetched ${placePredictions.length} predictions.");
        } else {
          debugPrint(
              "Places Autocomplete API Error: Status: ${decodedJson['status']}, Msg: ${decodedJson['error_message']}");
          // Optionally show error to user: errorMessage.value = "Search error: ${decodedJson['status']}";
          placePredictions.clear(); // Clear list on error
        }
      } else {
        debugPrint("HTTP Error fetching predictions: ${response.statusCode}");
        // Optionally show error: errorMessage.value = "Network error during search.";
        placePredictions.clear();
      }
    } catch (e) {
      debugPrint("Error fetching place predictions: $e");
      // Optionally show error: errorMessage.value = "Could not perform search.";
      placePredictions.clear();
    } finally {
      isSearching.value = false;
    }
  }
  
  // Clear search results
  void clearSearchResults() {
    placePredictions.clear();
    isSearching.value = false;
    searchErrorMessage.value = null;
  }
  
  // Called when user selects a prediction from the search results list
  Future<void> onPredictionSelected(
      PlaceAutocompletePrediction prediction) async {
    debugPrint(
        "Prediction Selected: ${prediction.description} (ID: ${prediction.placeId})");
        
    if (placesSessionToken == null) {
      debugPrint("Error: Session token missing when selecting prediction.");
      clearSearchResults(); // Clear search UI anyway
      return;
    }
    
    // Clear search UI immediately for better UX
    searchController.clear();
    clearSearchResults();
    
    // Get detailed place information including coordinates
    await _getPlaceDetails(prediction.placeId, placesSessionToken!);
    
    // Generate a new session token for next search
    // (Google recommends using the same token for autocomplete and place details,
    // then generating a new one for the next search session)
    placesSessionToken = null;
  }
  
  // Get place details from Google Places Details API
  Future<void> _getPlaceDetails(String placeId, String sessionToken) async {
    isSearching.value = true;
    searchErrorMessage.value = null;
    
    final url = 'https://maps.googleapis.com/maps/api/place/details/json'
        '?place_id=$placeId'
        '&fields=geometry'
        '&key=$_googleApiKey'
        '&sessiontoken=$sessionToken'; // Use the SAME session token
    
    debugPrint("Places Details URL: $url");
    
    try {
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);
        
        if (decodedJson['status'] == 'OK') {
          final result = decodedJson['result'];
          final locationData = result['geometry']['location'];
          final lat = locationData['lat'];
          final lng = locationData['lng'];
          
          if (lat != null && lng != null) {
            final selectedLatLng = LatLng(lat, lng);
            debugPrint("Place Details Location: $selectedLatLng");
            
            // Use the shared function to set marker and calculate route
            directionsController.setPickupLocationAndCalculateRoute(selectedLatLng);
          } else {
            searchErrorMessage.value =
                'Invalid location data received from Place Details.';
          }
        } else {
          debugPrint(
              "Places Details API Error: Status: ${decodedJson['status']}, Msg: ${decodedJson['error_message']}");
          throw Exception('Place Details API Error: ${decodedJson['status']}');
        }
      } else {
        debugPrint("HTTP Error fetching place details: ${response.statusCode}");
        throw Exception('Network error getting place details.');
      }
    } catch (e) {
      debugPrint("Error getting place details: $e");
      searchErrorMessage.value =
          "Could not get location details for the selected place.";
      // Revert state? Maybe back to selecting pickup without a marker.
    } finally {
      isSearching.value = false;
    }
  }
}
