import 'dart:async';

import 'package:ai_delivery_app/app/modules/home/<USER>/directions_controller.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../data/models/trip_request_model.dart';

class TripRequestController extends GetxController {
  // --- Dependencies ---
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DirectionsController directionsController = Get.find<DirectionsController>();
  
  // --- Observables ---
  final RxBool isRequestingTrip = false.obs;
  final Rx<String?> requestErrorMessage = Rx<String?>(null);
  final RxList<Map<String, dynamic>> nearbyDrivers = <Map<String, dynamic>>[].obs;
  
  // --- Controllers ---
  late TextEditingController passengersController;
  late TextEditingController bagsController;
  late TextEditingController notesController;
  late TextEditingController scheduledDateController;
  late TextEditingController scheduledTimeController;
  
  // --- Private Variables ---
  User? get currentUser => _auth.currentUser;
  StreamSubscription? _driverSubscription;
  Timer? _driverSearchTimeout;
  
  @override
  void onInit() {
    super.onInit();
    initControllers();
  }
  
  @override
  void onClose() {
    disposeControllers();
    _stopListeningToDrivers();
    _driverSearchTimeout?.cancel();
    super.onClose();
  }
  
  // Initialize text controllers
  void initControllers() {
    passengersController = TextEditingController(text: '1');
    bagsController = TextEditingController(text: '0');
    notesController = TextEditingController();
    scheduledDateController = TextEditingController();
    scheduledTimeController = TextEditingController();
  }
  
  // Dispose text controllers
  void disposeControllers() {
    passengersController.dispose();
    bagsController.dispose();
    notesController.dispose();
    scheduledDateController.dispose();
    scheduledTimeController.dispose();
  }
  
  // Reset form fields
  void resetForm() {
    passengersController.text = '1';
    bagsController.text = '0';
    notesController.clear();
    scheduledDateController.clear();
    scheduledTimeController.clear();
  }
  
  // Request a trip
  Future<String?> requestTrip({
    bool isScheduled = false,
    DateTime? scheduledDateTime,
    String paymentMethod = 'cash',
  }) async {
    if (currentUser == null) {
      requestErrorMessage.value = 'You must be logged in to request a trip.';
      return null;
    }
    
    if (directionsController.selectedDirection.value == null ||
        directionsController.selectedPickupLocation.value == null) {
      requestErrorMessage.value = 'Please select a pickup location and destination.';
      return null;
    }
    
    isRequestingTrip.value = true;
    requestErrorMessage.value = null;
    
    try {
      // Get user data
      final userDoc = await _firestore.collection('users').doc(currentUser!.uid).get();
      final userData = userDoc.data();
      
      if (userData == null) {
        throw Exception('User data not found');
      }
      
      // Create trip request
      final tripRequest = {
        'userId': currentUser!.uid,
        'userName': userData['name'] ?? 'User',
        'userPhone': userData['phone'] ?? '',
        'status': isScheduled
            ? TripRequest.statusToString(TripStatus.scheduled)
            : TripRequest.statusToString(TripStatus.searching),
        'pickupLocation': GeoPoint(
          directionsController.selectedPickupLocation.value!.latitude,
          directionsController.selectedPickupLocation.value!.longitude,
        ),
        'destinationLocation': GeoPoint(
          directionsController.selectedDirection.value!.endLocation.latitude,
          directionsController.selectedDirection.value!.endLocation.longitude,
        ),
        'destinationName': directionsController.selectedDirection.value!.title,
        'distance': directionsController.estimatedDistance.value,
        'duration': directionsController.estimatedDuration.value,
        'basePrice': directionsController.basePrice.value,
        'appFee': directionsController.appFee.value,
        'taxAmount': directionsController.taxAmount.value,
        'discountAmount': directionsController.discountAmount.value,
        'totalPrice': directionsController.estimatedPrice.value,
        'passengers': int.tryParse(passengersController.text) ?? 1,
        'bags': int.tryParse(bagsController.text) ?? 0,
        'notes': notesController.text,
        'paymentMethod': paymentMethod,
        'paymentStatus': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'scheduledFor': isScheduled ? Timestamp.fromDate(scheduledDateTime!) : null,
        'routePolyline': directionsController.routePoints
            .map((point) => {'lat': point.latitude, 'lng': point.longitude})
            .toList(),
      };
      
      // Add to Firestore
      final docRef = await _firestore.collection('tripRequests').add(tripRequest);
      
      // Start searching for drivers if not scheduled
      if (!isScheduled) {
        _findNearbyDrivers();
        
        // Set a timeout for driver search (e.g., 1 minute)
        _driverSearchTimeout = Timer(const Duration(minutes: 1), () {
          // Example: 1 minute timeout
          if (docRef.id.isNotEmpty) {
            debugPrint("Driver search timed out locally.");
            // Update trip status to 'noDriversAvailable' in Firestore
            // The listener _listenToTripStatus will then handle the UI change.
            _firestore
                .collection('tripRequests')
                .doc(docRef.id)
                .update({
              'status': TripRequest.statusToString(TripStatus.noDriversAvailable)
            }).catchError(
                    (e) => debugPrint("Error updating trip status to timeout: $e"));
          }
        });
      }
      
      return docRef.id;
    } catch (e) {
      debugPrint("Error requesting ride: $e");
      requestErrorMessage.value = "Could not request ride. Please try again.";
      return null;
    } finally {
      isRequestingTrip.value = false;
    }
  }
  
  // Find nearby drivers
  void _findNearbyDrivers() {
    // **** IMPORTANT: This is a basic placeholder. ****
    // **** Replace with actual Geofire/Geoquery implementation for scalability. ****
    debugPrint("Placeholder: Simulating finding nearby drivers...");
    _stopListeningToDrivers(); // Stop previous listener
    
    // Example: Listen to *all* available drivers (NOT efficient for production)
    _driverSubscription = _firestore
        .collection('drivers') // Assumes this collection exists
        // .where('isOnline', isEqualTo: true) // Example filter for available drivers
        // .where('location', isNear: ...) // *** NEED GEOQUERY HERE ***
        .limit(10) // Limit results for demo performance
        .snapshots()
        .listen((snapshot) {
      // Only process if we are still in the searching state
      nearbyDrivers.clear(); // Clear previous list
      
      for (var doc in snapshot.docs) {
        final data = doc.data();
        final driverId = doc.id;
        
        // Check if driver is available
        final isAvailable = data['isAvailable'] ?? false;
        final isOnline = data['isOnline'] ?? false;
        
        if (isAvailable && isOnline) {
          // Check if driver has location data
          if (data['location'] != null) {
            final location = data['location'] as GeoPoint;
            
            nearbyDrivers.add({
              'id': driverId,
              'name': data['name'] ?? 'Driver',
              'location': LatLng(location.latitude, location.longitude),
              'rating': data['rating'] ?? 0.0,
            });
          }
        }
      }
      
      if (nearbyDrivers.isEmpty) {
        debugPrint("No available drivers found in current query.");
      }
    }, onError: (error) {
      debugPrint("Error listening to drivers: $error");
      // Don't necessarily stop searching on temporary error? Maybe retry?
    });
  }
  
  // Stop listening to drivers
  void _stopListeningToDrivers() {
    if (_driverSubscription != null) {
      _driverSubscription?.cancel();
      _driverSubscription = null;
      nearbyDrivers.clear(); // Clear local list
      debugPrint("Stopped listening to drivers.");
    }
  }
}
