import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/models/trip_request_model.dart';
import '../../../services/payment_service.dart';

class PaymentController extends GetxController {
  // --- Dependencies ---
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PaymentService _paymentService = Get.find<PaymentService>();
  
  // --- Observables ---
  final RxDouble walletBalance = 0.0.obs;
  final RxBool isProcessingPayment = false.obs;
  final Rx<String?> paymentErrorMessage = Rx<String?>(null);
  
  // --- Private Variables ---
  User? get currentUser => _auth.currentUser;
  
  @override
  void onInit() {
    super.onInit();
    loadWalletBalance();
  }
  
  // Load wallet balance
  Future<void> loadWalletBalance() async {
    if (currentUser == null) return;
    
    try {
      final balance = await _paymentService.getWalletBalance(currentUser!.uid);
      walletBalance.value = balance;
    } catch (e) {
      debugPrint('Error loading wallet balance: $e');
    }
  }
  
  // Process wallet payment for a trip
  Future<bool> processWalletPayment(TripRequest trip) async {
    if (currentUser == null) return false;
    
    isProcessingPayment.value = true;
    paymentErrorMessage.value = null;
    
    try {
      // Generate a unique transaction ID based on trip ID to ensure idempotency
      final String transactionId = '${trip.id}_payment';
      
      // Check if transaction has already been processed
      final existingTransactions = await _firestore
          .collection('walletTransactions')
          .where('referenceId', isEqualTo: transactionId)
          .get();
      
      if (existingTransactions.docs.isNotEmpty) {
        debugPrint(
            'Transaction with ID $transactionId already exists. Preventing duplicate.');
        return true; // Already processed, consider it successful
      }
      
      // Check if payment has already been processed
      final existingPaymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: trip.id)
          .where('status', isEqualTo: 'completed')
          .get();
      
      if (existingPaymentQuery.docs.isNotEmpty) {
        debugPrint(
            'Payment for trip ${trip.id} already exists in payments collection.');
        return true; // Already processed, consider it successful
      }
      
      // Use a Firebase transaction to ensure atomicity
      await _firestore.runTransaction((transaction) async {
        // 1. Get current user document for wallet balance
        final userDocRef = _firestore.collection('users').doc(currentUser!.uid);
        final userDoc = await transaction.get(userDocRef);
        
        if (!userDoc.exists) {
          throw Exception('User document not found');
        }
        
        // 2. Check wallet balance
        final walletBalance = (userDoc.data()?['walletBalance'] ?? 0.0).toDouble();
        if (walletBalance < trip.totalPrice) {
          throw Exception('Insufficient wallet balance');
        }
        
        // 3. Create payment document
        final paymentRef = _firestore.collection('payments').doc();
        final payment = {
          'tripId': trip.id,
          'userId': currentUser!.uid,
          'driverId': trip.driverId,
          'amount': trip.totalPrice,
          'baseAmount': trip.basePrice,
          'appFee': trip.appFee,
          'taxAmount': trip.taxAmount,
          'discountAmount': trip.discountAmount,
          'method': 'wallet',
          'status': 'completed',
          'createdAt': FieldValue.serverTimestamp(),
          'completedAt': FieldValue.serverTimestamp(),
          'transactionId': transactionId, // Add transaction ID for idempotency
        };
        
        // 4. Calculate new wallet balance
        final newBalance = walletBalance - trip.totalPrice;
        
        // 5. Create wallet transaction
        final walletTransactionRef = _firestore.collection('walletTransactions').doc();
        final walletTransaction = {
          'userId': currentUser!.uid,
          'amount': -trip.totalPrice, // Negative amount for payment
          'type': 'tripPayment',
          'description': 'Payment for trip #${trip.id}',
          'referenceId': transactionId,
          'tripId': trip.id,
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
          'balanceAfter': newBalance,
        };
        
        // 6. Update trip request
        final tripRef = _firestore.collection('tripRequests').doc(trip.id);
        
        // 7. Execute all operations within the transaction
        transaction.set(paymentRef, payment);
        transaction.update(userDocRef, {'walletBalance': newBalance});
        transaction.set(walletTransactionRef, walletTransaction);
        transaction.update(tripRef, {
          'paymentStatus': 'completed',
          'paymentMethod': 'wallet',
          'paymentId': paymentRef.id,
          'paymentCompletedAt': FieldValue.serverTimestamp(),
        });
      });
      
      // Update local wallet balance
      await loadWalletBalance();
      
      // Show success message
      Get.snackbar(
        'تم الدفع',
        'تم خصم ${trip.totalPrice.toStringAsFixed(2)} ر.س من محفظتك',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      return true;
    } catch (e) {
      // Handle specific errors
      if (e.toString().contains('Insufficient wallet balance')) {
        Get.snackbar(
          'رصيد غير كافي',
          'الرجاء شحن محفظتك أو اختيار طريقة دفع أخرى',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      } else {
        debugPrint("Error processing wallet payment: $e");
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء معالجة الدفع',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      return false;
    } finally {
      isProcessingPayment.value = false;
    }
  }
  
  // Check if payment has been processed and show rating dialog
  Future<bool> checkPaymentStatus(String tripId) async {
    try {
      // Check if payment has already been processed
      final paymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: tripId)
          .where('status', isEqualTo: 'completed')
          .get();
      
      return paymentQuery.docs.isNotEmpty;
    } catch (e) {
      debugPrint("Error checking payment status: $e");
      return false;
    }
  }
  
  // Apply coupon to trip
  Future<Map<String, dynamic>> applyCoupon({
    required String couponCode,
    required double basePrice,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return {
          'valid': false,
          'message': 'يجب تسجيل الدخول لاستخدام الكوبون',
        };
      }
      
      // Validate the coupon using the payment service
      final result = await _paymentService.validateCoupon(
        couponCode: couponCode,
        userId: currentUser.uid,
        amount: basePrice,
      );
      
      if (result['valid']) {
        // Calculate discount amount
        final discountAmount = result['discountAmount'] ?? 0.0;
        final couponId = result['couponId'] ?? '';
        
        return {
          'valid': true,
          'message': 'تم تطبيق الكوبون بنجاح',
          'discountAmount': discountAmount,
          'couponId': couponId,
        };
      } else {
        return {
          'valid': false,
          'message': result['message'] ?? 'الكوبون غير صالح',
        };
      }
    } catch (e) {
      return {
        'valid': false,
        'message': 'حدث خطأ أثناء تطبيق الكوبون',
      };
    }
  }
  
  // Get available coupons for the current user
  Future<List<Map<String, dynamic>>> getAvailableCoupons({
    required double basePrice,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return [];
      }
      
      // Get available coupons using the payment service
      return await _paymentService.getAvailableCoupons(
        userId: currentUser.uid,
        amount: basePrice,
      );
    } catch (e) {
      return [];
    }
  }
}
