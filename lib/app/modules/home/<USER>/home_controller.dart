import 'dart:async';
import 'dart:convert'; // For json decoding

import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http; // For Directions API call
import 'package:location/location.dart' as loc;
import 'package:uuid/uuid.dart';

import '../../../config/constants.dart';
import '../../../data/models/direction_model.dart';
import '../../../data/models/place_autocomplete_model.dart';
import '../../../data/models/trip_request_model.dart'; // Alias location package
import '../../../data/models/user_model.dart';
import '../../../data/models/wallet_transaction_model.dart';
import '../../../data/models/rating_model.dart';
import '../../../services/payment_service.dart';
import 'trip_controller.dart';
import '../../../data/models/cancellation_reason_model.dart';
import '../../../data/models/faq_model.dart';
import '../../../data/models/support_chat_model.dart';
import '../../../services/payment_service.dart';

// Enum to manage the different stages of the home screen UI
enum HomeState {
  idle, // Initializing, getting location, fetching directions
  showingDirections, // Displaying list of predefined destinations
  selectingPickup, // Direction chosen, waiting for user to tap map for START point
  showingRoutePreview, // Start point tapped, route calculated, preview shown
  enteringDetails, // Route confirmed, entering passenger/bag details
  requestingRide, // Request sent, searching for driver
  trackingRide // Driver assigned or trip ongoing
}

class HomeController extends GetxController {
  // --- Dependencies ---
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PolylinePoints _polylinePoints = PolylinePoints(); // Polyline helper
  final loc.Location _locationService =
  loc.Location(); // Location service instance
  final Uuid uuid = const Uuid(); // UUID generator instance

  // --- User Info ---
  late final User? currentUser; // Current logged-in Firebase user
  final Rxn<UserModel> currentUserModel = Rxn<UserModel>();

  // --- Payment Related Observables ---
  final basePrice = 0.0.obs;
  final appFee = 0.0.obs;
  final taxAmount = 0.0.obs;
  final discountAmount = 0.0.obs;
  final totalPrice = 0.0.obs;
  final currencySymbol = 'SAR'.obs;
  final couponId = ''.obs;
  final couponCode = ''.obs;
  final isCouponApplied = false.obs;
  final isCouponLoading = false.obs;
  final couponMessage = ''.obs;
  final selectedPaymentMethod = 'cash'.obs;
  final walletBalance = 0.0.obs;

  // --- Payment Controllers ---
  final TextEditingController couponController = TextEditingController();

  // --- Services ---
  final PaymentService _paymentService = Get.find<PaymentService>();
  final RxList<WalletTransaction> walletTransactions =
      <WalletTransaction>[].obs;

  // --- UI State Management ---
  final Rx<HomeState> homeState = HomeState.idle.obs; // Reactive UI state
  final isLoading = false.obs; // General loading indicator for async operations
  final errorMessage = RxnString(); // Observable for displaying error messages

  // --- Directions Data ---
  final RxList<Direction> availableDirections =
      <Direction>[].obs; // List of fetched directions
  final Rxn<Direction> selectedDirection =
  Rxn<Direction>(); // The chosen predefined direction

  // --- Map Selection (Pickup Only) & Route Data ---
  final Rxn<LatLng> selectedPickupLocation =
  Rxn<LatLng>(); // User defines this via map tap/drag
  final RxSet<Marker> mapMarkers =
      <Marker>{}.obs; // Markers for user, pickup, destination, drivers
  final RxSet<Polyline> mapPolylines =
      <Polyline>{}.obs; // Route polyline shown on map

  // --- Calculated Route Info ---
  final RxnString estimatedDistance = RxnString(); // e.g., "5.2 km"
  final RxnString estimatedDuration = RxnString(); // e.g., "15 mins"
  final RxnDouble estimatedPrice =
  RxnDouble(); // Calculated price based on distance/duration

  // --- Trip Details Form ---
  final passengersController =
  TextEditingController(text: '1'); // Default passenger count
  final bagsController = TextEditingController(text: '0'); // Default bag count
  final notesController = TextEditingController(); // Optional notes for driver
  final tripDetailsFormKey = GlobalKey<FormState>(); // Key for form validation

  // --- Scheduling Options ---
  final RxBool isScheduledTrip = false.obs; // Whether this is a scheduled trip
  final Rxn<DateTime> scheduledDateTime =
  Rxn<DateTime>(); // When the trip is scheduled for

  // --- Search Functionality --- NEW ---
  final searchController =
  TextEditingController(); // Controller for search input
  final RxList<PlaceAutocompletePrediction> placePredictions =
      <PlaceAutocompletePrediction>[].obs; // List of search results
  final RxBool isSearching =
      false.obs; // To show/hide search results list or loading
  String? placesSessionToken; // Holds the current session token for Places API

  // --- Trip Request & Tracking ---
  final Rxn<TripRequest> currentTrip =
  Rxn<TripRequest>(); // Holds the active trip data
  StreamSubscription?
  _tripSubscription; // Firestore listener for trip status updates

  // Trip controller for handling cancellation and rating
  late TripController tripController;

  // --- Map & Location ---
  GoogleMapController?
  mapController; // Controller for interacting with the Google Map widget
  loc.LocationData?
  _currentLocationData; // Full location data if needed (heading, speed etc)
  StreamSubscription?
  _locationSubscription; // Listener for user location changes
  final Rxn<LatLng> userLocation =
  Rxn<LatLng>(); // Reactive user LatLng coordinates

  // --- Driver Finding and Tracking ---
  final RxList<Map<String, dynamic>> nearbyDrivers =
      <Map<String, dynamic>>[].obs; // List of nearby drivers
  StreamSubscription?
  _driverSubscription; // Firestore listener for nearby drivers (needs geoquery)
  StreamSubscription?
  _driverLocationSubscription; // Listener for assigned driver's location updates
  final Rxn<LatLng> driverLocation =
  Rxn<LatLng>(); // Current location of assigned driver
  final RxnString estimatedArrivalTime =
  RxnString(); // ETA for driver to arrive

  // --- API Key ---
  final String _googleApiKey =
      Constants.GOOGLE_MAPS_API_KEY; // Fetched from constants

  // === Lifecycle Methods ===

  @override
  void onInit() {
    super.onInit();

    // Initialize trip controller
    tripController = Get.put(TripController());

    currentUser = _auth.currentUser;
    if (currentUser == null) {
      _logoutUser();
      return;
    }
    // Add listener to search controller
    searchController.addListener(_onSearchChanged);
    _initializeAndFetchDirections();

    // Load user data, wallet balance and wallet transactions
    loadUserData();
    loadWalletBalance();
    loadWalletTransactions();

    // Check for any active trips when the app starts, but only once
    final RxBool hasCheckedForActiveTrips = false.obs;
    ever(isLoading, (loading) {
      // Wait until initialization is complete before checking for active trips
      if (!loading && currentUser != null && !hasCheckedForActiveTrips.value) {
        hasCheckedForActiveTrips.value = true;
        _checkForActiveTrips();
      }
    });
  }

  @override
  void onClose() {
    searchController.removeListener(_onSearchChanged); // Remove listener
    searchController.dispose(); // Dispose search controller
    // Dispose other controllers and cancel streams (existing code)
    passengersController.dispose();
    bagsController.dispose();
    notesController.dispose();
    _tripSubscription?.cancel();
    _driverSubscription?.cancel();
    _locationSubscription?.cancel();
    mapController?.dispose();
    EasyDebounce.cancelAll(); // Cancel any pending debounces
    super.onClose();
  }

  // === Initialization ===

  Future<void> _initializeAndFetchDirections() async {
    homeState.value = HomeState.idle; // Set initial state
    isLoading.value = true; // Show loading indicator
    errorMessage.value = null; // Clear previous errors
    try {
      // 1. Get User's Current Location
      await _getUserLocation(); // Handles permissions and fetching

      // 2. Fetch Predefined Directions from Firestore
      await _fetchDirections();

      // 3. Check for active or scheduled trips
      await _checkForActiveTrips();

      // 4. Update State based on results (if no active trip was found)
      if (currentTrip.value == null && availableDirections.isNotEmpty) {
        homeState.value = HomeState.showingDirections; // Ready to show list
      } else if (currentTrip.value == null) {
        // If directions fetch failed but location succeeded, show error
        if (errorMessage.value == null) {
          errorMessage.value = "No destinations currently available.";
        }
        homeState.value = HomeState.idle; // Stay idle with error message
      }
      // If active trip was found, state is already set by _checkForActiveTrips
    } catch (e) {
      print("Initialization Error: $e");
      errorMessage.value =
      "Initialization failed: ${e.toString()}. Please check permissions and connection.";
      homeState.value = HomeState.idle; // Stay idle on critical error
    } finally {
      isLoading.value = false; // Hide loading indicator
    }
  }

  Future<void> _getUserLocation() async {
    bool serviceEnabled;
    loc.PermissionStatus permissionGranted;

    // Check if location service is enabled
    serviceEnabled = await _locationService.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _locationService.requestService();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled.');
      }
    }

    // Check for location permissions
    permissionGranted = await _locationService.hasPermission();
    if (permissionGranted == loc.PermissionStatus.denied) {
      permissionGranted = await _locationService.requestPermission();
      if (permissionGranted != loc.PermissionStatus.granted) {
        throw Exception('Location permission denied.');
      }
    }

    // Get the current location
    _currentLocationData = await _locationService.getLocation();
    if (_currentLocationData?.latitude != null &&
        _currentLocationData?.longitude != null) {
      userLocation.value = LatLng(
          _currentLocationData!.latitude!, _currentLocationData!.longitude!);
      _updateUserMarker(); // Add user marker to map
      _listenToLocationChanges(); // Start listening for updates
      print("User location obtained: ${userLocation.value}");
    } else {
      throw Exception('Could not get current location.');
    }
  }

  Future<void> _fetchDirections() async {
    try {
      final snapshot = await _firestore
          .collection('directions')
          .orderBy('title')
          .get(); // Example ordering
      if (snapshot.docs.isEmpty) {
        print("No directions found in Firestore.");
        availableDirections.clear();
        availableDirections.add(
          Direction(
              id: 'id',
              title: 'غار حراء',
              basePrice: 200.0,
              pricePerKm: 2.0,
              startLocation: GeoPoint(21.461387864434002, 39.87411971727266),
              endLocation: GeoPoint(21.461387864434002, 39.87411971727266),
              createdAt: Timestamp.now()),
        );
        // Don't set error message here, let the caller decide
      } else {
        availableDirections.value =
            snapshot.docs.map((doc) => Direction.fromFirestore(doc)).toList();
        print("Fetched ${availableDirections.length} directions.");
      }
    } catch (e) {
      print("Error fetching directions: $e");
      availableDirections.clear();
      errorMessage.value = "Could not load destinations."; // Set specific error
      throw e; // Re-throw error to indicate failure
    }
  }

  void _listenToLocationChanges() {
    _locationSubscription?.cancel(); // Cancel existing subscription if any
    _locationSubscription = _locationService.onLocationChanged.listen(
            (loc.LocationData currentLocation) {
          if (currentLocation.latitude != null &&
              currentLocation.longitude != null) {
            userLocation.value =
                LatLng(currentLocation.latitude!, currentLocation.longitude!);
            _currentLocationData = currentLocation; // Store full data
            _updateUserMarker(); // Update marker position on map
          }
        }, onError: (error) {
      print("Error listening to location changes: $error");
      _locationSubscription?.cancel(); // Stop listening on error
    });
  }

  // === User Actions & State Transitions ===

  // Called when user taps a direction from the list
  void selectDirection(Direction direction) {
    selectedDirection.value = direction;
    selectedPickupLocation.value = null; // Clear any previously selected pickup
    mapPolylines.clear(); // Clear old route
    mapMarkers.removeWhere(
            (m) => m.markerId.value == 'pickup'); // Clear old pickup marker
    _clearRouteDetails(); // Clear distance, duration, price
    errorMessage.value = null; // Clear previous errors

    selectedDirection.value = direction;
    _resetPickupAndRoute(); // Use helper to clear pickup/route state
    errorMessage.value = null;

    // Add Destination Marker
    _addOrUpdateMarker(
      LatLng(direction.endLocation.latitude, direction.endLocation.longitude),
      'destination',
      BitmapDescriptor.hueViolet,
      draggable: false,
      info: 'Destination: ${direction.title}',
    );
    _zoomToShowUserAndDestination();
    homeState.value = HomeState.selectingPickup; // Move to next state
  }

  // --- NEW: Handle "Use Current Location" Button ---
  void useCurrentLocationForPickup() {
    if (userLocation.value == null ||
        homeState.value != HomeState.selectingPickup) {
      Get.snackbar('Error',
          'Could not get current location. Please ensure permissions are granted or select on map.');
      return;
    }
    print("Using current location for pickup: ${userLocation.value}");
    _setPickupLocationAndCalculateRoute(userLocation.value!);
  }

  // --- Modified: Handle Map Tap ---
  // void handleMapTap(LatLng tappedPoint) {
  //   if (homeState.value == HomeState.selectingPickup) {
  //     print("Map tapped for pickup: $tappedPoint");
  //     _setPickupLocationAndCalculateRoute(tappedPoint);
  //   } else {
  //     print("Map tapped in non-pickup-selection state: ${homeState.value}. Ignoring.");
  //     // Hide keyboard if search was active?
  //     // FocusScope.of(Get.context!).unfocus();
  //     // clearSearchResults();
  //   }
  // }
  // --- NEW: Shared Logic for Setting Pickup & Calculating Route ---
  void _setPickupLocationAndCalculateRoute(LatLng location) {
    selectedPickupLocation.value = location;
    // Add/update the pickup marker (make it draggable)
    _addOrUpdateMarker(location, 'pickup', BitmapDescriptor.hueOrange,
        draggable: true, info: 'Pickup Location');
    // Clear search results if they were used
    clearSearchResults();
    // Calculate the route from this pickup to the selected destination
    _getDirectionsAndDrawRoute();
  }

  // --- NEW: Search Logic ---

  // Called when the search text changes (with debounce)
  void _onSearchChanged() {
    // Use debounce to avoid API calls on every keystroke
    EasyDebounce.debounce(
        'search-debounce', // Unique ID for this debounce
        const Duration(milliseconds: 600), // Wait 600ms after user stops typing
            () {
          // Function to execute after debounce
          final query = searchController.text.trim();
          print("Debounced Search Query: $query");
          if (query.length < 3) {
            // Don't search for very short strings
            clearSearchResults();
            return;
          }
          if (placesSessionToken == null) {
            // Start a new session
            placesSessionToken = uuid.v4();
            print("Generated new Places session token: $placesSessionToken");
          }
          // Fetch predictions
          _fetchPlacePredictions(query);
        });
  }

  // Fetches place predictions from Google Places Autocomplete API
  Future<void> _fetchPlacePredictions(String input) async {
    if (placesSessionToken == null) {
      print("Error: Places session token is null.");
      return; // Should not happen if logic is correct
    }
    // Show loading/searching indicator for search results
    isSearching.value = true;
    placePredictions.clear(); // Clear previous results

    // Construct the API URL
    // Bias results to user's current location if available
    String locationBias = "";
    if (userLocation.value != null) {
      // Circular bias - radius in meters
      locationBias =
      "&location=${userLocation.value!.latitude}%2C${userLocation.value!.longitude}&radius=50000"; // 50km radius
    }
    // Add country component restriction if desired (e.g., 'country:sa' for Saudi Arabia)
    String components = "&components=country:sa"; // Example: Restrict to sa

    final String url =
        'https://maps.googleapis.com/maps/api/place/autocomplete/json'
        '?input=${Uri.encodeComponent(input)}'
        '&key=$_googleApiKey'
        '&sessiontoken=$placesSessionToken'
        '$locationBias'
        '$components'; // Add components if needed

    print("Places Autocomplete URL: $url");

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);
        if (decodedJson['status'] == 'OK') {
          final List predictionsJson = decodedJson['predictions'];
          placePredictions.value = predictionsJson
              .map((p) => PlaceAutocompletePrediction.fromJson(p))
              .toList();
          print("Fetched ${placePredictions.length} predictions.");
        } else {
          print(
              "Places Autocomplete API Error: Status: ${decodedJson['status']}, Msg: ${decodedJson['error_message']}");
          // Optionally show error to user: errorMessage.value = "Search error: ${decodedJson['status']}";
          placePredictions.clear(); // Clear list on error
        }
      } else {
        print("HTTP Error fetching predictions: ${response.statusCode}");
        // Optionally show error: errorMessage.value = "Network error during search.";
        placePredictions.clear();
      }
    } catch (e) {
      print("Error fetching place predictions: $e");
      // Optionally show error: errorMessage.value = "Could not perform search.";
      placePredictions.clear();
    } finally {
      isSearching.value = false; // Hide loading indicator
    }
  }

  // Called when user selects a prediction from the search results list
  Future<void> onPredictionSelected(
      PlaceAutocompletePrediction prediction) async {
    print(
        "Prediction Selected: ${prediction.description} (ID: ${prediction.placeId})");
    if (placesSessionToken == null) {
      print("Error: Session token missing when selecting prediction.");
      clearSearchResults(); // Clear search UI anyway
      return;
    }

    isLoading.value = true; // Show global loading while getting details
    final String placeId = prediction.placeId;
    final String sessionToken = placesSessionToken!;

    // IMPORTANT: Reset session token *after* getting details or for next search
    placesSessionToken = null; // Consume the token
    clearSearchResults(); // Hide prediction list, clear search text

    // Construct Place Details URL (requesting only geometry/location)
    final String url = 'https://maps.googleapis.com/maps/api/place/details/json'
        '?place_id=$placeId'
        '&fields=geometry/location' // Only request coordinates
        '&key=$_googleApiKey'
        '&sessiontoken=$sessionToken'; // Use the SAME session token

    print("Places Details URL: $url");

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);
        if (decodedJson['status'] == 'OK') {
          final locationData = decodedJson['result']['geometry']['location'];
          final lat = locationData['lat'];
          final lng = locationData['lng'];
          if (lat != null && lng != null) {
            final selectedLatLng = LatLng(lat, lng);
            print("Place Details Location: $selectedLatLng");
            // Use the shared function to set marker and calculate route
            _setPickupLocationAndCalculateRoute(selectedLatLng);
          } else {
            throw Exception(
                'Invalid location data received from Place Details.');
          }
        } else {
          print(
              "Places Details API Error: Status: ${decodedJson['status']}, Msg: ${decodedJson['error_message']}");
          throw Exception('Place Details API Error: ${decodedJson['status']}');
        }
      } else {
        print("HTTP Error fetching place details: ${response.statusCode}");
        throw Exception('Network error getting place details.');
      }
    } catch (e) {
      print("Error getting place details: $e");
      errorMessage.value =
      "Could not get location details for the selected place.";
      // Revert state? Maybe back to selecting pickup without a marker.
      backToPickupSelection(); // Reset to pickup selection
    } finally {
      isLoading.value = false;
    }
  }

  // Helper to clear search state
  void clearSearchResults() {
    searchController
        .removeListener(_onSearchChanged); // Temporarily remove listener
    searchController.clear(); // Clear text field
    searchController.addListener(_onSearchChanged); // Re-add listener
    placePredictions.clear(); // Clear prediction list
    isSearching.value = false; // Hide loading/list
    // Hide keyboard
    FocusScope.of(Get.context!).unfocus();
  }

  // Called when the user taps the map
  void handleMapTap(LatLng tappedPoint) {
    // Only allow setting pickup in the correct state
    if (homeState.value == HomeState.selectingPickup) {
      selectedPickupLocation.value = tappedPoint;
      _addOrUpdateMarker(
        tappedPoint,
        'pickup',
        BitmapDescriptor.hueOrange,
        draggable: true, // Allow dragging pickup marker
        info: 'Pickup Location',
      );
      _getDirectionsAndDrawRoute(); // Calculate route immediately after pickup selection
    } else {
      print(
          "Map tapped in non-pickup-selection state: ${homeState.value}. Ignoring.");
      // Optionally show a snackbar: Get.snackbar('Info', 'Select a destination first or confirm route.');
    }
  }

  // Opens a map search dialog to pick a location
  void openMapSearch() async {
    if (homeState.value != HomeState.selectingPickup) {
      Get.snackbar('خطأ', 'يرجى اختيار وجهة أولاً');
      return;
    }
    
    // Clear any existing search results
    clearSearchResults();
    
    // Show a dialog with a search field and map
    final LatLng? result = await Get.dialog(
      Dialog(
        insetPadding: const EdgeInsets.all(10),
        child: Container(
          width: Get.width * 0.9,
          height: Get.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: searchController,
                      decoration: InputDecoration(
                        hintText: 'search_pickup'.tr,
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // Search results list
              Obx(() {
                if (isSearching.value) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (placePredictions.isNotEmpty) {
                  return Expanded(
                    child: ListView.separated(
                      itemCount: placePredictions.length,
                      separatorBuilder: (_, __) => const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final prediction = placePredictions[index];
                        return ListTile(
                          leading: const Icon(Icons.location_pin),
                          title: Text(prediction.description),
                          onTap: () {
                            onPredictionSelected(prediction);
                            Get.back();
                          },
                        );
                      },
                    ),
                  );
                }
                // Show map when no search results
                return Expanded(
                  child: Column(
                    children: [
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10),
                          child: GoogleMap(
                            initialCameraPosition: CameraPosition(
                              target: userLocation.value ?? const LatLng(24.7136, 46.6753), // Default to Riyadh if no user location
                              zoom: 15,
                            ),
                            onMapCreated: (controller) {
                              // Use a temporary controller just for this map
                            },
                            markers: {
                              if (userLocation.value != null)
                                Marker(
                                  markerId: const MarkerId('user_location'),
                                  position: userLocation.value!,
                                  icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
                                ),
                            },
                            onTap: (LatLng position) {
                              // Return the selected position
                              Get.back(result: position);
                            },
                            myLocationEnabled: true,
                            myLocationButtonEnabled: true,
                            zoomControlsEnabled: true,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text('tap_map_to_select'.tr, style: Get.textTheme.bodyMedium),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
      ),
      barrierDismissible: true,
    );
    
    // Handle the result
    if (result != null) {
      selectedPickupLocation.value = result;
      _addOrUpdateMarker(
        result,
        'pickup',
        BitmapDescriptor.hueOrange,
        draggable: true,
        info: 'Pickup Location',
      );
      _getDirectionsAndDrawRoute();
    }
  }

  // Called when user confirms the route preview
  void confirmRouteAndProceed() {
    if (selectedPickupLocation.value != null &&
        selectedDirection.value != null &&
        homeState.value == HomeState.showingRoutePreview) {
      homeState.value = HomeState.enteringDetails;
      // Reset form fields to defaults when entering details
      passengersController.text = '1';
      bagsController.text = '0';
      notesController.clear();
    } else {
      print("Cannot proceed: Route not finalized or state mismatch.");
      Get.snackbar(
          'Error', 'Please select pickup and wait for route calculation.');
    }
  }

  void _resetPickupAndRoute() {
    selectedPickupLocation.value = null;
    mapPolylines.clear();
    mapMarkers.removeWhere((m) => m.markerId.value == 'pickup');
    _clearRouteDetails();
    errorMessage.value = null; // Clear route-specific errors
    clearSearchResults(); // Clear search state too
  }

  // Called when user clicks "Request Ride" from the details form
  Future<void> requestRide() async {
    // Validate preconditions
    if (currentUser == null ||
        selectedPickupLocation.value == null ||
        selectedDirection.value == null ||
        estimatedPrice.value == null) {
      // Ensure price is calculated
      errorMessage.value = "Missing required information to request ride.";
      return;
    }
    if (!tripDetailsFormKey.currentState!.validate()) {
      Get.snackbar(
          'Validation Error', 'Please check the number of passengers/bags.');
      return;
    }

    // Check if scheduled trip has a valid date
    if (isScheduledTrip.value && scheduledDateTime.value == null) {
      errorMessage.value =
      "Please select a valid date and time for your scheduled trip.";
      Get.snackbar('Validation Error',
          'Please select a date and time for your scheduled trip.');
      return;
    }

    isLoading.value = true; // Show loading indicator
    errorMessage.value = null;
    homeState.value = HomeState.requestingRide; // Update state

    try {
      final newTripRef =
      _firestore.collection('tripRequests').doc(); // Generate Firestore ID
      final now = Timestamp.now();
      final pickupGeoPoint = GeoPoint(selectedPickupLocation.value!.latitude,
          selectedPickupLocation.value!.longitude);
      final dropoffGeoPoint =
          selectedDirection.value!.endLocation; // Use GeoPoint from Direction

      // Create timestamp for scheduled time if needed
      Timestamp? scheduledTime;
      if (isScheduledTrip.value && scheduledDateTime.value != null) {
        scheduledTime = Timestamp.fromDate(scheduledDateTime.value!);
      }

      // Fetch user details to include in trip request
      String userName = 'User';
      double userRating = 0.0;

      if (currentUserModel.value != null) {
        userName = currentUserModel.value!.name;
        userRating = currentUserModel.value!.rating;
      } else {
        // Try to fetch user data if not already loaded
        final userDoc =
        await _firestore.collection('users').doc(currentUser!.uid).get();
        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          userName = userData['name'] ?? currentUser!.displayName ?? 'User';
          userRating = (userData['rating'] as num?)?.toDouble() ?? 0.0;
        }
      }

      // Calculate price breakdown
      // Use the direction's base price if estimatedPrice is null or 0
      if (estimatedPrice.value == null || estimatedPrice.value == 0.0) {
        basePrice.value = selectedDirection.value!.basePrice;
      } else {
        basePrice.value = estimatedPrice.value!;
      }

      // Ensure base price is never 0
      if (basePrice.value <= 0.0) {
        basePrice.value = 50.0; // Default minimum price
      }

      appFee.value = basePrice.value * 0.10; // 10% app fee
      taxAmount.value = basePrice.value * 0.15; // 15% tax
      totalPrice.value = basePrice.value +
          appFee.value +
          taxAmount.value -
          discountAmount.value;

      final newTrip = TripRequest(
        id: newTripRef.id,
        userId: currentUser!.uid,
        directionId: selectedDirection.value!.id,
        directionTitle: selectedDirection.value!.title,
        pickupLocation: pickupGeoPoint,
        dropoffLocation: dropoffGeoPoint,
        passengers: int.tryParse(passengersController.text) ?? 1,
        bags: int.tryParse(bagsController.text) ?? 0,
        notes: notesController.text.trim(),
        basePrice: basePrice.value,
        appFee: appFee.value,
        taxAmount: taxAmount.value,
        discountAmount: discountAmount.value,
        totalPrice: totalPrice.value,
        paymentMethod: selectedPaymentMethod.value,
        couponId: couponId.value.isNotEmpty ? couponId.value : null,
        couponCode: couponCode.value.isNotEmpty ? couponCode.value : null,
        // Add scheduling information
        isScheduled: isScheduledTrip.value,
        scheduledTime: scheduledTime,
        // Add route information
        distanceText: estimatedDistance.value,
        durationText: estimatedDuration.value,
        // Add user information
        userName: userName,
        userRating: userRating,
        status: TripStatus.searching, // Initial status
        createdAt: now,
      );

      // Save the trip request to Firestore
      await newTripRef.set(newTrip.toFirestore());
      currentTrip.value = newTrip; // Store the active trip locally

      // Show appropriate message based on trip type
      if (isScheduledTrip.value) {
        final formattedDate = scheduledDateTime.value != null
            ? '${scheduledDateTime.value!.day}/${scheduledDateTime.value!.month}/${scheduledDateTime.value!.year} at ${scheduledDateTime.value!.hour}:${scheduledDateTime.value!.minute.toString().padLeft(2, '0')}'
            : 'scheduled time';
        Get.snackbar(
          'Trip Scheduled',
          'Your trip has been scheduled for $formattedDate',
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );
        // Reset the form and go back to directions
        backToDirections();
        isLoading.value = false;
      } else {
        // For immediate trips, proceed with tracking
        _listenToTripStatus(newTrip.id); // Start listening for status updates
        _findNearbyDrivers(); // Start searching for drivers (placeholder)
        _updateMapForTrip(); // Update map markers/view for tracking phase
        // isLoading is handled by the status listener for immediate trips
      }
    } catch (e) {
      print("Error requesting ride: $e");
      errorMessage.value = "Could not request ride. Please try again.";
      homeState.value =
          HomeState.enteringDetails; // Revert state to allow retry
      currentTrip.value = null; // Clear current trip data
      isLoading.value = false;
    }
  }

  // This method is replaced by the new cancelTripRequest method below

  // === Navigation / Reset Methods ===

  // Go back from route preview/details form to PICKUP selection state
  void backToPickupSelection() {
    // Clear only the pickup marker and route, keep direction selected
    selectedPickupLocation.value = null;
    mapPolylines.clear();
    mapMarkers.removeWhere((m) => m.markerId.value == 'pickup');
    _clearRouteDetails();
    errorMessage.value = null;
    homeState.value = HomeState.selectingPickup; // Revert state
    _zoomToShowUserAndDestination(); // Adjust map view
  }

  // Go back from pickup selection (or later stage) to the initial directions list
  void backToDirections() {
    selectedDirection.value = null; // Clear selected direction
    selectedPickupLocation.value = null; // Clear selected pickup
    mapPolylines.clear(); // Clear route line
    mapMarkers.removeWhere((m) =>
    m.markerId.value == 'pickup' ||
        m.markerId.value == 'destination'); // Clear markers
    _clearRouteDetails(); // Clear calculated info
    errorMessage.value = null; // Clear errors
    homeState.value =
        HomeState.showingDirections; // Go back to initial list state
    _recenterMapOnUser(); // Center map back on user
  }

  // Full reset after a trip ends (completed, cancelled, no drivers)
  void _resetAfterTrip() {
    // Stop listening to driver location updates
    _stopListeningToDriverLocation();

    currentTrip.value = null; // Clear active trip data
    selectedDirection.value = null; // Clear selected direction
    selectedPickupLocation.value = null; // Clear selected pickup
    driverLocation.value = null; // Clear driver location
    estimatedArrivalTime.value = null; // Clear ETA

    // Clear all polylines (routes)
    mapPolylines.clear();

    // Clear all trip-related markers including driver marker
    mapMarkers.removeWhere((m) =>
    m.markerId.value == 'pickup' ||
        m.markerId.value == 'destination' ||
        m.markerId.value.startsWith('driver_') ||
        m.markerId.value == 'driver');

    _clearRouteDetails();
    errorMessage.value = null; // Clear any lingering errors
    _updateUserMarker(); // Ensure user marker is still present
    homeState.value =
        HomeState.showingDirections; // Reset state to show directions list
    _recenterMapOnUser(); // Center map on user
    // Optionally re-fetch directions if they might have changed
    // _fetchDirections();
  }

  // Go back from details form to route preview
  void backToRoutePreview() {
    if (homeState.value == HomeState.enteringDetails) {
      homeState.value = HomeState.showingRoutePreview;
    }
  }

  // === Core Logic ===

  // Calculates route using Google Directions API
  Future<void> _getDirectionsAndDrawRoute() async {
    if (selectedPickupLocation.value == null ||
        selectedDirection.value == null) {
      print("_getDirectionsAndDrawRoute preconditions not met.");
      return; // Exit if required points are missing
    }

    isLoading.value = true; // Show loading during calculation
    errorMessage.value = null; // Clear previous errors
    mapPolylines.clear(); // Clear existing route lines
    _clearRouteDetails(); // Clear old calculated values

    try {
      final LatLng pickup = selectedPickupLocation.value!;
      final LatLng destination = LatLng(
          selectedDirection.value!.endLocation.latitude,
          selectedDirection.value!.endLocation.longitude);

      // 1. Get Polyline Points using flutter_polyline_points package
      PolylineResult result = await _polylinePoints.getRouteBetweenCoordinates(
        googleApiKey: _googleApiKey,
        request: PolylineRequest(
            origin: PointLatLng(pickup.latitude, pickup.longitude),
            destination:
            PointLatLng(destination.latitude, destination.longitude),
            mode: TravelMode.driving),
        // travelMode: TravelMode.driving,
      );

      if (result.points.isNotEmpty) {
        // Convert points to LatLng list
        List<LatLng> polylineCoordinates = result.points
            .map((point) => LatLng(point.latitude, point.longitude))
            .toList();

        // Create and add the Polyline object to the map's reactive set
        mapPolylines.add(Polyline(
          polylineId: const PolylineId('route'),
          color: Colors.blueAccent, // Customize color
          width: 5, // Customize width
          points: polylineCoordinates,
        ));

        // 2. Fetch Detailed Route Info (Distance, Duration) using HTTP call
        await _fetchRouteDetails(pickup, destination);

        _zoomToFitRoute(polylineCoordinates); // Adjust map camera
        homeState.value = HomeState.showingRoutePreview; // Update state
        print("Route calculated and drawn successfully.");
      } else {
        print("Directions API Error (polyline_points): ${result.errorMessage}");
        errorMessage.value = result.errorMessage ??
            "Could not calculate route. Please check pickup location.";
        homeState.value =
            HomeState.selectingPickup; // Revert state if route fails
      }
    } catch (e) {
      print("Error getting directions: $e");
      errorMessage.value = "An error occurred calculating the route.";
      homeState.value = HomeState.selectingPickup; // Revert state on error
    } finally {
      isLoading.value = false; // Hide loading indicator
    }
  }

  // Makes HTTP call to Google Directions API for text details
  Future<void> _fetchRouteDetails(LatLng origin, LatLng destination) async {
    final String url = 'https://maps.googleapis.com/maps/api/directions/json'
        '?origin=${origin.latitude},${origin.longitude}'
        '&destination=${destination.latitude},${destination.longitude}'
        '&key=$_googleApiKey';
    //
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);

        if (decodedJson['status'] == 'OK' &&
            (decodedJson['routes'] as List).isNotEmpty) {
          final route = decodedJson['routes'][0];
          if ((route['legs'] as List).isNotEmpty) {
            final leg = route['legs'][0];
            estimatedDistance.value = leg['distance']['text']; // e.g., "5.2 km"
            estimatedDuration.value =
            leg['duration']['text']; // e.g., "15 mins"

            // --- Example Pricing Logic (ADJUST AS NEEDED) ---
            double baseFare = 2.00; // Example base fare
            double costPerKm = 1.20; // Example cost per kilometer
            double costPerMin = 0.15; // Example cost per minute
            double distanceInKm = (leg['distance']['value'] ?? 0) / 1000.0;
            double durationInMin = (leg['duration']['value'] ?? 0) / 60.0;

            double calculatedPrice = baseFare +
                (distanceInKm * costPerKm) +
                (durationInMin * costPerMin);
            // Apply minimum fare? Surge pricing? Promotions?
            double minimumFare = 5.00;
            estimatedPrice.value =
            calculatedPrice < minimumFare ? minimumFare : calculatedPrice;
            // -------------------------------------------------

            // Calculate price breakdown immediately
            // Use the direction's base price if estimatedPrice is null or 0
            if (estimatedPrice.value == null || estimatedPrice.value == 0.0) {
              basePrice.value = selectedDirection.value!.basePrice;
            } else {
              basePrice.value = estimatedPrice.value!;
            }

            // Ensure base price is never 0
            if (basePrice.value <= 0.0) {
              basePrice.value = 50.0; // Default minimum price
            }

            appFee.value = basePrice.value * 0.10; // 10% app fee
            taxAmount.value = basePrice.value * 0.15; // 15% tax
            totalPrice.value = basePrice.value +
                appFee.value +
                taxAmount.value -
                discountAmount.value;

            print(
                "Route Details: Dist: ${estimatedDistance.value}, Dur: ${estimatedDuration.value}, Price: \$${estimatedPrice.value?.toStringAsFixed(2)}");
          } else {
            throw Exception('Directions API response missing legs.');
          }
        } else {
          print(
              "Directions API Error (details): Status: ${decodedJson['status']}, Msg: ${decodedJson['error_message']}");
          throw Exception('Directions API error: ${decodedJson['status']}');
        }
      } else {
        throw Exception(
            'Failed to load directions details (HTTP ${response.statusCode})');
      }
    } catch (e) {
      print("Error fetching route details: $e");
      errorMessage.value = "Could not get route details."; // Set specific error
      _clearRouteDetails(); // Clear values on error
    }
  }

  void _clearRouteDetails() {
    estimatedDistance.value = null;
    estimatedDuration.value = null;
    estimatedPrice.value = null;
  }

  // === Map Management ===

  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
    // Apply custom map style here if desired (load from JSON)
    // mapController?.setMapStyle(_mapStyleJson);
    print("Map Controller Initialized.");
    // Set initial camera position based on user location (if available)
    if (userLocation.value != null) {
      _updateMapCamera(userLocation.value!, zoom: 14.0);
    }
    _updateUserMarker(); // Ensure user marker is added if location is known
  }

  void _updateMapCamera(LatLng target, {double zoom = 14.0}) {
    mapController?.animateCamera(CameraUpdate.newLatLngZoom(target, zoom));
  }

  void _recenterMapOnUser() {
    if (userLocation.value != null) {
      mapController?.animateCamera(
          CameraUpdate.newLatLngZoom(userLocation.value!, 14.0));
    }
  }

  void _zoomToShowUserAndDestination() {
    if (userLocation.value == null ||
        selectedDirection.value == null ||
        mapController == null) return;
    try {
      LatLng destinationLatLng = LatLng(
          selectedDirection.value!.endLocation.latitude,
          selectedDirection.value!.endLocation.longitude);
      LatLngBounds bounds =
      _boundsFromLatLngList([userLocation.value!, destinationLatLng]);
      mapController?.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 80.0)); // Adjust padding
    } catch (e) {
      print("Error zooming to user/destination: $e");
      _recenterMapOnUser(); // Fallback
    }
  }

  void _zoomToFitRoute(List<LatLng> points) {
    if (points.isEmpty || mapController == null) return;
    try {
      if (points.length == 1) {
        mapController
            ?.animateCamera(CameraUpdate.newLatLngZoom(points.first, 15));
      } else {
        LatLngBounds bounds = _boundsFromLatLngList(points);
        mapController?.animateCamera(
            CameraUpdate.newLatLngBounds(bounds, 60.0)); // Adjust padding
      }
    } catch (e) {
      print("Error zooming to fit route: $e");
      // Fallback to user/destination zoom?
      _zoomToShowUserAndDestination();
    }
  }

  // Helper to create LatLngBounds from a list of points
  LatLngBounds _boundsFromLatLngList(List<LatLng> list) {
    assert(list.isNotEmpty);
    double? x0, x1, y0, y1;
    for (LatLng latLng in list) {
      if (x0 == null) {
        x0 = x1 = latLng.latitude;
        y0 = y1 = latLng.longitude;
      } else {
        if (latLng.latitude > x1!) x1 = latLng.latitude;
        if (latLng.latitude < x0) x0 = latLng.latitude;
        if (latLng.longitude > y1!) y1 = latLng.longitude;
        if (latLng.longitude < y0!) y0 = latLng.longitude;
      }
    }
    // Add a small buffer to prevent markers being exactly on the edge
    double latPadding = (x1! - x0!) * 0.05;
    double lngPadding = (y1! - y0!) * 0.05;
    if (latPadding == 0)
      latPadding = 0.001; // Minimum padding if points are identical vertically
    if (lngPadding == 0)
      lngPadding =
      0.001; // Minimum padding if points are identical horizontally

    return LatLngBounds(
        northeast: LatLng(x1 + latPadding, y1 + lngPadding),
        southwest: LatLng(x0 - latPadding, y0 - lngPadding));
  }

  // Updates or adds a marker to the map's reactive set
  void _addOrUpdateMarker(LatLng position, String markerId, double hue,
      {bool draggable = false, String? info}) {
    mapMarkers.removeWhere(
            (m) => m.markerId.value == markerId); // Remove previous if exists
    mapMarkers.add(Marker(
      markerId: MarkerId(markerId),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(hue),
      infoWindow: info != null ? InfoWindow(title: info) : InfoWindow.noText,
      draggable: draggable,
      onDragEnd: draggable
          ? (newPosition) {
        // Only add listener if draggable
        print("Marker '$markerId' dragged to $newPosition");
        if (markerId == 'pickup') {
          selectedPickupLocation.value = newPosition;
          _getDirectionsAndDrawRoute(); // Recalculate route on drag end
        }
        // Ignore drag end for non-pickup markers in this logic
      }
          : null, // No listener if not draggable
    ));
  }

  // Specifically updates the user's location marker
  void _updateUserMarker() {
    if (userLocation.value != null) {
      _addOrUpdateMarker(
        userLocation.value!,
        'userLocation',
        BitmapDescriptor.hueAzure, // Unique color for user
        draggable: false, // User marker shouldn't be draggable
        info: 'Your Location',
      );
    }
  }

  // Updates map markers based on the current trip status (e.g., add driver marker)
  void _updateMapForTrip() {
    if (mapController == null) return;
    // Logic needed here:
    // 1. Ensure pickup/destination markers are present based on currentTrip or selectedDirection.
    // 2. If trip is assigned and driver location is known, add/update driver marker.
    //    (Requires listening to driver location - see _findNearbyDrivers placeholder).
    // 3. Adjust camera if needed (e.g., to show user and driver).

    // Example: Add driver marker if available (replace with actual data)
    // if (currentTrip.value?.status == TripStatus.assigned && currentTrip.value?.driverId != null) {
    //    LatLng? driverLatLng = getDriverLocationFromSomewhere(currentTrip.value!.driverId!);
    //    if (driverLatLng != null) {
    //       _addOrUpdateMarker(driverLatLng, 'driver_${currentTrip.value!.driverId}', BitmapDescriptor.hueGreen, draggable: false, info: 'Your Driver');
    //    }
    // }
  }

  // === Trip Status & Driver Tracking ===

  void _listenToTripStatus(String tripId) {
    _tripSubscription?.cancel(); // Cancel previous listener
    _tripSubscription = _firestore
        .collection('tripRequests')
        .doc(tripId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        try {
          currentTrip.value = TripRequest.fromFirestore(snapshot);
          print("Trip Status Updated: ${currentTrip.value?.status}");
          _handleStatusChange(
              currentTrip.value!.status); // Process the status update
        } catch (e) {
          print("Error parsing trip snapshot: $e");
          // Handle error - maybe cancel trip locally?
          errorMessage.value = "Error reading trip update.";
          cancelTripRequest(showMessage: false);
        }
      } else {
        print("Trip $tripId not found or deleted.");
        // If trip disappears, cancel locally
        cancelTripRequest(showMessage: false);
        errorMessage.value = "The trip request could not be found.";
      }
    }, onError: (error) {
      print("Error listening to trip status: $error");
      errorMessage.value = "Connection error while tracking trip.";
      // Consider cancelling or showing persistent error UI
    });
  }

  // Central logic to react to trip status changes from Firestore
  void _handleStatusChange(TripStatus status) {
    isLoading.value =
    false; // Generally stop global loading once status changes

    bool shouldReset = false; // Flag to trigger full UI reset

    // Stop searching for drivers if trip is assigned, completed, or cancelled
    if (status != TripStatus.searching) {
      _stopListeningToDrivers();
      mapMarkers.removeWhere((m) =>
          m.markerId.value.startsWith('driver_')); // Clear driver markers
      _updateMapForTrip(); // Refresh map (pickup/dropoff potentially)
    } else {
      // Ensure driver search is active ONLY if status is searching
      _findNearbyDrivers();
      isLoading.value = true; // Show loading indicator while searching
    }

    // Update UI State (HomeState) and handle terminal cases
    switch (status) {
      case TripStatus.searching:
        homeState.value = HomeState.requestingRide; // Ensure correct state
        break;
      case TripStatus.assigned:
      case TripStatus.enRouteToPickup:
      case TripStatus.arrivedAtPickup:
      case TripStatus.ongoing:
        homeState.value = HomeState.trackingRide;
        // Start listening to the driver's location if we have a driver ID
        if (currentTrip.value?.driverId != null) {
          _listenToAssignedDriverLocation(currentTrip.value!.driverId!);
        }
        _updateMapForTrip(); // Update map with pickup/dropoff markers
        break;
      case TripStatus.noDriversAvailable:
        errorMessage.value =
        "No drivers available currently. Please try again later.";
        Get.snackbar(
            "No Drivers", "We couldn't find a driver for your request.");
        shouldReset = true; // Reset UI back to directions list
        break;
      case TripStatus.completed:
        Get.snackbar("خلصت الرحلة", "تم انتهاء الرحلة بنجاح");

        // Check if payment has already been processed and show rating dialog
        _checkPaymentAndShowRating();

        shouldReset = true; // Reset UI
        break;
      case TripStatus
          .cancelledByUser: // Already handled locally by cancelTripRequest
      case TripStatus.cancelledByDriver:
        Get.snackbar("Trip Cancelled", "The trip request has been cancelled.");
        shouldReset = true; // Reset UI
        break;
      case TripStatus.scheduled:
        // TODO: Handle this case.
    }

    if (shouldReset) {
      _resetAfterTrip(); // Trigger full reset back to showing directions
    }
  }

  // --- Driver Finding (Placeholder - Replace with real GeoFire/GeoQuery) ---
  void _findNearbyDrivers() {
    // **** IMPORTANT: This is a basic placeholder. ****
    // **** Replace with actual Geofire/Geoquery implementation for scalability. ****
    print("Placeholder: Simulating finding nearby drivers...");
    _stopListeningToDrivers(); // Stop previous listener

    // Example: Listen to *all* available drivers (NOT efficient for production)
    _driverSubscription = _firestore
        .collection('drivers') // Assumes this collection exists
    // .where('isOnline', isEqualTo: true) // Example filter for available drivers
    // .where('location', isNear: ...) // *** NEED GEOQUERY HERE ***
        .limit(10) // Limit results for demo performance
        .snapshots()
        .listen((snapshot) {
      // Only process if we are still in the searching state
      if (currentTrip.value?.status != TripStatus.searching) {
        _stopListeningToDrivers();
        return;
      }

      nearbyDrivers.clear(); // Clear previous list
      mapMarkers.removeWhere((m) =>
          m.markerId.value.startsWith('driver_')); // Clear old driver markers

      for (var doc in snapshot.docs) {
        final data = doc.data();
        final driverId = doc.id;
        // Check if location data exists and is a GeoPoint
        if (data['location'] is GeoPoint) {
          final geoPoint = data['location'] as GeoPoint;
          final latLng = LatLng(geoPoint.latitude, geoPoint.longitude);
          nearbyDrivers
              .add({'id': driverId, 'location': latLng}); // Store minimal info

          // Add driver marker to map
          _addOrUpdateMarker(
            latLng,
            'driver_$driverId',
            BitmapDescriptor.hueGreen, // Driver color
            draggable: false,
            info: 'Driver Available', // Basic info
          );
        }
      }
      if (nearbyDrivers.isEmpty) {
        print("No available drivers found in current query.");
      }
    }, onError: (error) {
      print("Error listening to drivers: $error");
      // Don't necessarily stop searching on temporary error? Maybe retry?
    });

    // Simulate a timeout for finding a driver (optional, backend might handle this)
    Future.delayed(const Duration(seconds: 200), () {
      // Example: 1 minute timeout
      if (currentTrip.value != null &&
          currentTrip.value!.status == TripStatus.searching) {
        print("Driver search timed out locally.");
        // Update trip status to 'noDriversAvailable' in Firestore
        // The listener _listenToTripStatus will then handle the UI change.
        _firestore
            .collection('tripRequests')
            .doc(currentTrip.value!.id)
            .update({
          'status': TripRequest.statusToString(TripStatus.noDriversAvailable)
        }).catchError(
                (e) => print("Error updating trip status to timeout: $e"));
      }
    });
  }

  void _stopListeningToDrivers() {
    if (_driverSubscription != null) {
      _driverSubscription?.cancel();
      _driverSubscription = null;
      nearbyDrivers.clear(); // Clear local list
      mapMarkers.removeWhere((m) =>
          m.markerId.value.startsWith('driver_')); // Remove driver markers
      print("Stopped listening to drivers.");
    }
  }

  // Listen to the assigned driver's location updates
  void _listenToAssignedDriverLocation(String driverId) {
    // Cancel any existing subscription
    _stopListeningToDriverLocation();

    // Start listening to the driver's location updates
    _driverLocationSubscription = _firestore
        .collection('driverLocations')
        .doc(driverId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists && snapshot.data() != null) {
        final data = snapshot.data()!;
        // Check if location data exists and is a GeoPoint
        if (data['location'] is GeoPoint) {
          final geoPoint = data['location'] as GeoPoint;
          driverLocation.value = LatLng(geoPoint.latitude, geoPoint.longitude);

          // Update the driver marker on the map
          _updateDriverMarkerOnMap();

          // Calculate and update ETA if we have both user and driver locations
          if (userLocation.value != null && currentTrip.value != null) {
            _calculateEstimatedArrivalTime();
          }
        }
      } else {
        // Driver document doesn't exist or has no location
        print("Driver $driverId location data not available");
      }
    }, onError: (error) {
      print("Error listening to driver location: $error");
      errorMessage.value = "Error tracking driver location";
    });
  }

  // Stop listening to driver location updates
  void _stopListeningToDriverLocation() {
    if (_driverLocationSubscription != null) {
      _driverLocationSubscription?.cancel();
      _driverLocationSubscription = null;
      print("Stopped listening to driver location updates");
    }
  }

  // Custom car icon for driver marker
  BitmapDescriptor? _carIcon;
  double _driverHeading = 0.0; // Store driver's heading/bearing

  // Create a custom car marker for the driver
  Future<void> _createCustomMarker() async {
    if (_carIcon == null) {
      // Use a custom car icon from assets
      // Note: You need to add this asset to your project
      try {
        // _carIcon = await BitmapDescriptor.fromAssetImage(
        //   const ImageConfiguration(size: Size(10, 10)),
        //   'assets/images/car.png',
        // );
        //
        _carIcon = await BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueAzure);

        // Note: The above method is deprecated. When you add the actual asset,
        // use the following method instead:
        // _carIcon = await BitmapDescriptor.asset('assets/images/car_top_view.png');
      } catch (e) {
        // Fallback to default marker
        _carIcon =
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
      }
    }
  }

  // Update the driver marker on the map
  Future<void> _updateDriverMarkerOnMap() async {
    // First, clear any existing driver marker
    mapMarkers.removeWhere((m) => m.markerId.value == 'driver');

    // Only add a new marker if we have a driver location and are in tracking state
    if (driverLocation.value != null &&
        homeState.value == HomeState.trackingRide) {
      // Ensure we have the custom marker
      await _createCustomMarker();

      // Calculate heading if we have previous location data
      if (_currentLocationData?.heading != null) {
        _driverHeading = _currentLocationData!.heading!;
      }

      // Add the driver marker with custom icon and rotation
      mapMarkers.add(Marker(
        markerId: const MarkerId('driver'),
        position: driverLocation.value!,
        icon: _carIcon ??
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
        rotation: _driverHeading,
        flat: true, // Make marker flat on the map
        anchor: const Offset(0.5, 0.5), // Center the marker for better rotation
        infoWindow: const InfoWindow(title: 'Your Driver'),
      ));

      // Update the route from driver to destination
      _updateRouteFromDriver();

      // Make sure the map shows both user and driver
      _zoomToShowUserAndDriver();
    } else if (homeState.value != HomeState.trackingRide) {
      // If not in tracking state, clear the driver route
      mapPolylines.removeWhere((p) => p.polylineId.value == 'driverRoute');
    }
  }

  // Calculate the estimated arrival time and route information
  Future<void> _calculateEstimatedArrivalTime() async {
    if (driverLocation.value == null || currentTrip.value == null) return;

    try {
      // Determine the destination based on trip status
      LatLng destination;
      if (currentTrip.value!.status == TripStatus.enRouteToPickup ||
          currentTrip.value!.status == TripStatus.assigned) {
        // Driver is coming to pickup location
        destination = LatLng(currentTrip.value!.pickupLocation.latitude,
            currentTrip.value!.pickupLocation.longitude);
      } else {
        // Driver is heading to dropoff location
        destination = LatLng(currentTrip.value!.dropoffLocation.latitude,
            currentTrip.value!.dropoffLocation.longitude);
      }

      // Call the Directions API to get the ETA
      final url = 'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${driverLocation.value!.latitude},${driverLocation.value!.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&key=$_googleApiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);

        if (decodedJson['status'] == 'OK') {
          if (decodedJson['routes'] != null &&
              decodedJson['routes'].isNotEmpty &&
              decodedJson['routes'][0]['legs'] != null &&
              decodedJson['routes'][0]['legs'].isNotEmpty) {
            final leg = decodedJson['routes'][0]['legs'][0];
            final durationText = leg['duration']['text'];
            final distanceText = leg['distance']['text'];
            final polyline =
            decodedJson['routes'][0]['overview_polyline']['points'];

            // Update the ETA
            estimatedArrivalTime.value = durationText;
            print("Updated ETA: $durationText");

            // Update trip with route information
            await _firestore
                .collection('tripRequests')
                .doc(currentTrip.value!.id)
                .update({
              'durationText': durationText,
              'distanceText': distanceText,
              'routePolyline': polyline,
            });

            // Show notification if this is a significant update
            if (currentTrip.value!.durationText == null ||
                currentTrip.value!.durationText != durationText) {
              _showNotification('Driver Update',
                  'Your driver will arrive in $durationText ($distanceText)');
            }
          }
        }
      }
    } catch (e) {
      print("Error calculating ETA: $e");
      // Don't show error to user for ETA calculation failures
    }
  }

  // Update the route from driver to destination
  Future<void> _updateRouteFromDriver() async {
    // First, clear any existing driver route
    mapPolylines.removeWhere((p) => p.polylineId.value == 'driverRoute');

    // Only draw a new route if we have a driver location, are in tracking state, and have an active trip
    if (driverLocation.value == null ||
        currentTrip.value == null ||
        homeState.value != HomeState.trackingRide) {
      return;
    }

    try {
      // Determine the destination based on trip status
      LatLng destination;
      if (currentTrip.value!.status == TripStatus.enRouteToPickup ||
          currentTrip.value!.status == TripStatus.assigned) {
        // Driver is coming to pickup location
        destination = LatLng(currentTrip.value!.pickupLocation.latitude,
            currentTrip.value!.pickupLocation.longitude);
      } else {
        // Driver is heading to dropoff location
        destination = LatLng(currentTrip.value!.dropoffLocation.latitude,
            currentTrip.value!.dropoffLocation.longitude);
      }

      // Get route points from Google Directions API
      final polylinePoints = PolylinePoints();
      final origin = PointLatLng(
          driverLocation.value!.latitude, driverLocation.value!.longitude);
      final dest = PointLatLng(destination.latitude, destination.longitude);

      // Call the Google Directions API directly
      final url = 'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${dest.latitude},${dest.longitude}'
          '&mode=driving'
          '&key=$_googleApiKey';

      final response = await http.get(Uri.parse(url));
      final decodedJson = json.decode(response.body);

      PolylineResult result = PolylineResult();

      if (decodedJson['status'] == 'OK') {
        // Extract the polyline points from the response
        final points = polylinePoints.decodePolyline(
            decodedJson['routes'][0]['overview_polyline']['points']);
        result.points = points;
      } else {
        result.errorMessage = decodedJson['status'];
      }

      if (result.points.isNotEmpty) {
        // Convert points to LatLng for Google Maps
        final List<LatLng> polylineCoordinates = result.points
            .map((point) => LatLng(point.latitude, point.longitude))
            .toList();

        // Clear existing driver route polylines
        mapPolylines.removeWhere(
                (polyline) => polyline.polylineId.value == 'driverRoute');

        // Add the new polyline with a different color for driver route
        mapPolylines.add(
          Polyline(
            polylineId: const PolylineId('driverRoute'),
            color: Colors.blue, // Different color for driver route
            points: polylineCoordinates,
            width: 5,
            patterns: [
              PatternItem.dash(20),
              PatternItem.gap(10)
            ], // Dashed line for driver route
          ),
        );

        // Zoom to fit the route
        _zoomToFitDriverRoute(polylineCoordinates);
      }
    } catch (e) {
      print("Error updating driver route: $e");
      // Don't show error to user for route calculation failures
    }
  }

  // Zoom to fit the driver route
  void _zoomToFitDriverRoute(List<LatLng> routePoints) {
    if (routePoints.isEmpty || mapController == null) return;

    try {
      // Include both the route points and the user's location
      final allPoints = [...routePoints];
      if (userLocation.value != null) {
        allPoints.add(userLocation.value!);
      }

      final bounds = _boundsFromLatLngList(allPoints);
      mapController?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 80));
    } catch (e) {
      print("Error zooming to fit driver route: $e");
      // Fallback to showing user and driver
      _zoomToShowUserAndDriver();
    }
  }

  // Zoom map to show both user and driver
  void _zoomToShowUserAndDriver() {
    if (userLocation.value != null &&
        driverLocation.value != null &&
        mapController != null) {
      try {
        final bounds =
        _boundsFromLatLngList([userLocation.value!, driverLocation.value!]);
        mapController?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
      } catch (e) {
        print("Error zooming to show user and driver: $e");
        // Fallback to just showing the driver
        if (driverLocation.value != null) {
          mapController?.animateCamera(
              CameraUpdate.newLatLngZoom(driverLocation.value!, 15));
        }
      }
    }
  }

  // Show a notification to the user
  void _showNotification(String title, String message) {
    // For now, we'll use Get.snackbar, but this could be replaced with proper push notifications
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.primary.withOpacity(0.9),
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(10),
      borderRadius: 10,
      icon: const Icon(Icons.notifications_active, color: Colors.white),
    );
  }

  // === Active Trip Management ===

  Future<void> _checkForActiveTrips() async {
    // Don't check for active trips if we already have one or if user is null
    if (currentUser == null) return;

    // If we already have an active trip, don't check for another one
    if (currentTrip.value != null &&
        currentTrip.value!.status != TripStatus.completed &&
        currentTrip.value!.status != TripStatus.cancelledByUser &&
        currentTrip.value!.status != TripStatus.cancelledByDriver &&
        currentTrip.value!.status != TripStatus.noDriversAvailable) {
      return;
    }

    try {
      // Check if we have a tripId from arguments (resuming from notification or trips view)
      String? tripIdFromArgs;
      if (Get.arguments != null && Get.arguments['tripId'] != null) {
        tripIdFromArgs = Get.arguments['tripId'];
      }

      // First, check for active trips (non-completed, non-cancelled)
      final activeTripsSnapshot = await _firestore
          .collection('tripRequests')
          .where('userId', isEqualTo: currentUser!.uid)
          .where('status', whereNotIn: [
        TripRequest.statusToString(TripStatus.completed),
        TripRequest.statusToString(TripStatus.cancelledByUser),
        TripRequest.statusToString(TripStatus.cancelledByDriver),
        TripRequest.statusToString(TripStatus.noDriversAvailable),
      ])
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      // If we have an active trip, resume it
      if (activeTripsSnapshot.docs.isNotEmpty) {
        final tripDoc = activeTripsSnapshot.docs.first;
        final trip = TripRequest.fromFirestore(tripDoc);

        // If it's a scheduled trip that hasn't started yet, show it but don't start tracking
        if (trip.isScheduled &&
            trip.scheduledTime != null &&
            trip.scheduledTime!.toDate().isAfter(DateTime.now()) &&
            trip.status == TripStatus.searching) {
          // Store the trip but don't change the home state
          currentTrip.value = trip;

          // Show a snackbar to inform the user about the scheduled trip
          Get.snackbar(
            'رحلة مجدولة',
            'لديك رحلة مجدولة في ${_formatScheduledTime(trip.scheduledTime!)}',
            backgroundColor: Get.theme.colorScheme.primary.withOpacity(0.8),
            colorText: Colors.white,
            duration: const Duration(seconds: 5),
            mainButton: TextButton(
              onPressed: () => Get.toNamed(Routes.USER_TRIPS),
              child: const Text('عرض', style: TextStyle(color: Colors.white)),
            ),
          );
          return;
        }

        // Resume the active trip
        currentTrip.value = trip;
        _listenToTripStatus(trip.id);
        homeState.value = HomeState.trackingRide;

        // If the trip is assigned to a driver, start listening to driver location
        if (trip.driverId != null) {
          _listenToAssignedDriverLocation(trip.driverId!);
        }

        // Update map for trip
        _updateMapForTrip();

        // Show a snackbar to inform the user
        Get.snackbar(
          'استئناف الرحلة',
          'تم استئناف رحلتك النشطة',
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        return;
      }

      // If we have a specific tripId from arguments, try to load that trip
      if (tripIdFromArgs != null) {
        final tripDoc = await _firestore
            .collection('tripRequests')
            .doc(tripIdFromArgs)
            .get();

        if (tripDoc.exists) {
          final trip = TripRequest.fromFirestore(tripDoc);

          // Only resume if it's an active trip
          if (trip.status != TripStatus.completed &&
              trip.status != TripStatus.cancelledByUser &&
              trip.status != TripStatus.cancelledByDriver &&
              trip.status != TripStatus.noDriversAvailable) {
            // If it's a scheduled trip that hasn't started yet, show it but don't start tracking
            if (trip.isScheduled &&
                trip.scheduledTime != null &&
                trip.scheduledTime!.toDate().isAfter(DateTime.now()) &&
                trip.status == TripStatus.searching) {
              // Store the trip but don't change the home state
              currentTrip.value = trip;

              // Show a snackbar to inform the user about the scheduled trip
              Get.snackbar(
                'رحلة مجدولة',
                'لديك رحلة مجدولة في ${_formatScheduledTime(trip.scheduledTime!)}',
                backgroundColor: Get.theme.colorScheme.primary.withOpacity(0.8),
                colorText: Colors.white,
                duration: const Duration(seconds: 5),
                mainButton: TextButton(
                  onPressed: () => Get.toNamed(Routes.USER_TRIPS),
                  child:
                  const Text('عرض', style: TextStyle(color: Colors.white)),
                ),
              );
              return;
            }

            // Resume the trip
            currentTrip.value = trip;
            _listenToTripStatus(trip.id);
            homeState.value = HomeState.trackingRide;

            // If the trip is assigned to a driver, start listening to driver location
            if (trip.driverId != null) {
              _listenToAssignedDriverLocation(trip.driverId!);
            }

            // Update map for trip
            _updateMapForTrip();

            // Show a snackbar to inform the user
            Get.snackbar(
              'استئناف الرحلة',
              'تم استئناف رحلتك النشطة',
              backgroundColor: Get.theme.colorScheme.primary,
              colorText: Colors.white,
              duration: const Duration(seconds: 3),
            );
          }
        }
      }
    } catch (e) {
      print("Error checking for active trips: $e");
      // Don't show error to user, just continue with normal initialization
    }
  }

  // Format scheduled time for display
  String _formatScheduledTime(Timestamp timestamp) {
    final dateTime = timestamp.toDate();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final scheduledDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (scheduledDate == today) {
      dateStr = 'اليوم'; // Today
    } else if (scheduledDate == tomorrow) {
      dateStr = 'غداً'; // Tomorrow
    } else {
      dateStr = '${dateTime.year}/${dateTime.month}/${dateTime.day}';
    }

    // Format time as HH:MM
    final timeStr =
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';

    return '$dateStr $timeStr';
  }

  // Check if payment has been processed and show rating dialog
  Future<void> _checkPaymentAndShowRating() async {
    if (currentTrip.value == null) return;

    try {
      // Check if payment has already been processed
      final paymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: currentTrip.value!.id)
          .where('status', isEqualTo: 'completed')
          .get();

      // Pass the completed trip to the trip controller for rating
      tripController.currentTrip.value = currentTrip.value;

      // Reset the rating dialog flag to ensure we can show the dialog
      tripController.ratingDialogShown.value = false;

      // If payment method is wallet and payment hasn't been processed yet
      if (paymentQuery.docs.isEmpty &&
          currentTrip.value!.paymentMethod == 'wallet') {
        // Process wallet payment first, then show rating dialog
        await _processWalletPayment(currentTrip.value!);

        // Show rating dialog after payment processing is complete
        Future.delayed(const Duration(seconds: 1), () {
          tripController.showRatingDialog();
        });
      } else {
        // Payment already processed or not wallet payment, show rating dialog immediately
        Future.delayed(const Duration(seconds: 1), () {
          tripController.showRatingDialog();
        });
      }
    } catch (e) {
      print("Error checking payment status: $e");
      // Still show rating dialog even if there's an error
      tripController.currentTrip.value = currentTrip.value;

      // Reset the rating dialog flag to ensure we can show the dialog
      tripController.ratingDialogShown.value = false;

      Future.delayed(const Duration(seconds: 1), () {
        tripController.showRatingDialog();
      });
    }
  }

  // Process wallet payment
  Future<void> _processWalletPayment(TripRequest trip) async {
    // Generate a unique transaction ID based on trip ID to ensure idempotency
    final String transactionId = '${trip.id}_payment';

    try {
      // First, check if this transaction has already been processed by looking for the transaction ID
      final existingTransactions = await _firestore
          .collection('walletTransactions')
          .where('transactionId', isEqualTo: transactionId)
          .get();

      if (existingTransactions.docs.isNotEmpty) {
        print(
            'Transaction with ID $transactionId already exists. Preventing duplicate.');
        return;
      }

      // Also check if payment has already been processed in the payments collection
      final existingPaymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: trip.id)
          .where('status', isEqualTo: 'completed')
          .get();

      if (existingPaymentQuery.docs.isNotEmpty) {
        print(
            'Payment for trip ${trip.id} already exists in payments collection.');
        return;
      }

      // Use a Firebase transaction to ensure atomicity
      await _firestore.runTransaction((transaction) async {
        // 1. Get current user document for wallet balance
        final userDocRef = _firestore.collection('users').doc(currentUser!.uid);
        final userDoc = await transaction.get(userDocRef);

        if (!userDoc.exists) {
          throw Exception('User document not found');
        }

        // 2. Check wallet balance
        final walletBalance =
        (userDoc.data()?['walletBalance'] ?? 0.0).toDouble();
        if (walletBalance < trip.totalPrice) {
          throw Exception('Insufficient wallet balance');
        }

        // 3. Create payment document
        final paymentRef = _firestore.collection('payments').doc();
        final payment = {
          'tripId': trip.id,
          'userId': currentUser!.uid,
          'driverId': trip.driverId,
          'amount': trip.totalPrice,
          'baseAmount': trip.basePrice,
          'appFee': trip.appFee,
          'taxAmount': trip.taxAmount,
          'discountAmount': trip.discountAmount,
          'method': 'wallet',
          'status': 'completed',
          'createdAt': FieldValue.serverTimestamp(),
          'completedAt': FieldValue.serverTimestamp(),
          'transactionId': transactionId, // Add transaction ID for idempotency
        };

        // 4. Update user's wallet balance
        final newBalance = walletBalance - trip.totalPrice;

        // 5. Create wallet transaction document
        final walletTransactionRef =
        _firestore.collection('walletTransactions').doc();
        final walletTransaction = {
          'userId': currentUser!.uid,
          'amount': -trip.totalPrice,
          'type': 'payment',
          'description': 'Payment for trip #${trip.id}',
          'referenceId': paymentRef.id,
          'tripId': trip.id,
          'timestamp': FieldValue.serverTimestamp(),
          'balance': newBalance,
          'status': 'completed',
          'transactionId': transactionId, // Add transaction ID for idempotency
        };

        // 6. Update trip request
        final tripRef = _firestore.collection('tripRequests').doc(trip.id);

        // 7. Execute all operations within the transaction
        transaction.set(paymentRef, payment);
        transaction.update(userDocRef, {'walletBalance': newBalance});
        transaction.set(walletTransactionRef, walletTransaction);
        transaction.update(tripRef, {
          'paymentStatus': 'completed',
          'paymentMethod': 'wallet',
          'paymentId': paymentRef.id,
          'paymentCompletedAt': FieldValue.serverTimestamp(),
        });

        // Return the new balance to use after transaction completes
        return newBalance;
      }).then((newBalance) {
        // Transaction successful, update local wallet balance
        walletBalance.value = newBalance;

        // Show success message
        Get.snackbar(
          '\u062a\u0645 \u0627\u0644\u062f\u0641\u0639',
          '\u062a\u0645 \u062e\u0635\u0645 \u0627\u0644\u0645\u0628\u0644\u063a \u0645\u0646 \u0645\u062d\u0641\u0638\u062a\u0643',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }).catchError((error) {
        // Handle specific errors
        if (error.toString().contains('Insufficient wallet balance')) {
          Get.snackbar(
            '\u062e\u0637\u0623',
            '\u0631\u0635\u064a\u062f \u0627\u0644\u0645\u062d\u0641\u0638\u0629 \u063a\u064a \u0643\u0627\u0641\u064a',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        } else {
          print('Transaction failed: $error');
          Get.snackbar(
            '\u062e\u0637\u0623',
            '\u062d\u062f\u062b \u062e\u0637\u0623 \u0623\u062b\u0646\u0627\u0621 \u0645\u0639\u0627\u0644\u062c\u0629 \u0627\u0644\u062f\u0641\u0639',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
        throw error; // Re-throw to be caught by the outer catch block
      });
    } catch (e) {
      // Only show error if it's not already handled in the transaction
      if (!e.toString().contains('Insufficient wallet balance')) {
        print("Error processing wallet payment: $e");
        Get.snackbar(
          '\u062e\u0637\u0623',
          '\u062d\u062f\u062b \u062e\u0637\u0623 \u0623\u062b\u0646\u0627\u0621 \u0645\u0639\u0627\u0644\u062c\u0629 \u0627\u0644\u062f\u0641\u0639',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }

    isLoading.value = false;
    try {
      print("Logging out user: ${currentUser?.uid}");
      // Cancel all listeners before logging out
      _tripSubscription?.cancel();
      _driverSubscription?.cancel();
      _driverLocationSubscription?.cancel();
      _locationSubscription?.cancel();
      // Sign out from Google if used (add dependency if needed)
      // if (await GoogleSignIn().isSignedIn()) { await GoogleSignIn().signOut(); }
      await _auth.signOut();
      Get.offAllNamed(Routes.LOGIN); // Navigate to login and clear stack
    } catch (e) {
      print("Error during logout: $e");
      Get.snackbar('Logout Error', 'Could not log out. Please try again.');
    } finally {
      isLoading.value = false;
    }
  }

  // Internal helper for forced logout/redirect
  void _logoutUser() {
    Get.offAllNamed(Routes.LOGIN);
  }

  // === Getters (for View) ===

  String get userDisplayName =>
      currentUserModel.value?.name ??
          currentUser?.displayName ??
          currentUser?.email ??
          'User';
  String get userEmail => currentUser?.email ?? 'No Email';

  // === User Profile & Wallet ===

  // Load user data from Firestore
  Future<void> loadUserData() async {
    if (currentUser == null) return;

    try {
      final userDoc =
      await _firestore.collection('users').doc(currentUser!.uid).get();
      if (userDoc.exists) {
        currentUserModel.value = UserModel.fromFirestore(userDoc);
        // Load wallet balance from user data
        walletBalance.value = currentUserModel.value?.walletBalance ?? 0.0;
      }

      // Also load wallet balance using payment service as a backup
      loadWalletBalance();
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  // Load wallet balance using payment service
  Future<void> loadWalletBalance() async {
    if (currentUser == null) return;

    try {
      final balance = await _paymentService.getWalletBalance(currentUser!.uid);
      walletBalance.value = balance;
    } catch (e) {
      debugPrint('Error loading wallet balance: $e');
    }
  }

  // Load wallet transactions
  Future<void> loadWalletTransactions() async {
    if (currentUser == null) return;

    try {
      final transactionsSnapshot = await _firestore
          .collection('walletTransactions')
          .where('userId', isEqualTo: currentUser!.uid)
          .orderBy('timestamp', descending: true)
          .get();

      walletTransactions.value = transactionsSnapshot.docs
          .map((doc) => WalletTransaction.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error loading wallet transactions: $e');
    }
  }

  // Cancel a trip request with reason
  Future<void> cancelTripRequest({
    String reason = 'User cancelled',
    String? customReason,
    bool showMessage = true,
  }) async {
    if (currentTrip.value == null) {
      if (showMessage) {
        errorMessage.value = "No active trip to cancel.";
      }
      return;
    }

    // Show cancellation dialog with reasons
    tripController.showCancellationDialog();

    // The actual cancellation will be handled by the TripController
    // which will update Firestore and show success messages

    // Reset UI state after cancellation
    homeState.value = HomeState.showingDirections;
    currentTrip.value = null;
  }

  Future<Map<String, dynamic>> applyCoupon(String couponCode) async {
    if (couponCode.isEmpty) {
      return {
        'valid': false,
        'message': 'الرجاء إدخال كود الكوبون',
      };
    }

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return {
          'valid': false,
          'message': 'يجب تسجيل الدخول لاستخدام الكوبون',
        };
      }

      // Validate the coupon using the payment service
      final result = await _paymentService.validateCoupon(
        couponCode: couponCode,
        userId: currentUser.uid,
        amount: basePrice.value,
      );

      if (result['valid']) {
        // Calculate discount amount
        final discountAmount = result['discountAmount'] ?? 0.0;
        final couponId = result['couponId'] ?? '';

        return {
          'valid': true,
          'message': 'تم تطبيق الكوبون بنجاح',
          'discountAmount': discountAmount,
          'couponId': couponId,
        };
      } else {
        return {
          'valid': false,
          'message': result['message'] ?? 'الكوبون غير صالح',
        };
      }
    } catch (e) {
      return {
        'valid': false,
        'message': 'حدث خطأ أثناء تطبيق الكوبون',
      };
    }
  }

  // Get available coupons for the current user
  Future<List<Map<String, dynamic>>> getAvailableCoupons() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return [];
      }

      // Get available coupons using the payment service
      return await _paymentService.getAvailableCoupons(
        userId: currentUser.uid,
        amount: basePrice.value,
      );
    } catch (e) {
      return [];
    }
  }
  
  // Create and select a custom direction based on map selection
  Future<void> createCustomDirection() async {
    isLoading.value = true;
    
    try {
      // Show a dialog with a map to select destination
      final LatLng? result = await Get.dialog(
        Dialog(
          insetPadding: const EdgeInsets.all(10),
          child: Container(
            width: Get.width * 0.9,
            height: Get.height * 0.8,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'select_custom_destination'.tr,
                        style: Get.textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Get.back(),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                // Search field
                TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'search_destination'.tr,
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                // Search results or map
                Obx(() {
                  if (isSearching.value) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (placePredictions.isNotEmpty) {
                    return Expanded(
                      child: ListView.separated(
                        itemCount: placePredictions.length,
                        separatorBuilder: (_, __) => const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final prediction = placePredictions[index];
                          return ListTile(
                            leading: const Icon(Icons.location_pin),
                            title: Text(prediction.description),
                            onTap: () async {
                              // Get location details from prediction
                              final location = await _getPlaceDetails(prediction.placeId);
                              if (location != null) {
                                Get.back(result: location);
                              }
                            },
                          );
                        },
                      ),
                    );
                  }
                  // Show map when no search results
                  return Expanded(
                    child: Column(
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: GoogleMap(
                              initialCameraPosition: CameraPosition(
                                target: userLocation.value ?? const LatLng(24.7136, 46.6753), // Default to Riyadh
                                zoom: 15,
                              ),
                              onMapCreated: (controller) {
                                // Use a temporary controller just for this map
                              },
                              markers: {
                                if (userLocation.value != null)
                                  Marker(
                                    markerId: const MarkerId('user_location'),
                                    position: userLocation.value!,
                                    icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
                                  ),
                              },
                              onTap: (LatLng position) {
                                // Return the selected position
                                Get.back(result: position);
                              },
                              myLocationEnabled: true,
                              myLocationButtonEnabled: true,
                              zoomControlsEnabled: true,
                            ),
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text('tap_map_to_select_destination'.tr, style: Get.textTheme.bodyMedium),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
        barrierDismissible: true,
      );
      
      if (result != null) {
        // Create a custom direction with the selected destination
        final customDirection = Direction(
          id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
          title: 'custom_destination'.tr,
          description: 'custom_destination_desc'.tr,
          basePrice: 10.0, // Default base price
          pricePerKm: 2.0, // Default price per km
          startLocation: GeoPoint(userLocation.value?.latitude ?? 0, userLocation.value?.longitude ?? 0),
          endLocation: GeoPoint(result.latitude, result.longitude),
          createdAt: Timestamp.now(),
          isActive: true,
        );
        
        // Select the custom direction
        selectDirection(customDirection);
      }
    } catch (e) {
      print('Error creating custom direction: $e');
      Get.snackbar('error'.tr, 'custom_direction_error'.tr);
    } finally {
      isLoading.value = false;
    }
  }
  
  // Helper method to get place details from place ID
  Future<LatLng?> _getPlaceDetails(String placeId) async {
    if (placesSessionToken == null) {
      placesSessionToken = uuid.v4();
    }
    
    final String url = 'https://maps.googleapis.com/maps/api/place/details/json'
        '?place_id=$placeId'
        '&fields=geometry/location'
        '&key=$_googleApiKey'
        '&sessiontoken=${placesSessionToken!}';
    
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);
        if (decodedJson['status'] == 'OK') {
          final locationData = decodedJson['result']['geometry']['location'];
          final lat = locationData['lat'];
          final lng = locationData['lng'];
          if (lat != null && lng != null) {
            return LatLng(lat, lng);
          }
        }
      }
      return null;
    } catch (e) {
      print('Error getting place details: $e');
      return null;
    }
  }

  // void logout() {}
  Future<void> logout() async {
    try {
      await _auth.signOut();
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      print('Error during logout: $e');
      Get.snackbar(
        'خطاء',
        'حدث خطاء اثناء تسجيل الخروج',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
// Load user data is called from the existing onInit method

  void showCancellationDialog(String tripId) {
    Get.dialog(
      AlertDialog(
        title: const Text('Cancel Trip'),
        content: const Text('Do you want to cancel this trip?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back(); // Close the dialog
              Get.toNamed(
                Routes.CANCEL_TRIP,
                arguments: {'tripId': tripId},
              );
            },
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }
}
