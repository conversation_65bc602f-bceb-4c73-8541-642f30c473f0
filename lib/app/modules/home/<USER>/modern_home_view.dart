import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_dimensions.dart';
import '../../../widgets/modern_app_bar.dart';
import '../../../widgets/modern_drawer.dart';
import '../../../widgets/modern_home_components.dart';
import '../../../widgets/modern_trip_tabs.dart';
import '../../../widgets/modern_card.dart';
import '../../../widgets/modern_button.dart';
import '../controllers/home_controller.dart';
import 'widgets/coupon_widget.dart';
import 'widgets/payment_method_widget.dart';
import 'widgets/payment_summary_widget.dart';
import 'widgets/trip_summary_widget.dart';

class ModernHomeView extends GetView<HomeController> {
  const ModernHomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      drawer: const ModernDrawer(),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Obx(() => _buildStateBasedContent(context)),
      ),
    );
  }

  Widget _buildStateBasedContent(BuildContext context) {
    switch (controller.homeState.value) {
      case HomeState.selectingPickup:
        return _buildPickupSelectionView(context);
      case HomeState.selectingDestination:
        return _buildDestinationSelectionView(context);
      case HomeState.showingRoutePreview:
        return _buildRoutePreviewView(context);
      case HomeState.requestingTrip:
        return _buildTripRequestView(context);
      case HomeState.tripRequested:
        return _buildTripRequestedView(context);
      case HomeState.driverAssigned:
        return _buildDriverAssignedView(context);
      case HomeState.tripInProgress:
        return _buildTripInProgressView(context);
      case HomeState.tripCompleted:
        return _buildTripCompletedView(context);
      default:
        return _buildPickupSelectionView(context);
    }
  }

  Widget _buildPickupSelectionView(BuildContext context) {
    return Column(
      children: [
        // Modern Header
        ModernHomeHeader(
          userName: controller.currentUser.value?.displayName ?? 'مستخدم',
          greeting: _getGreeting(),
          onProfileTap: () => Scaffold.of(context).openDrawer(),
          onNotificationTap: () => _showNotifications(context),
          notificationCount: 2,
        ),
        
        // Quick Actions
        ModernQuickActions(
          onRequestRide: () => controller.startTripRequest(),
          onScheduleRide: () => _showScheduleRide(context),
          onViewTrips: () => _showTripsBottomSheet(context),
          onWallet: () => _showWalletBottomSheet(context),
        ),
        
        // Search Bar
        ModernSearchBar(
          hintText: 'إلى أين تريد الذهاب؟',
          onChanged: (value) => controller.searchDestinations(value),
          onCurrentLocation: () => controller.useCurrentLocation(),
          isLoading: controller.isLoadingLocation.value,
        ),
        
        // Map Container
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Bottom Stats
        _buildBottomStats(context),
      ],
    );
  }

  Widget _buildDestinationSelectionView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'اختر الوجهة',
          onBackPressed: () => controller.backToPickupSelection(),
        ),
        
        // Search Results or Map
        Expanded(
          child: Stack(
            children: [
              _buildModernMapContainer(context),
              
              // Search Results Overlay
              if (controller.searchResults.isNotEmpty)
                _buildSearchResultsOverlay(context),
            ],
          ),
        ),
        
        // Confirm Button
        _buildConfirmDestinationButton(context),
      ],
    );
  }

  Widget _buildRoutePreviewView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'معاينة الرحلة',
          onBackPressed: () => controller.backToDestinationSelection(),
        ),
        
        // Map with Route
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Trip Summary Card
        _buildTripSummaryCard(context),
        
        // Payment and Booking Section
        _buildBookingSection(context),
      ],
    );
  }

  Widget _buildTripRequestView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'طلب الرحلة',
        ),
        
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Loading Card
        _buildRequestingTripCard(context),
      ],
    );
  }

  Widget _buildTripRequestedView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'البحث عن سائق',
        ),
        
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Searching Driver Card
        _buildSearchingDriverCard(context),
      ],
    );
  }

  Widget _buildDriverAssignedView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'تم تعيين السائق',
        ),
        
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Driver Info Card
        _buildDriverInfoCard(context),
      ],
    );
  }

  Widget _buildTripInProgressView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'الرحلة جارية',
        ),
        
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Trip Progress Card
        _buildTripProgressCard(context),
      ],
    );
  }

  Widget _buildTripCompletedView(BuildContext context) {
    return Column(
      children: [
        ModernTransparentAppBar(
          title: 'تمت الرحلة',
        ),
        
        Expanded(
          child: _buildModernMapContainer(context),
        ),
        
        // Trip Completed Card
        _buildTripCompletedCard(context),
      ],
    );
  }

  Widget _buildModernMapContainer(BuildContext context) {
    return ModernCard(
      type: ModernCardType.elevated,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      borderRadius: AppDimensions.radiusL,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        child: GoogleMap(
          onMapCreated: controller.onMapCreated,
          initialCameraPosition: controller.initialCameraPosition,
          markers: controller.markers.toSet(),
          polylines: controller.polylines.toSet(),
          onTap: controller.onMapTapped,
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          style: _getMapStyle(),
        ),
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 600.ms).scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildBottomStats(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          Expanded(
            child: ModernStatsCard(
              title: 'رحلاتي',
              value: '12',
              icon: Icons.directions_car,
              color: AppColors.primary,
              subtitle: 'هذا الشهر',
              onTap: () => _showTripsBottomSheet(context),
            ),
          ),
          const SizedBox(width: AppDimensions.spacing12),
          Expanded(
            child: ModernStatsCard(
              title: 'المحفظة',
              value: '250 ريال',
              icon: Icons.account_balance_wallet,
              color: AppColors.success,
              subtitle: 'الرصيد المتاح',
              onTap: () => _showWalletBottomSheet(context),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 900.ms, delay: 800.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildSearchResultsOverlay(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: ModernCard(
        type: ModernCardType.glass,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: controller.searchResults.length,
          itemBuilder: (context, index) {
            final result = controller.searchResults[index];
            return ListTile(
              leading: const Icon(Icons.location_on, color: AppColors.primary),
              title: Text(result['name'] ?? ''),
              subtitle: Text(result['address'] ?? ''),
              onTap: () => controller.selectDestination(result),
            );
          },
        ),
      ),
    );
  }

  Widget _buildConfirmDestinationButton(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: ModernButton(
        text: 'تأكيد الوجهة',
        onPressed: () => controller.confirmDestination(),
        type: ModernButtonType.gradient,
        size: ModernButtonSize.large,
        isFullWidth: true,
        icon: Icons.check,
      ),
    );
  }

  Widget _buildTripSummaryCard(BuildContext context) {
    return ModernCard(
      type: ModernCardType.glass,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.route, color: AppColors.primary),
              const SizedBox(width: AppDimensions.spacing8),
              Text(
                'ملخص الرحلة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacing16),
          TripSummaryWidget(),
        ],
      ),
    );
  }

  Widget _buildBookingSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          // Payment Method
          PaymentMethodWidget(),
          const SizedBox(height: AppDimensions.spacing12),

          // Coupon
          CouponWidget(),
          const SizedBox(height: AppDimensions.spacing12),

          // Payment Summary
          PaymentSummaryWidget(),
          const SizedBox(height: AppDimensions.spacing16),

          // Book Button
          ModernButton(
            text: 'تأكيد الحجز',
            onPressed: () => controller.requestTrip(),
            type: ModernButtonType.gradient,
            size: ModernButtonSize.large,
            isFullWidth: true,
            icon: Icons.check_circle,
          ),
        ],
      ),
    );
  }

  Widget _buildRequestingTripCard(BuildContext context) {
    return ModernCard(
      type: ModernCardType.glass,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: AppDimensions.spacing16),
          Text(
            'جاري إرسال طلب الرحلة...',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            'يرجى الانتظار',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchingDriverCard(BuildContext context) {
    return ModernCard(
      type: ModernCardType.gradient,
      customGradient: AppColors.primaryGradient,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search,
              color: AppColors.white,
              size: AppDimensions.iconXL,
            ),
          ).animate(onPlay: (controller) => controller.repeat())
              .scale(duration: 1000.ms, begin: const Offset(1.0, 1.0), end: const Offset(1.2, 1.2))
              .then()
              .scale(duration: 1000.ms, begin: const Offset(1.2, 1.2), end: const Offset(1.0, 1.0)),

          const SizedBox(height: AppDimensions.spacing16),
          Text(
            'البحث عن سائق...',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            'سنجد لك أقرب سائق متاح',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.white.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: AppDimensions.spacing16),
          ModernButton(
            text: 'إلغاء الطلب',
            onPressed: () => controller.cancelTripRequest(),
            type: ModernButtonType.outline,
            customColor: AppColors.white,
            size: ModernButtonSize.medium,
          ),
        ],
      ),
    );
  }

  Widget _buildDriverInfoCard(BuildContext context) {
    return ModernCard(
      type: ModernCardType.elevated,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Obx(() {
        final driver = controller.assignedDriver.value;
        return Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: driver?.photoURL != null
                      ? NetworkImage(driver!.photoURL!)
                      : null,
                  child: driver?.photoURL == null
                      ? const Icon(Icons.person, size: 30)
                      : null,
                ),
                const SizedBox(width: AppDimensions.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driver?.name ?? 'السائق',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(Icons.star, color: AppColors.warning, size: 16),
                          Text(' ${driver?.rating?.toStringAsFixed(1) ?? '5.0'}'),
                          const SizedBox(width: AppDimensions.spacing8),
                          Text('${driver?.vehicleModel ?? 'سيارة'}'),
                        ],
                      ),
                    ],
                  ),
                ),
                ModernButton(
                  text: 'اتصال',
                  onPressed: () => controller.callDriver(),
                  type: ModernButtonType.primary,
                  size: ModernButtonSize.small,
                  icon: Icons.phone,
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacing16),
            Row(
              children: [
                Expanded(
                  child: ModernButton(
                    text: 'رسالة',
                    onPressed: () => controller.openChat(),
                    type: ModernButtonType.outline,
                    size: ModernButtonSize.medium,
                    icon: Icons.message,
                  ),
                ),
                const SizedBox(width: AppDimensions.spacing12),
                Expanded(
                  child: ModernButton(
                    text: 'إلغاء',
                    onPressed: () => controller.cancelTrip(),
                    type: ModernButtonType.text,
                    size: ModernButtonSize.medium,
                    customColor: AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        );
      }),
    );
  }

  Widget _buildTripProgressCard(BuildContext context) {
    return ModernCard(
      type: ModernCardType.gradient,
      customGradient: AppColors.secondaryGradient,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Column(
        children: [
          Text(
            'الرحلة جارية',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing16),
          LinearProgressIndicator(
            value: 0.6,
            backgroundColor: AppColors.white.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الوقت المتبقي: 15 دقيقة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.white.withOpacity(0.9),
                ),
              ),
              Text(
                'المسافة: 5 كم',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTripCompletedCard(BuildContext context) {
    return ModernCard(
      type: ModernCardType.elevated,
      margin: const EdgeInsets.all(AppDimensions.marginM),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: AppDimensions.iconXXL,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing16),
          Text(
            'تمت الرحلة بنجاح!',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing8),
          Text(
            'شكراً لاستخدام خدمتنا',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacing24),
          Row(
            children: [
              Expanded(
                child: ModernButton(
                  text: 'تقييم الرحلة',
                  onPressed: () => _showRatingDialog(context),
                  type: ModernButtonType.primary,
                  size: ModernButtonSize.medium,
                  icon: Icons.star,
                ),
              ),
              const SizedBox(width: AppDimensions.spacing12),
              Expanded(
                child: ModernButton(
                  text: 'رحلة جديدة',
                  onPressed: () => controller.startNewTrip(),
                  type: ModernButtonType.gradient,
                  size: ModernButtonSize.medium,
                  icon: Icons.add,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Utility methods
  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  }

  String? _getMapStyle() {
    // Return custom map style JSON if needed
    return null;
  }

  void _showNotifications(BuildContext context) {
    // Implementation for showing notifications
  }

  void _showScheduleRide(BuildContext context) {
    // Implementation for scheduling a ride
  }

  void _showTripsBottomSheet(BuildContext context) {
    // Implementation for showing trips
  }

  void _showWalletBottomSheet(BuildContext context) {
    // Implementation for showing wallet
  }

  void _showRatingDialog(BuildContext context) {
    // Implementation for rating dialog
  }
}
