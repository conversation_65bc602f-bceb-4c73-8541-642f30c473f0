// import 'package:get/get.dart';
//
// import '../controllers/directions_controller.dart';
// import '../controllers/home_controller_new.dart';
// import '../controllers/location_controller.dart';
// import '../controllers/payment_controller.dart';
// import '../controllers/search_controller.dart';
// import '../controllers/trip_controller.dart';
// import '../controllers/trip_request_controller.dart';
// import '../controllers/trip_tracking_controller.dart';
//
// class HomeBindingNew extends Bindings {
//   @override
//   void dependencies() {
//     // First, register the TripController as it's needed by other controllers
//     Get.put(TripController());
//
//     // Register all the individual controllers
//     Get.put(LocationController());
//     Get.put(DirectionsController());
//     Get.put(SearchPlacesController());
//     Get.put(TripRequestController());
//     Get.put(TripTrackingController());
//     Get.put(PaymentController());
//
//     // Finally, register the main HomeController that coordinates everything
//     Get.put(HomeControllerNew());
//   }
// }
