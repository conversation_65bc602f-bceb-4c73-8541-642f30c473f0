import 'dart:async';
import 'dart:convert';

import 'package:ai_delivery_app/app/modules/home/<USER>/location_controller.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;

import '../../../config/constants.dart';
import '../../../data/models/direction_model.dart';

class DirectionsController extends GetxController {
  // --- Dependencies ---
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PolylinePoints _polylinePoints = PolylinePoints();
  final LocationController locationController = Get.find<LocationController>();
  
  // --- Observables ---
  final RxList<Direction> availableDirections = <Direction>[].obs;
  final Rx<Direction?> selectedDirection = Rx<Direction?>(null);
  final Rx<LatLng?> selectedPickupLocation = Rx<LatLng?>(null);
  final RxBool isLoadingDirections = false.obs;
  final RxBool isCalculatingRoute = false.obs;
  final Rx<String?> directionsErrorMessage = Rx<String?>(null);
  
  // --- Route Information ---
  final RxList<LatLng> routePoints = <LatLng>[].obs;
  final RxString estimatedDistance = ''.obs;
  final RxString estimatedDuration = ''.obs;
  final Rx<double?> estimatedPrice = Rx<double?>(null);
  final RxDouble basePrice = 0.0.obs;
  final RxDouble pricePerKm = 0.0.obs;
  final RxDouble appFee = 0.0.obs;
  final RxDouble taxAmount = 0.0.obs;
  final RxDouble discountAmount = 0.0.obs;
  
  // --- Google Maps API Key ---
  final String _googleApiKey = Constants.GOOGLE_MAPS_API_KEY;

  @override
  void onInit() {
    super.onInit();
    fetchAvailableDirections();
  }

  // Fetch available directions from Firestore
  Future<void> fetchAvailableDirections() async {
    isLoadingDirections.value = true;
    directionsErrorMessage.value = null;
    
    try {
      final snapshot = await _firestore
          .collection('directions')
          .orderBy('title')
          .get();
          
      if (snapshot.docs.isEmpty) {
        debugPrint("No directions found in Firestore.");
        availableDirections.clear();
        availableDirections.add(
          Direction(
              id: 'default',
              title: 'غار حراء',
              basePrice: 200.0,
              pricePerKm: 2.0,
              startLocation: GeoPoint(21.461387864434002, 39.87411971727266),
              endLocation: GeoPoint(21.461387864434002, 39.87411971727266),
              createdAt: Timestamp.now()),
        );
      } else {
        availableDirections.value =
            snapshot.docs.map((doc) => Direction.fromFirestore(doc)).toList();
        debugPrint("Fetched ${availableDirections.length} directions.");
      }
    } catch (e) {
      debugPrint("Error fetching directions: $e");
      availableDirections.clear();
      directionsErrorMessage.value = "Could not load destinations.";
      rethrow;
    } finally {
      isLoadingDirections.value = false;
    }
  }

  // Select a direction
  void selectDirection(Direction direction) {
    selectedDirection.value = direction;
    basePrice.value = direction.basePrice;
    pricePerKm.value = direction.pricePerKm;
    
    // Clear previous route
    clearRoute();
    
    // Set destination marker
    final destinationLatLng = LatLng(
      direction.endLocation.latitude,
      direction.endLocation.longitude,
    );
    
    locationController.addOrUpdateMarker(
      destinationLatLng,
      'destination',
      locationController.getDestinationIcon(),
      info: direction.title,
    );
    
    // Zoom to show user and destination
    locationController.zoomToShowUserAndDestination(destinationLatLng);
  }

  // Set pickup location and calculate route
  void setPickupLocationAndCalculateRoute(LatLng location) {
    selectedPickupLocation.value = location;
    
    // Add pickup marker
    locationController.addOrUpdateMarker(
      location,
      'pickup',
      BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      draggable: true,
      info: 'Pickup Location',
      onDragEnd: (newLocation) {
        selectedPickupLocation.value = newLocation;
        calculateRoute();
      },
    );
    
    // Calculate route
    calculateRoute();
  }

  // Use current location as pickup
  void useCurrentLocationAsPickup() {
    if (locationController.userLocation.value == null) {
      directionsErrorMessage.value = 
          'Could not get current location. Please ensure permissions are granted or select on map.';
      return;
    }
    
    debugPrint("Using current location for pickup: ${locationController.userLocation.value}");
    setPickupLocationAndCalculateRoute(locationController.userLocation.value!);
  }

  // Calculate route between pickup and destination
  Future<void> calculateRoute() async {
    if (selectedPickupLocation.value == null ||
        selectedDirection.value == null) {
      debugPrint("calculateRoute preconditions not met.");
      return;
    }
    
    isCalculatingRoute.value = true;
    directionsErrorMessage.value = null;
    
    try {
      final pickup = selectedPickupLocation.value!;
      final destination = LatLng(
        selectedDirection.value!.endLocation.latitude,
        selectedDirection.value!.endLocation.longitude,
      );
      
      // Get route points
      final result = await _polylinePoints.getRouteBetweenCoordinates(
        googleApiKey: _googleApiKey,
        request: PolylineRequest(
            origin: PointLatLng(pickup.latitude, pickup.longitude)
            , destination: PointLatLng(destination.latitude, destination.longitude),
            mode: TravelMode.driving),
      );
      
      if (result.points.isNotEmpty) {
        // Convert to LatLng points for Google Maps
        routePoints.value = result.points
            .map((point) => LatLng(point.latitude, point.longitude))
            .toList();
        
        // Draw polyline on map
        locationController.addPolyline(
          routePoints,
          'route',
          color: Colors.blue,
          width: 5,
        );
        
        // Fetch detailed route info (distance, duration, etc.)
        await fetchRouteDetails(pickup, destination);
        
        // Zoom to fit route
        locationController.zoomToFitRoute(routePoints);
        
        debugPrint("Route calculated and drawn successfully.");
      } else {
        debugPrint("Directions API Error (polyline_points): ${result.errorMessage}");
        directionsErrorMessage.value = result.errorMessage ??
            "Could not calculate route. Please check pickup location.";
        clearRoute();
      }
    } catch (e) {
      debugPrint("Error getting directions: $e");
      directionsErrorMessage.value = "An error occurred calculating the route.";
      clearRoute();
    } finally {
      isCalculatingRoute.value = false;
    }
  }

  // Fetch detailed route information (distance, duration, price)
  Future<void> fetchRouteDetails(LatLng origin, LatLng destination) async {
    try {
      final url = 'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${origin.latitude},${origin.longitude}'
          '&destination=${destination.latitude},${destination.longitude}'
          '&mode=driving'
          '&key=$_googleApiKey';
      
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final decodedJson = json.decode(response.body);
        
        if (decodedJson['status'] == 'OK') {
          final routes = decodedJson['routes'] as List;
          
          if (routes.isNotEmpty) {
            final legs = routes[0]['legs'] as List;
            
            if (legs.isNotEmpty) {
              // Extract distance and duration
              final distanceText = legs[0]['distance']['text'];
              final durationText = legs[0]['duration']['text'];
              final distanceValue = legs[0]['distance']['value'] / 1000.0; // Convert to km
              
              estimatedDistance.value = distanceText;
              estimatedDuration.value = durationText;
              
              // Calculate price based on base price and distance
              final calculatedPrice = basePrice.value + (distanceValue * pricePerKm.value);
              
              // Calculate app fee (10% of base price)
              appFee.value = calculatedPrice * 0.10;
              
              // Calculate tax (15% of base price + app fee)
              taxAmount.value = (calculatedPrice + appFee.value) * 0.15;
              
              // Calculate total price
              estimatedPrice.value = calculatedPrice +
                  appFee.value +
                  taxAmount.value -
                  discountAmount.value;
              
              debugPrint(
                  "Route Details: Dist: $distanceText, Dur: $durationText, Price: \$${estimatedPrice.value?.toStringAsFixed(2)}");
            } else {
              throw Exception('Directions API response missing legs.');
            }
          }
        } else {
          debugPrint(
              "Directions API Error (details): Status: ${decodedJson['status']}, Msg: ${decodedJson['error_message']}");
          throw Exception('Directions API error: ${decodedJson['status']}');
        }
      } else {
        throw Exception(
            'Failed to load directions details (HTTP ${response.statusCode})');
      }
    } catch (e) {
      debugPrint("Error fetching route details: $e");
      directionsErrorMessage.value = "Could not get route details.";
      clearRouteDetails();
    }
  }

  // Clear route information
  void clearRoute() {
    routePoints.clear();
    clearRouteDetails();
    
    // Remove route polyline
    locationController.mapPolylines.removeWhere((p) => p.polylineId.value == 'route');
    
    // Remove pickup marker
    locationController.mapMarkers.removeWhere((m) => m.markerId.value == 'pickup');
    selectedPickupLocation.value = null;
  }

  // Clear route details (distance, duration, price)
  void clearRouteDetails() {
    estimatedDistance.value = '';
    estimatedDuration.value = '';
    estimatedPrice.value = null;
    appFee.value = 0.0;
    taxAmount.value = 0.0;
  }

  // Reset all directions data
  void reset() {
    selectedDirection.value = null;
    clearRoute();
    locationController.mapMarkers.removeWhere((m) => m.markerId.value == 'destination');
  }
}
