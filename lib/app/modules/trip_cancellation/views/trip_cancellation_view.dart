import 'package:ai_delivery_app/app/modules/trip_cancellation/controllers/trip_cancellation_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TripCancellationView extends GetView<TripCancellationController> {
  const TripCancellationView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cancel Trip'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Why are you cancelling this trip?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: controller.cancellationReasons.length,
                itemBuilder: (context, index) {
                  final reason = controller.cancellationReasons[index];
                  return Obx(() => RadioListTile<String>(
                    title: Text(reason),
                    value: reason,
                    groupValue: controller.selectedReason.value,
                    onChanged: (value) {
                      if (value != null) {
                        controller.selectedReason.value = value;
                      }
                    },
                  ));
                },
              ),
            ),
            const SizedBox(height: 20),
            Obx(() => ElevatedButton(
              onPressed: controller.selectedReason.value.isEmpty
                  ? null
                  : () => _showConfirmationDialog(context),
              child: const Text('Cancel Trip'),
            )),
          ],
        ),
      ),
    );
  }

  void _showConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Cancellation'),
          content: const Text(
            'Are you sure you want to cancel this trip? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('No, Keep Trip'),
            ),
            Obx(() => ElevatedButton(
              onPressed: controller.isLoading.value
                  ? null
                  : () {
                      Navigator.of(context).pop();

                      controller.cancelTrip();
                    },
              child: controller.isLoading.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Yes, Cancel Trip'),
            )),
          ],
        );
      },
    );
  }
}
