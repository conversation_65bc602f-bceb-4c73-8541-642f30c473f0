import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TripCancellationController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final TextEditingController otherReasonController = TextEditingController();
  
  final RxString selectedReason = ''.obs;
  final RxBool isLoading = false.obs;
  final RxBool showCancellationFee = false.obs;
  final RxDouble cancellationFee = 0.0.obs;
  
  // Trip details
  final RxString tripStatus = 'pending'.obs;
  final RxString originAddress = ''.obs;
  final RxString destinationAddress = ''.obs;
  final RxString tripTime = ''.obs;
  final RxDouble tripFare = 0.0.obs;
  final RxDouble tripDistance = 0.0.obs;
  
  late String tripId;
  
  final List<String> cancellationReasons = [
    'Driver is taking too long',
    'Found another ride',
    'Wrong pickup location',
    'Wrong destination',
    'Price is too high',
    'Emergency situation',
    'Other reason'
  ];
  
  @override
  void onInit() {
    super.onInit();
    
    // Get trip ID from arguments
    final args = Get.arguments;
    if (args != null && args is Map<String, dynamic>) {
      tripId = args['tripId'] ?? '';
      loadTripDetails();
    } else {
      tripId = '';
      Get.back();
      Get.snackbar(
        'خطأ',
        'لم يتم تحديد رحلة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  @override
  void onClose() {
    otherReasonController.dispose();
    super.onClose();
  }
  
  Future<void> loadTripDetails() async {
    if (tripId.isEmpty) return;
    
    isLoading.value = true;
    
    try {
      final tripDoc = await _firestore.collection('tripRequests').doc(tripId).get();
      
      if (tripDoc.exists) {
        final data = tripDoc.data() as Map<String, dynamic>;
        
        tripStatus.value = data['status'] ?? 'pending';
        originAddress.value = data['originAddress'] ?? 'Unknown';
        destinationAddress.value = data['destinationAddress'] ?? 'Unknown';
        tripTime.value = data['estimatedTime'] ?? '0 min';
        tripFare.value = (data['fare'] ?? 0.0).toDouble();
        tripDistance.value = (data['distance'] ?? 0.0).toDouble();
        
        // Check if cancellation fee applies
        if (tripStatus.value == 'accepted' || tripStatus.value == 'in_progress') {
          showCancellationFee.value = true;
          // Calculate cancellation fee (e.g., 10% of fare)
          cancellationFee.value = tripFare.value * 0.1;
        } else {
          showCancellationFee.value = false;
          cancellationFee.value = 0.0;
        }
      } else {
        Get.back();
        Get.snackbar(
          'خطأ',
          'الرحلة غير موجودة',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات الرحلة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void selectReason(String reason) {
    selectedReason.value = reason;
  }
  
  void confirmCancellation() {
    if (selectedReason.value.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار سبب الإلغاء',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    if (selectedReason.value == 'other' && otherReasonController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى كتابة سبب الإلغاء',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('تأكيد إلغاء الرحلة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('هل أنت متأكد من رغبتك في إلغاء الرحلة؟'),
              if (showCancellationFee.value) ...[
                const SizedBox(height: 16),
                Text(
                  'سيتم خصم ${cancellationFee.value} ر.س كرسوم إلغاء.',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                cancelTrip();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('تأكيد الإلغاء'),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> cancelTrip() async {
    if (tripId.isEmpty) return;
    
    isLoading.value = true;
    
    try {
      // Get the cancellation reason
      String reason = selectedReason.value;
      if (reason == 'other') {
        reason = otherReasonController.text.trim();
      } else {
        // Get the title for the selected reason
        final reasonData = cancellationReasons.firstWhere(
          (r) => r == reason,
        );
        reason = reasonData;
      }
      
      // Get current user
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }
      
      // Update trip status in Firestore
      await _firestore.collection('tripRequests').doc(tripId).update({
        'status': 'cancelled',
        'cancellationReason': reason,
        'cancelledBy': user.uid,
        'cancellationFee': showCancellationFee.value ? cancellationFee.value : 0.0,
        'cancelledAt': FieldValue.serverTimestamp(),
      });
      
      // If there's a cancellation fee, update user's wallet
      if (showCancellationFee.value && cancellationFee.value > 0 && user != null) {
        // Deduct from user's wallet
        await _firestore.collection('users').doc(user.uid).update({
          'walletBalance': FieldValue.increment(-cancellationFee.value),
        });
        
        // Create a transaction record
        await _firestore.collection('walletTransactions').add({
          'userId': user.uid,
          'amount': cancellationFee.value,
          'type': 'deduction',
          'description': 'رسوم إلغاء الرحلة #$tripId',
          'timestamp': FieldValue.serverTimestamp(),
          'status': 'completed',
        });
      }
      
      // Show success message and navigate back
      Get.back(); // Go back to trip screen
      Get.back(); // Go back to home screen
      
      Get.snackbar(
        'تم',
        'تم إلغاء الرحلة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إلغاء الرحلة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
