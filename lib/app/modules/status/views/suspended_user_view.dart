import 'package:ai_delivery_app/app/modules/status/controllers/user_status_controller.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SuspendedUserView extends GetView<UserStatusController> {
  const SuspendedUserView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.timer,
                size: 80,
                color: Colors.orange.shade700,
              ),
              const SizedBox(height: 24),
              Text(
                'الحساب موقوف مؤقتاً',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Obx(() => Text(
                controller.currentUser.value?.statusReason ?? 
                'تم إيقاف حسابك مؤقتاً من استخدام التطبيق.',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              )),
              const SizedBox(height: 24),
            _buildCountdownTimer(context),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => Get.toNamed(Routes.SUPPORT_CHAT),
                icon: const Icon(Icons.support_agent),
                label: const Text('التواصل مع الدعم الفني'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: controller.logout,
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildCountdownTimer(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Text(
          'سيتم إعادة تفعيل حسابك بعد:',
          style: theme.textTheme.titleMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Obx(() {
          final remainingTime = controller.remainingTime.value;
          
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTimeUnit(context, remainingTime.inHours.toString().padLeft(2, '0'), 'ساعة'),
                const SizedBox(width: 8),
                const Text(':', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                const SizedBox(width: 8),
                _buildTimeUnit(context, (remainingTime.inMinutes % 60).toString().padLeft(2, '0'), 'دقيقة'),
                const SizedBox(width: 8),
                const Text(':', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                const SizedBox(width: 8),
                _buildTimeUnit(context, (remainingTime.inSeconds % 60).toString().padLeft(2, '0'), 'ثانية'),
              ],
            ),
          );
        }),
      ],
    );
  }
  
  Widget _buildTimeUnit(BuildContext context, String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
