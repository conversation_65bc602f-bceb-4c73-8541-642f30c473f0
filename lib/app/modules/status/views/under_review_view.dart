import 'package:ai_delivery_app/app/modules/status/controllers/user_status_controller.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UnderReviewView extends GetView<UserStatusController> {
  const UnderReviewView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.pending_actions,
                size: 80,
                color: Colors.blue.shade700,
              ),
              const SizedBox(height: 24),
              Text(
                'الحساب قيد المراجعة',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const Sized<PERSON>ox(height: 16),
              Obx(() => Text(
                controller.currentUser.value?.statusReason ?? 
                'حسابك قيد المراجعة من قبل فريق الإدارة. سيتم إعلامك عند اكتمال المراجعة.',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              )),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  children: [
                    const Text(
                      'ملاحظة:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'قد تستغرق عملية المراجعة ما بين 24-48 ساعة. يرجى التحلي بالصبر.',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => Get.toNamed(Routes.SUPPORT_CHAT),
                icon: const Icon(Icons.support_agent),
                label: const Text('التواصل مع الدعم الفني'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
              const SizedBox(height: 16),
              OutlinedButton(
                onPressed: controller.checkStatus,
                child: const Text('تحديث الحالة'),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: controller.logout,
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
