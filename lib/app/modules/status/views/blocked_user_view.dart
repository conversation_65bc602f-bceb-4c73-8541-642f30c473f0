import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:ai_delivery_app/app/modules/status/controllers/user_status_controller.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BlockedUserView extends GetView<UserStatusController> {
  const BlockedUserView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.block,
                size: 80,
                color: Colors.red.shade700,
              ),
              const SizedBox(height: 24),
              Text(
                'الحساب محظور',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Obx(() => Text(
                controller.currentUser.value?.statusReason ?? 
                'تم حظر حسابك من استخدام التطبيق. يرجى التواصل مع الدعم الفني للمزيد من المعلومات.',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              )),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => Get.toNamed(Routes.SUPPORT_CHAT),
                icon: const Icon(Icons.support_agent),
                label: const Text('التواصل مع الدعم الفني'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: controller.logout,
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
