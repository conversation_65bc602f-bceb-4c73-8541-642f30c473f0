import 'dart:async';

import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserStatusController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  final RxBool isLoading = false.obs;
  final Rx<Duration> remainingTime = Duration.zero.obs;
  
  Timer? _countdownTimer;
  
  @override
  void onInit() {
    super.onInit();
    loadUserData();
    
    // Start countdown timer if user is suspended
    if (currentUser.value?.status == UserStatus.suspended && 
        currentUser.value?.suspensionEndTime != null) {
      _startCountdownTimer();
    }
  }
  
  @override
  void onClose() {
    _countdownTimer?.cancel();
    super.onClose();
  }
  
  Future<void> loadUserData() async {
    isLoading.value = true;
    
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
          currentUser.value = UserModel.fromFirestore(userDoc);
          
          // Start countdown timer if user is suspended
          if (currentUser.value?.status == UserStatus.suspended && 
              currentUser.value?.suspensionEndTime != null) {
            _startCountdownTimer();
          }
        }
      }
    } catch (e) {
      print('Error loading user data: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  void _startCountdownTimer() {
    // Cancel existing timer if any
    _countdownTimer?.cancel();
    
    // Calculate initial remaining time
    _updateRemainingTime();
    
    // Start a timer that ticks every second
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateRemainingTime();
      
      // Check if suspension has ended
      if (remainingTime.value.inSeconds <= 0) {
        timer.cancel();
        _handleSuspensionEnd();
      }
    });
  }
  
  void _updateRemainingTime() {
    if (currentUser.value?.suspensionEndTime != null) {
      final endTime = currentUser.value!.suspensionEndTime!.toDate();
      final now = DateTime.now();
      
      if (endTime.isAfter(now)) {
        remainingTime.value = endTime.difference(now);
      } else {
        remainingTime.value = Duration.zero;
      }
    }
  }
  
  Future<void> _handleSuspensionEnd() async {
    try {
      // Update user status in Firestore
      await _firestore.collection('users').doc(_auth.currentUser?.uid).update({
        'status': UserModel.statusToString(UserStatus.active),
        'statusReason': null,
        'suspensionEndTime': null,
      });
      
      // Reload user data
      await loadUserData();
      
      // Show success message
      Get.snackbar(
        'تم إعادة تفعيل الحساب',
        'تم إعادة تفعيل حسابك بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Navigate to appropriate home screen based on role
      _navigateToHomeScreen();
    } catch (e) {
      print('Error handling suspension end: $e');
    }
  }
  
  Future<void> checkStatus() async {
    isLoading.value = true;
    
    try {
      // Reload user data from Firestore
      await loadUserData();
      
      // Check if status has changed
      if (currentUser.value?.status == UserStatus.active) {
        Get.snackbar(
          'تم تفعيل الحساب',
          'تم تفعيل حسابك بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        
        // Navigate to appropriate home screen
        _navigateToHomeScreen();
      } else {
        Get.snackbar(
          'لا يزال قيد المراجعة',
          'حسابك لا يزال قيد المراجعة. يرجى المحاولة لاحقاً.',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      print('Error checking status: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء التحقق من حالة الحساب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void _navigateToHomeScreen() {
    if (currentUser.value != null) {
      switch (currentUser.value!.role) {
        case 'driver':
          Get.offAllNamed(Routes.DRIVER_HOME);
          break;
        case 'support':
          Get.offAllNamed(Routes.SUPPORT_DASHBOARD);
          break;
        default:
          Get.offAllNamed(Routes.HOME);
          break;
      }
    } else {
      Get.offAllNamed(Routes.HOME);
    }
  }
  
  Future<void> logout() async {
    try {
      await _auth.signOut();
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      print('Error during logout: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تسجيل الخروج',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
