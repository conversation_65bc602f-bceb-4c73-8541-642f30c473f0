import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/models/payment_model.dart';
import '../../../data/models/trip_request_model.dart';
import '../../../routes/app_pages.dart';
import '../../../services/payment_service.dart';

class PaymentController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final PaymentService _paymentService = Get.find<PaymentService>();

  // Trip data
  final String tripId = Get.arguments['tripId'] ?? '';

  // Observable state
  final RxBool isLoading = true.obs;
  final RxBool isProcessing = false.obs;
  final RxBool isCouponLoading = false.obs;
  final RxBool isCouponApplied = false.obs;
  final RxString couponMessage = ''.obs;

  // Payment details
  final RxDouble basePrice = 0.0.obs;
  final RxDouble appFee = 0.0.obs;
  final RxDouble taxAmount = 0.0.obs;
  final RxDouble discountAmount = 0.0.obs;
  final RxDouble totalPrice = 0.0.obs;
  final RxString currencySymbol = 'ر.س'.obs;
  final RxString couponId = ''.obs;
  final RxString couponCode = ''.obs;

  // Payment method
  final RxString selectedPaymentMethod = 'cash'.obs;
  final List<Map<String, dynamic>> paymentMethods = [];

  // Form controllers
  final couponController = TextEditingController();

  // Trip data
  Rx<TripRequest?> trip = Rx<TripRequest?>(null);

  @override
  void onInit() {
    super.onInit();
    // Add default payment methods
    paymentMethods.addAll([
      {
        'id': 'cash',
        'name': 'الدفع عند الاستلام',
        'icon': 'assets/icons/cash.png'
      },
      {'id': 'wallet', 'name': 'المحفظة', 'icon': 'assets/icons/wallet.png'},
      {
        'id': 'card',
        'name': 'بطاقة الائتمان',
        'icon': 'assets/icons/credit_card.png'
      },
      {
        'id': 'apple_pay',
        'name': 'Apple Pay',
        'icon': 'assets/icons/apple_pay.png'
      },
      {'id': 'stc_pay', 'name': 'STC Pay', 'icon': 'assets/icons/stc_pay.png'},
      {'id': 'mada', 'name': 'مدى', 'icon': 'assets/icons/mada.png'},
    ]);
    loadTripData();
  }

  @override
  void onClose() {
    couponController.dispose();
    super.onClose();
  }

  Future<void> loadTripData() async {
    isLoading.value = true;

    try {
      if (tripId.isEmpty) {
        Get.back();
        Get.snackbar(
          'خطأ',
          'معرف الرحلة غير صالح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Get trip data
      final tripDoc =
          await _firestore.collection('tripRequests').doc(tripId).get();

      if (!tripDoc.exists) {
        Get.back();
        Get.snackbar(
          'خطأ',
          'الرحلة غير موجودة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      trip.value = TripRequest.fromFirestore(tripDoc);

      // Set initial payment details
      basePrice.value = trip.value!.basePrice;
      appFee.value = trip.value!.appFee;
      taxAmount.value = trip.value!.taxAmount;
      discountAmount.value = trip.value!.discountAmount;
      totalPrice.value = trip.value!.totalPrice;

      // Check if coupon is already applied
      if (trip.value!.couponId != null && trip.value!.couponCode != null) {
        couponId.value = trip.value!.couponId!;
        couponCode.value = trip.value!.couponCode!;
        couponController.text = trip.value!.couponCode!;
        isCouponApplied.value = true;
        couponMessage.value = 'تم تطبيق الكوبون بنجاح';
      }

      // Get user wallet balance
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        final userDoc =
            await _firestore.collection('users').doc(currentUser.uid).get();
        final walletBalance =
            (userDoc.data()?['walletBalance'] ?? 0.0).toDouble();

        // Update wallet payment method subtitle
        final walletMethodIndex = paymentMethods.indexWhere(
          (method) => method['id'] == 'wallet',
        );

        if (walletMethodIndex != -1) {
          paymentMethods[walletMethodIndex]['subtitle'] =
              'الرصيد: ${currencySymbol.value}${walletBalance.toStringAsFixed(2)}';
        }
      }
    } catch (e) {
      print('Error loading trip data: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات الرحلة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> applyCoupon() async {
    final couponText = couponController.text.trim();
    if (couponText.isEmpty) {
      couponMessage.value = 'الرجاء إدخال كود الخصم';
      return;
    }

    isCouponLoading.value = true;
    couponMessage.value = '';

    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        couponMessage.value = 'يجب تسجيل الدخول لاستخدام الكوبون';
        isCouponLoading.value = false;
        return;
      }

      // First validate the coupon
      final validationResult = await _paymentService.validateCoupon(
        couponCode: couponText,
        userId: currentUser.uid,
        amount: basePrice.value,
      );

      if (!validationResult['valid']) {
        couponMessage.value = validationResult['message'] ?? 'الكوبون غير صالح';
        isCouponLoading.value = false;
        return;
      }

      // Calculate price breakdown with coupon
      final result = await _paymentService.calculateTripPriceBreakdown(
        basePrice: basePrice.value,
        couponCode: couponText,
        userId: currentUser.uid,
      );

      // Update payment details
      appFee.value = result['appFee'];
      taxAmount.value = result['taxAmount'];
      discountAmount.value = result['discountAmount'];
      totalPrice.value = result['totalPrice'];
      couponId.value = result['couponId'] ?? '';
      couponCode.value = couponText; // Ensure we use the entered coupon code
      currencySymbol.value = result['currencySymbol'];

      // Update UI
      isCouponApplied.value = true;
      couponMessage.value = 'تم تطبيق الكوبون بنجاح';

      // Update trip in Firestore
      await _firestore.collection('tripRequests').doc(tripId).update({
        'appFee': appFee.value,
        'taxAmount': taxAmount.value,
        'discountAmount': discountAmount.value,
        'totalPrice': totalPrice.value,
        'couponId': couponId.value,
        'couponCode': couponCode.value,
      });
    } catch (e) {
      print('Error applying coupon: $e');
      couponMessage.value = 'حدث خطأ أثناء تطبيق الكوبون';
    } finally {
      isCouponLoading.value = false;
    }
  }

  void removeCoupon() async {
    isCouponLoading.value = true;

    try {
      // Reset coupon data
      couponController.clear();
      couponId.value = '';
      couponCode.value = '';

      // Recalculate price without coupon
      final result = await _paymentService.calculateTripPriceBreakdown(
        basePrice: basePrice.value,
      );

      // Update payment details
      appFee.value = result['appFee'];
      taxAmount.value = result['taxAmount'];
      discountAmount.value = 0.0;
      totalPrice.value = result['totalPrice'];

      // Update UI
      isCouponApplied.value = false;
      couponMessage.value = '';

      // Update trip in Firestore
      await _firestore.collection('tripRequests').doc(tripId).update({
        'appFee': appFee.value,
        'taxAmount': taxAmount.value,
        'discountAmount': 0.0,
        'totalPrice': totalPrice.value,
        'couponId': null,
        'couponCode': null,
      });
    } catch (e) {
      print('Error removing coupon: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إزالة الكوبون',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isCouponLoading.value = false;
    }
  }

  Future<void> processPayment() async {
    isProcessing.value = true;

    try {
      // Process payment
      final result = await _paymentService.processPayment(
        tripId: tripId,
        amount: totalPrice.value,
        paymentMethod: PaymentMethod.values.firstWhere(
          (method) =>
              method.toString().split('.').last == selectedPaymentMethod.value,
          orElse: () => PaymentMethod.cash,
        ),
        couponId: couponId.value.isNotEmpty ? couponId.value : null,
        couponCode: couponCode.value.isNotEmpty ? couponCode.value : null,
      );

      if (result['success']) {
        // Show success message
        Get.snackbar(
          'نجاح',
          'تم معالجة الدفع بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate back to trip screen
        Get.back(result: {
          'success': true,
          'paymentId': result['paymentId'],
          'paymentMethod': selectedPaymentMethod.value,
        });
      } else {
        // Show error message
        Get.snackbar(
          'خطأ',
          result['message'] ?? 'حدث خطأ أثناء معالجة الدفع',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      print('Error processing payment: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء معالجة الدفع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isProcessing.value = false;
    }
  }

  // Show available coupons dialog
  Future<void> showAvailableCoupons() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      Get.snackbar(
        'خطأ',
        'يجب تسجيل الدخول لعرض الكوبونات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // Show loading dialog
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // Get available coupons
      final coupons = await _paymentService.getAvailableCoupons(
        userId: currentUser.uid,
        amount: basePrice.value,
      );

      // Close loading dialog
      Get.back();

      if (coupons.isEmpty) {
        Get.snackbar(
          'معلومات',
          'لا توجد كوبونات متاحة حالياً',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.blue,
          colorText: Colors.white,
        );
        return;
      }

      // Show coupons dialog
      Get.dialog(
        Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الكوبونات المتاحة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: Get.height * 0.5,
                  ),
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: coupons.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final coupon = coupons[index];
                      final discountText = coupon['discountType'] ==
                              'percentage'
                          ? '${coupon['discountValue']}%'
                          : '${currencySymbol.value}${coupon['discountValue']}';
                      final savingsText =
                          '${currencySymbol.value}${coupon['discountAmount'].toStringAsFixed(2)}';

                      return ListTile(
                        title: Text(coupon['title']),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(coupon['description']),
                            const SizedBox(height: 4),
                            Text(
                              'الخصم: $discountText | التوفير: $savingsText',
                              style: const TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        trailing: ElevatedButton(
                          onPressed: () {
                            Get.back(); // Close dialog
                            couponController.text = coupon['code'];
                            applyCoupon(); // Apply the selected coupon
                          },
                          child: const Text('تطبيق'),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إغلاق'),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } catch (e) {
      // Close loading dialog
      Get.back();

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل الكوبونات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
