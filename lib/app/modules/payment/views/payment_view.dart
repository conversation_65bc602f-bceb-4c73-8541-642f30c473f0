import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/payment_controller.dart';

class PaymentView extends GetView<PaymentController> {
  const PaymentView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الدفع'),
        centerTitle: true,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            // Price summary card
            _buildPriceSummaryCard(context),

            // Coupon section
            _buildCouponSection(context),

            // Payment methods
            Expanded(
              child: _buildPaymentMethods(context),
            ),
          ],
        );
      }),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: controller.isProcessing.value
              ? null
              : () => controller.processPayment(),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            disabledBackgroundColor: Colors.grey.shade300,
          ),
          child: Obx(() => controller.isProcessing.value
              ? const CircularProgressIndicator(color: Colors.white)
              : const Text('تأكيد الدفع')),
        ),
      ),
    );
  }

  Widget _buildPriceSummaryCard(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص السعر',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            // Base price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('سعر الرحلة الأساسي'),
                Obx(() => Text(
                      '${controller.currencySymbol}${controller.basePrice.toStringAsFixed(2)}',
                      style: theme.textTheme.titleMedium,
                    )),
              ],
            ),
            const SizedBox(height: 8),

            // App fee
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('رسوم التطبيق'),
                Obx(() => Text(
                      '${controller.currencySymbol}${controller.appFee.toStringAsFixed(2)}',
                      style: theme.textTheme.titleMedium,
                    )),
              ],
            ),
            const SizedBox(height: 8),

            // Tax
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('الضريبة'),
                Obx(() => Text(
                      '${controller.currencySymbol}${controller.taxAmount.toStringAsFixed(2)}',
                      style: theme.textTheme.titleMedium,
                    )),
              ],
            ),

            // Discount (if applied)
            Obx(() => controller.discountAmount.value > 0
                ? Column(
                    children: [
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'الخصم (${controller.couponCode.value})',
                            style: const TextStyle(color: Colors.green),
                          ),
                          Text(
                            '- ${controller.currencySymbol}${controller.discountAmount.toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
                : const SizedBox.shrink()),

            const Divider(height: 24),

            // Total price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المجموع',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Obx(() => Text(
                      '${controller.currencySymbol}${controller.totalPrice.toStringAsFixed(2)}',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCouponSection(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'كوبون الخصم',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller.couponController,
                  decoration: const InputDecoration(
                    hintText: 'أدخل كود الخصم',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  enabled: !controller.isCouponApplied.value,
                ),
              ),
              const SizedBox(width: 8),
              Obx(() => controller.isCouponApplied.value
                  ? TextButton.icon(
                      onPressed: controller.removeCoupon,
                      icon: const Icon(Icons.close),
                      label: const Text('إزالة'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Show available coupons button
                        IconButton(
                          onPressed: controller.showAvailableCoupons,
                          icon: const Icon(Icons.local_offer),
                          tooltip: 'عرض الكوبونات المتاحة',
                          color: theme.colorScheme.primary,
                        ),
                        // Apply coupon button
                        ElevatedButton(
                          onPressed: controller.isCouponLoading.value
                              ? null
                              : controller.applyCoupon,
                          child: controller.isCouponLoading.value
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text('تطبيق'),
                        ),
                      ],
                    )),
            ],
          ),

          // Coupon message
          Obx(() => controller.couponMessage.value.isNotEmpty
              ? Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    controller.couponMessage.value,
                    style: TextStyle(
                      color: controller.isCouponApplied.value
                          ? Colors.green
                          : Colors.red,
                      fontSize: 12,
                    ),
                  ),
                )
              : const SizedBox.shrink()),

          const SizedBox(height: 16),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'طرق الدفع',
            style: theme.textTheme.titleMedium,
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: controller.paymentMethods.length,
            itemBuilder: (context, index) {
              final method = controller.paymentMethods[index];

              return Obx(() => RadioListTile<String>(
                    title: Text(method['title']),
                    subtitle: Text(method['subtitle']),
                    secondary: Image.asset(
                      method['icon'],
                      width: 40,
                      height: 40,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.payment,
                        size: 40,
                      ),
                    ),
                    value: method['id'],
                    groupValue: controller.selectedPaymentMethod.value,
                    onChanged: (value) {
                      if (value != null) {
                        controller.selectedPaymentMethod.value = value;
                      }
                    },
                    activeColor: theme.colorScheme.primary,
                  ));
            },
          ),
        ),
      ],
    );
  }
}
