import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/auth_controller.dart';
import '../../../../widgets/modern_button.dart';

class SignupButton extends StatelessWidget {
  final AuthController controller;

  const SignupButton({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Get.locale?.languageCode == 'ar';

    return Obx(() => ModernButton(
      text: isArabic ? 'إنشاء حساب' : 'Sign Up',
      onPressed: controller.isLoading.value ? null : controller.signup,
      type: ModernButtonType.gradient,
      size: ModernButtonSize.large,
      isFullWidth: true,
      isLoading: controller.isLoading.value,
      icon: Icons.person_add,
    ));
  }
}
