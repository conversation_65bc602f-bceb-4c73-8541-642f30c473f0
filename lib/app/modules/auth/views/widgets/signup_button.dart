import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/auth_controller.dart';

class SignupButton extends StatelessWidget {
  final AuthController controller;

  const SignupButton({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = Get.locale?.languageCode == 'ar';
    
    return Obx(() => ElevatedButton(
      onPressed: controller.isLoading.value
          ? null
          : controller.signup,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
      ),
      child: controller.isLoading.value
          ? SizedBox(
              height: 24,
              width: 24,
              child: CircularProgressIndicator(
                color: theme.colorScheme.onPrimary,
                strokeWidth: 2.0,
              ),
            )
          : Text(
              isArabic ? 'إنشاء حساب' : 'Sign Up',
              style: const TextStyle(
                  fontSize: 16, fontWeight: FontWeight.bold),
            ),
    ));
  }
}
