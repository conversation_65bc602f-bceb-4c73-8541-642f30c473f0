import 'package:flutter/material.dart';
import '../../../../widgets/modern_text_field.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final bool isPassword;
  final IconData? prefixIcon;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;

  const CustomTextField({
    Key? key,
    required this.controller,
    required this.label,
    required this.hint,
    this.isPassword = false,
    this.prefixIcon,
    this.validator,
    this.keyboardType = TextInputType.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernTextField(
      controller: controller,
      labelText: label,
      hintText: hint,
      obscureText: isPassword,
      prefixIcon: prefixIcon,
      validator: validator,
      keyboardType: keyboardType,
      type: ModernTextFieldType.filled,
    );
  }
}