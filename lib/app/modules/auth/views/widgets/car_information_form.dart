import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../controllers/auth_controller.dart';
import 'language_aware_text_field.dart';
import 'license_plate_field.dart';

class CarInformationForm extends StatelessWidget {
  final AuthController controller;

  const CarInformationForm({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = Get.locale?.languageCode == 'ar';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border:
                Border.all(color: theme.colorScheme.primary.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isArabic ? 'معلومات السيارة' : 'Car Information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                isArabic
                    ? 'سيتم مراجعة حسابك بعد التسجيل.'
                    : 'Your account will be under review after registration.',
                style: TextStyle(
                  fontSize: 12,
                  color: theme.colorScheme.primary.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Car Make Field
        LanguageAwareTextField(
          controller: controller.carMakeController,
          labelTextEn: 'Car Make',
          labelTextAr: 'ماركة السيارة',
          hintTextEn: 'e.g., Toyota, Honda',
          hintTextAr: 'مثال: تويوتا، هوندا',
          prefixIcon: Icons.directions_car,
          validator: controller.validateCarMake,
        ),
        const SizedBox(height: 16),

        // Car Model Field
        LanguageAwareTextField(
          controller: controller.carModelController,
          labelTextEn: 'Car Model',
          labelTextAr: 'موديل السيارة',
          hintTextEn: 'e.g., Camry, Accord',
          hintTextAr: 'مثال: كامري، أكورد',
          prefixIcon: Icons.car_rental,
          validator: controller.validateCarModel,
        ),
        const SizedBox(height: 16),

        // Car Year Field - With Date Picker
        LanguageAwareTextField(
          controller: controller.carYearController,
          labelTextEn: 'Car Year',
          labelTextAr: 'سنة الصنع',
          hintTextEn: 'e.g., 2020',
          hintTextAr: 'مثال: ٢٠٢٠',
          prefixIcon: Icons.date_range,
          keyboardType: TextInputType.number,
          validator: controller.validateCarYear,
          readOnly: true,
          onTap: () => _showYearPicker(context, controller),
        ),
        const SizedBox(height: 16),

        // License Plate Field - Custom component
        LicensePlateField(
          lettersController: controller.carPlateLettersController,
          numbersController: controller.carPlateNumbersController,
          validator: controller.validateCarPlate,
        ),
        const SizedBox(height: 16),

        // Car Color Field - With Color Picker
        LanguageAwareTextField(
          controller: controller.carColorController,
          labelTextEn: 'Car Color',
          labelTextAr: 'لون السيارة',
          hintTextEn: 'e.g., White, Black',
          hintTextAr: 'مثال: أبيض، أسود',
          prefixIcon: Icons.color_lens,
          validator: controller.validateCarColor,
          readOnly: true,
          onTap: () => _showColorPicker(context, controller),
          suffixIcon: Obx(() {
            if (controller.carColorController.text.isNotEmpty) {
              return Container(
                margin: const EdgeInsets.all(8),
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: getColorFromName(controller.carColorController.text),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey),
                ),
              );
            } else {
              return const Icon(Icons.color_lens,
                  size: 0); // Empty icon as placeholder
            }
          }),
        ),
      ],
    );
  }

  // Show year picker dialog
  void _showYearPicker(BuildContext context, AuthController controller) async {
    final isArabic = Get.locale?.languageCode == 'ar';
    final currentYear = DateTime.now().year;
    final initialYear = controller.carYearController.text.isNotEmpty
        ? int.tryParse(controller.carYearController.text) ?? currentYear
        : currentYear;

    final DateTime? picked = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'اختر سنة الصنع' : 'Select Car Year'),
          content: SizedBox(
            width: 300,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(1990),
              lastDate: DateTime(currentYear + 1),
              selectedDate: DateTime(initialYear),
              onChanged: (DateTime dateTime) {
                Navigator.pop(context, dateTime);
              },
            ),
          ),
        );
      },
    );

    if (picked != null) {
      controller.carYearController.text = DateFormat('yyyy').format(picked);
    }
  }

  // Show color picker dialog
  void _showColorPicker(BuildContext context, AuthController controller) {
    final isArabic = Get.locale?.languageCode == 'ar';

    // Common car colors
    final List<Map<String, String>> carColors = [
      {'en': 'White', 'ar': 'أبيض', 'hex': '#FFFFFF'},
      {'en': 'Black', 'ar': 'أسود', 'hex': '#000000'},
      {'en': 'Silver', 'ar': 'فضي', 'hex': '#C0C0C0'},
      {'en': 'Gray', 'ar': 'رمادي', 'hex': '#808080'},
      {'en': 'Red', 'ar': 'أحمر', 'hex': '#FF0000'},
      {'en': 'Blue', 'ar': 'أزرق', 'hex': '#0000FF'},
      {'en': 'Green', 'ar': 'أخضر', 'hex': '#008000'},
      {'en': 'Yellow', 'ar': 'أصفر', 'hex': '#FFFF00'},
      {'en': 'Brown', 'ar': 'بني', 'hex': '#A52A2A'},
      {'en': 'Orange', 'ar': 'برتقالي', 'hex': '#FFA500'},
      {'en': 'Purple', 'ar': 'بنفسجي', 'hex': '#800080'},
      {'en': 'Gold', 'ar': 'ذهبي', 'hex': '#FFD700'},
      {'en': 'Beige', 'ar': 'بيج', 'hex': '#F5F5DC'},
      {'en': 'Burgundy', 'ar': 'نبيذي', 'hex': '#800020'},
    ];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'اختر لون السيارة' : 'Select Car Color'),
          content: SizedBox(
            width: 300,
            height: 300,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1.0,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
              ),
              itemCount: carColors.length,
              itemBuilder: (context, index) {
                final color = carColors[index];
                final colorName = isArabic ? color['ar']! : color['en']!;
                final colorHex = color['hex']!;

                return InkWell(
                  onTap: () {
                    controller.carColorController.text = colorName;
                    Navigator.pop(context);
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: _hexToColor(colorHex),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.grey,
                            width: 1,
                          ),
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        colorName,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(isArabic ? 'إلغاء' : 'Cancel'),
            ),
          ],
        );
      },
    );
  }

  // Convert hex color string to Color
  Color _hexToColor(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  // Get color from name
  Color getColorFromName(String colorName) {
    final lowerColorName = colorName.toLowerCase();

    switch (lowerColorName) {
      case 'white':
      case 'أبيض':
        return Colors.white;
      case 'black':
      case 'أسود':
        return Colors.black;
      case 'silver':
      case 'فضي':
        return const Color(0xFFC0C0C0);
      case 'gray':
      case 'grey':
      case 'رمادي':
        return Colors.grey;
      case 'red':
      case 'أحمر':
        return Colors.red;
      case 'blue':
      case 'أزرق':
        return Colors.blue;
      case 'green':
      case 'أخضر':
        return Colors.green;
      case 'yellow':
      case 'أصفر':
        return Colors.yellow;
      case 'brown':
      case 'بني':
        return Colors.brown;
      case 'orange':
      case 'برتقالي':
        return Colors.orange;
      case 'purple':
      case 'بنفسجي':
        return Colors.purple;
      case 'gold':
      case 'ذهبي':
        return const Color(0xFFFFD700);
      case 'beige':
      case 'بيج':
        return const Color(0xFFF5F5DC);
      case 'burgundy':
      case 'نبيذي':
        return const Color(0xFF800020);
      default:
        return Colors.grey;
    }
  }
}
