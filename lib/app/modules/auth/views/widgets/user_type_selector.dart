import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/auth_controller.dart';

class UserTypeSelector extends StatelessWidget {
  final AuthController controller;

  const UserTypeSelector({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Get.locale?.languageCode == 'ar';
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
            child: Text(
              isArabic ? 'نوع الحساب' : 'Account Type',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 14,
              ),
            ),
          ),
          Obx(() => Row(
                children: [
                  Expanded(
                    child: RadioListTile<String>(
                      title: Text(isArabic ? 'مستخدم' : 'User'),
                      value: 'user',
                      groupValue: controller.userType.value,
                      onChanged: (value) => controller.setUserType(value!),
                      dense: true,
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<String>(
                      title: Text(isArabic ? 'سائق' : 'Driver'),
                      value: 'driver',
                      groupValue: controller.userType.value,
                      onChanged: (value) => controller.setUserType(value!),
                      dense: true,
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }
}
