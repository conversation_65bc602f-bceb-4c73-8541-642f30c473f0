import 'package:flutter/material.dart';

class SocialButtons extends StatelessWidget {
  const SocialButtons({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Text('Or continue with'),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSocialButton(
              icon: Icons.g_mobiledata,
              onPressed: () {
                // TODO: Implement Google sign in
              },
            ),
            _buildSocialButton(
              icon: Icons.facebook,
              onPressed: () {
                // TODO: Implement Facebook sign in
              },
            ),
            _buildSocialButton(
              icon: Icons.apple,
              onPressed: () {
                // TODO: Implement Apple sign in
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return IconButton(
      onPressed: onPressed,
      icon: Icon(icon),
      style: IconButton.styleFrom(
        backgroundColor: Colors.grey[200],
        padding: const EdgeInsets.all(12),
      ),
    );
  }
} 