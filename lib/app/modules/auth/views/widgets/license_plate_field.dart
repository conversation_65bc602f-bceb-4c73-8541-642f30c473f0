import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class LicensePlateField extends StatelessWidget {
  final TextEditingController lettersController;
  final TextEditingController numbersController;
  final String? Function(String?)? validator;

  const LicensePlateField({
    Key? key,
    required this.lettersController,
    required this.numbersController,
    this.validator,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = Get.locale?.languageCode == 'ar';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 12, right: 12, bottom: 8),
          child: Text(
            isArabic ? 'رقم لوحة السيارة' : 'License Plate Number',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic
                      ? 'أدخل رقم اللوحة (3 أحرف عربية و 4 أرقام)'
                      : 'Enter plate number (3 Arabic letters and 4 numbers)',
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.hintColor,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    // Arabic Letters Field (3 characters)
                    Expanded(
                      flex: 3,
                      child: TextFormField(
                        controller: lettersController,
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          labelText: isArabic ? 'الأحرف' : 'Letters',
                          hintText: 'ح ج ب',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                        ),
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(3),
                          // Only allow Arabic letters
                          FilteringTextInputFormatter.allow(RegExp(r'[\u0600-\u06FF]')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isArabic ? 'مطلوب' : 'Required';
                          }
                          if (value.length < 3) {
                            return isArabic ? '3 أحرف' : '3 letters';
                          }
                          // Check if all characters are Arabic
                          final arabicRegex = RegExp(r'^[\u0600-\u06FF]+$');
                          if (!arabicRegex.hasMatch(value)) {
                            return isArabic ? 'أحرف عربية فقط' : 'Arabic only';
                          }
                          return null;
                        },
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Numbers Field (4 digits)
                    Expanded(
                      flex: 4,
                      child: TextFormField(
                        controller: numbersController,
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: isArabic ? 'الأرقام' : 'Numbers',
                          hintText: '1234',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                        ),
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(4),
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isArabic ? 'مطلوب' : 'Required';
                          }
                          if (value.length < 4) {
                            return isArabic ? '4 أرقام' : '4 digits';
                          }
                          return null;
                        },
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Preview of the license plate
                Container(
                  margin: const EdgeInsets.only(top: 16),
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade400),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        isArabic ? 'معاينة اللوحة:' : 'Plate Preview:',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.grey.shade500),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              numbersController.text.padRight(4, '_'),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              lettersController.text.padRight(3, '_'),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2,
                              ),
                              textDirection: TextDirection.rtl,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
