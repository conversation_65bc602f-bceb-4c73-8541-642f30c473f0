import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../data/services/translation_service.dart';

class AuthLanguageSelector extends StatelessWidget {
  const AuthLanguageSelector({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Get.locale?.languageCode == 'ar';
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton.icon(
          icon: const Icon(Icons.language),
          label: Text(isArabic ? 'English' : 'العربية'),
          onPressed: () {
            if (isArabic) {
              TranslationService.changeLocale('en', 'US');
            } else {
              TranslationService.changeLocale('ar', 'SA');
            }
          },
        ),
      ],
    );
  }
}
