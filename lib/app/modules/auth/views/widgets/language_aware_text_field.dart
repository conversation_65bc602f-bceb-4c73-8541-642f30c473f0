import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../widgets/modern_text_field.dart';

class LanguageAwareTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelTextEn;
  final String labelTextAr;
  final String? hintTextEn;
  final String? hintTextAr;
  final IconData prefixIcon;
  final bool obscureText;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final TextCapitalization textCapitalization;
  final AutovalidateMode autovalidateMode;
  final bool readOnly;
  final VoidCallback? onTap;
  final int? maxLines;
  final TextInputAction? textInputAction;
  final ModernTextFieldType type;

  const LanguageAwareTextField({
    Key? key,
    required this.controller,
    required this.labelTextEn,
    required this.labelTextAr,
    this.hintTextEn,
    this.hintTextAr,
    required this.prefixIcon,
    this.obscureText = false,
    this.suffixIcon,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.textCapitalization = TextCapitalization.none,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.readOnly = false,
    this.onTap,
    this.maxLines = 1,
    this.textInputAction,
    this.type = ModernTextFieldType.filled,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Get.locale?.languageCode == 'ar';

    return ModernTextField(
      controller: controller,
      labelText: isArabic ? labelTextAr : labelTextEn,
      hintText: isArabic ? (hintTextAr ?? '') : (hintTextEn ?? ''),
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      obscureText: obscureText,
      validator: validator,
      keyboardType: keyboardType,
      readOnly: readOnly,
      onTap: onTap,
      maxLines: maxLines,
      textInputAction: textInputAction,
      type: type,
    );
  }
}
