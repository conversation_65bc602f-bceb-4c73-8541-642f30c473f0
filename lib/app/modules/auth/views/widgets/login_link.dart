import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/auth_controller.dart';

class LoginLink extends StatelessWidget {
  final AuthController controller;

  const LoginLink({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = Get.locale?.languageCode == 'ar';
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          isArabic ? "لديك حساب بالفعل؟" : "Already have an account?",
          style: TextStyle(color: theme.hintColor),
        ),
        TextButton(
          onPressed: controller.isLoading.value
              ? null
              : controller.goToLogin,
          child: Text(
            isArabic ? 'تسجيل الدخول' : 'Login',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }
}
