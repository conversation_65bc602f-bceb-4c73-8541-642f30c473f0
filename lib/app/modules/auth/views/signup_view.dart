import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/auth_controller.dart';
import 'widgets/auth_language_selector.dart';
import 'widgets/car_information_form.dart';
import 'widgets/language_aware_text_field.dart';
import 'widgets/login_link.dart';
import 'widgets/password_field.dart';
import 'widgets/signup_button.dart';
import 'widgets/user_type_selector.dart';

class SignupView extends GetView<AuthController> {
  const SignupView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = Get.locale?.languageCode == 'ar';

    return Scaffold(
      appBar: AppBar(
        // Provides a back button automatically
        title: Text(isArabic ? 'إنشاء حساب' : 'Create Account'),
        centerTitle: true,
        elevation: 0, // Cleaner look
        backgroundColor: Colors.transparent, // Blend with body
        foregroundColor: theme.colorScheme.onSurface, // Adapts to theme
        actions: const [AuthLanguageSelector()],
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: controller.signupFormKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Title and subtitle
                  Text(
                    isArabic ? 'لنبدأ!' : 'Let\'s Get Started!',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    isArabic
                        ? 'أدخل بياناتك أدناه'
                        : 'Enter your details below',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.secondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // --- Name Field ---
                  LanguageAwareTextField(
                    controller: controller.signupNameController,
                    labelTextEn: 'Full Name',
                    labelTextAr: 'الاسم الكامل',
                    prefixIcon: Icons.person_outline,
                    keyboardType: TextInputType.name,
                    textCapitalization: TextCapitalization.words,
                    validator: controller.validateName,
                  ),
                  const SizedBox(height: 16),

                  // --- Email Field ---
                  LanguageAwareTextField(
                    controller: controller.signupEmailController,
                    labelTextEn: 'Email',
                    labelTextAr: 'البريد الإلكتروني',
                    prefixIcon: Icons.email_outlined,
                    keyboardType: TextInputType.emailAddress,
                    validator: controller.validateEmail,
                  ),
                  const SizedBox(height: 16),

                  // --- Password Field ---
                  Obx(() => PasswordField(
                        controller: controller.signupPasswordController,
                        labelTextEn: 'Password',
                        labelTextAr: 'كلمة المرور',
                        isVisible: controller.isSignupPasswordVisible.value,
                        toggleVisibility:
                            controller.toggleSignupPasswordVisibility,
                        validator: controller.validatePassword,
                      )),
                  const SizedBox(height: 16),

                  // --- Confirm Password Field ---
                  Obx(() => PasswordField(
                        controller: controller.signupConfirmPasswordController,
                        labelTextEn: 'Confirm Password',
                        labelTextAr: 'تأكيد كلمة المرور',
                        isVisible:
                            controller.isSignupConfirmPasswordVisible.value,
                        toggleVisibility:
                            controller.toggleSignupConfirmPasswordVisibility,
                        validator: controller.validateConfirmPassword,
                      )),
                  const SizedBox(height: 16),

                  // --- User Type Selection ---
                  UserTypeSelector(controller: controller),
                  const SizedBox(height: 16),

                  // --- Car Information Fields (Only for Drivers) ---
                  Obx(() => controller.userType.value == 'driver'
                      ? CarInformationForm(controller: controller)
                      : const SizedBox.shrink()),
                  const SizedBox(height: 32),

                  // --- Signup Button ---
                  SignupButton(controller: controller),
                  const SizedBox(height: 24),

                  // --- Go to Login ---
                  LoginLink(controller: controller),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
