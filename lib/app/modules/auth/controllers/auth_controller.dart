import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';

class AuthController extends GetxController {
  // --- COMMON ---
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore =
      FirebaseFirestore.instance; // Firestore instance
  final GoogleSignIn _googleSignIn = GoogleSignIn(); // Google Sign-In instance

  final isLoading = false.obs; // Observable for loading state
  final errorMessage = RxnString(); // Observable for error messages

  // --- LOGIN SCREEN ---
  final loginFormKey = GlobalKey<FormState>();
  final loginEmailController = TextEditingController();
  final loginPasswordController = TextEditingController();
  final isLoginPasswordVisible = false.obs;

  // --- SIGNUP SCREEN ---
  final signupFormKey = GlobalKey<FormState>();
  final signupNameController = TextEditingController();
  final signupEmailController = TextEditingController();
  final signupPasswordController = TextEditingController();
  final signupConfirmPasswordController = TextEditingController();
  final isSignupPasswordVisible = false.obs;
  final isSignupConfirmPasswordVisible = false.obs;

  // --- USER TYPE SELECTION ---
  final userType = 'user'.obs; // Default to regular user

  // --- CAR INFORMATION (for drivers) ---
  final carMakeController = TextEditingController();
  final carModelController = TextEditingController();
  final carYearController = TextEditingController();
  final carPlateController =
      TextEditingController(); // For backward compatibility
  final carPlateLettersController =
      TextEditingController(); // For Arabic letters (3)
  final carPlateNumbersController = TextEditingController(); // For numbers (4)
  final carColorController = TextEditingController();

  // --- PHONE LOGIN ---
  final phoneFormKey = GlobalKey<FormState>();
  final dialCode = '+966'.obs;
  final phoneController = TextEditingController();
  final otpController = TextEditingController();
  final isCodeSent = false.obs; // Controls UI for OTP entry
  final RxnString verificationId =
      RxnString(); // Stores verification ID from Firebase
  int? resendToken; // Stores resend token for OT
  // --- LIFECYCLE ---
  @override
  void onClose() {
    // Dispose controllers to prevent memory leaks
    loginEmailController.dispose();
    loginPasswordController.dispose();
    signupNameController.dispose();
    signupEmailController.dispose();
    signupPasswordController.dispose();
    signupConfirmPasswordController.dispose();
    carMakeController.dispose();
    carModelController.dispose();
    carYearController.dispose();
    carPlateController.dispose();
    carPlateLettersController.dispose();
    carPlateNumbersController.dispose();
    carColorController.dispose();
    phoneController.dispose();
    otpController.dispose();
    super.onClose();
  }

  // --- METHODS ---

  void resetPhoneAuthState() {
    isLoading.value = false;
    isCodeSent.value = false;
    errorMessage.value = null;
    verificationId.value = null;
    otpController.clear();
    // Keep phone number? Optional: phoneController.clear();
  }

  // Login Password Visibility Toggle
  void toggleLoginPasswordVisibility() {
    isLoginPasswordVisible.value = !isLoginPasswordVisible.value;
    // You could add haptic feedback or a small animation trigger here
  }

  // Signup Password Visibility Toggle
  void toggleSignupPasswordVisibility() {
    isSignupPasswordVisible.value = !isSignupPasswordVisible.value;
  }

  // Signup Confirm Password Visibility Toggle
  void toggleSignupConfirmPasswordVisibility() {
    isSignupConfirmPasswordVisible.value =
        !isSignupConfirmPasswordVisible.value;
  }

  Future<void> login() async {
    if (!loginFormKey.currentState!.validate()) return;
    _clearError();
    _setLoading(true);

    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: loginEmailController.text.trim(),
        password: loginPasswordController.text.trim(),
      );

      if (credential.user != null) {
        // Save/Update data in Firestore (even on login)
        await _saveUserDataToFirestore(credential.user!);
        print('Email Login Successful: ${credential.user?.uid}');
        Get.offAllNamed(Routes.HOME); // Navigate to Home
      } else {
        throw FirebaseAuthException(
            code: 'user-not-found', message: 'Login failed. Please try again.');
      }
    } on FirebaseAuthException catch (e) {
      print('Login failed: ${e.code} - ${e.message}');
      errorMessage.value = _mapFirebaseErrorToMessage(e);
      Get.snackbar(
          'Login Failed', errorMessage.value ?? 'An unknown error occurred.');
    } catch (e) {
      print('Login failed (Generic): $e');
      errorMessage.value = 'An unexpected error occurred.';
      Get.snackbar('Login Failed', errorMessage.value!);
    } finally {
      _setLoading(false);
    }
  }

  // --- Signup Logic (Email/Password) ---
  Future<void> signup() async {
    if (!signupFormKey.currentState!.validate()) return;
    _clearError();
    _setLoading(true);

    // Additional validation for driver car information
    if (userType.value == 'driver') {
      // Validate car information fields
      if (carMakeController.text.isEmpty ||
          carModelController.text.isEmpty ||
          carYearController.text.isEmpty ||
          carColorController.text.isEmpty) {
        Get.snackbar(
          Get.locale?.languageCode == 'ar' ? 'خطأ' : 'Error',
          Get.locale?.languageCode == 'ar'
              ? 'الرجاء إكمال جميع حقول معلومات السيارة'
              : 'Please complete all car information fields',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        _setLoading(false);
        return;
      }

      // Validate Arabic letters in license plate
      if (carPlateLettersController.text.length != 3) {
        Get.snackbar(
          Get.locale?.languageCode == 'ar' ? 'خطأ' : 'Error',
          Get.locale?.languageCode == 'ar'
              ? 'يجب أن يحتوي رقم اللوحة على 3 أحرف عربية'
              : 'License plate must contain 3 Arabic letters',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        _setLoading(false);
        return;
      }

      // Validate numbers in license plate
      if (carPlateNumbersController.text.length != 4) {
        Get.snackbar(
          Get.locale?.languageCode == 'ar' ? 'خطأ' : 'Error',
          Get.locale?.languageCode == 'ar'
              ? 'يجب أن يحتوي رقم اللوحة على 4 أرقام'
              : 'License plate must contain 4 numbers',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        _setLoading(false);
        return;
      }

      // For backward compatibility, combine the plate parts
      carPlateController.text =
          '${carPlateNumbersController.text} ${carPlateLettersController.text}';
    }

    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: signupEmailController.text.trim(),
        password: signupPasswordController.text.trim(),
      );

      if (credential.user != null) {
        // Save new user data to Firestore
        await _saveUserDataToFirestore(credential.user!,
            name: signupNameController.text.trim() // Pass the name from form
            );
        print('Signup Successful: ${credential.user?.uid}');

        // Redirect based on user type
        if (userType.value == 'driver') {
          // Redirect drivers to under review screen
          Get.offAllNamed(Routes.UNDER_REVIEW);
        } else {
          // Regular users go to home
          Get.offAllNamed(Routes.HOME);
        }

        // Option 2: Go back to Login (less common now, but possible)
        // Get.back(); // Go back from signup screen
        // Get.snackbar('Signup Successful', 'Please log in to continue.');
      } else {
        throw FirebaseAuthException(
            code: 'user-creation-failed', message: 'Could not create user.');
      }
    } on FirebaseAuthException catch (e) {
      print('Signup failed: ${e.code} - ${e.message}');
      errorMessage.value = _mapFirebaseErrorToMessage(e);
      Get.snackbar(
          Get.locale?.languageCode == 'ar' ? 'فشل التسجيل' : 'Signup Failed',
          errorMessage.value ??
              (Get.locale?.languageCode == 'ar'
                  ? 'حدث خطأ غير معروف'
                  : 'An unknown error occurred.'));
    } catch (e) {
      print('Signup failed (Generic): $e');
      errorMessage.value = Get.locale?.languageCode == 'ar'
          ? 'حدث خطأ غير متوقع'
          : 'An unexpected error occurred.';
      Get.snackbar(
          Get.locale?.languageCode == 'ar' ? 'فشل التسجيل' : 'Signup Failed',
          errorMessage.value!);
    } finally {
      _setLoading(false);
    }
  }

  // --- Google Login Logic ---
  Future<void> loginWithGoogle() async {
    _clearError();
    _setLoading(true);

    try {
      // Trigger the Google authentication flow.
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      // If the user cancelled the sign-in
      if (googleUser == null) {
        _setLoading(false);
        return;
      }

      // Obtain the auth details from the request.
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential for Firebase.
      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential.
      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        // Save/Update data in Firestore
        await _saveUserDataToFirestore(userCredential.user!);
        print('Google Sign-In Successful: ${userCredential.user?.uid}');
        Get.offAllNamed(Routes.HOME); // Navigate to Home
      } else {
        throw FirebaseAuthException(
            code: 'google-signin-failed',
            message: 'Could not sign in with Google.');
      }
    } on FirebaseAuthException catch (e) {
      print('Google Login failed: ${e.code} - ${e.message}');
      errorMessage.value = _mapFirebaseErrorToMessage(e);
      Get.snackbar('Google Login Failed',
          errorMessage.value ?? 'An unknown error occurred.');
    } catch (e) {
      print('Google Login failed (Generic): $e');
      errorMessage.value = 'An unexpected error occurred.';
      Get.snackbar('Google Login Failed', errorMessage.value!);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loginWithApple() async {
    if (GetPlatform.isIOS || GetPlatform.isMacOS) {
      // Apple Sign In is typically only for Apple platforms
      isLoading.value = true;
      print('Attempting Apple Login...');
      // **--- PLACEHOLDER: Apple Sign-In Logic ---**
      await Future.delayed(const Duration(seconds: 2));
      try {
        // Simulate success
        Get.snackbar('Success', 'Logged in with Apple (Simulated)');
        // Get.offAllNamed(Routes.HOME); // Navigate on success
        Get.offAllNamed(
            Routes.INITIAL); // Replace with your actual home/dashboard route
      } catch (e) {
        Get.snackbar('Error', 'Apple Login Failed (Simulated)');
      } finally {
        isLoading.value = false;
      }
    } else {
      Get.snackbar('Error', 'Apple Sign In is not available on this platform.');
    }
  }

  // --- Navigation ---
  void goToSignup() {
    // Add a transition animation here if desired
    Get.toNamed(Routes.SIGNUP);
  }

  void goToLogin() {
    // Add a transition animation here if desired
    // Usually you go *back* to login from signup
    if (Get.previousRoute == Routes.SIGNUP) {
      Get.back();
    } else {
      // If coming from somewhere else, navigate directly
      Get.offNamed(Routes.LOGIN); // Use offNamed if replacing splash/intro
    }
  }

  // --- User Type Selection ---
  void setUserType(String type) {
    userType.value = type;
  }

  // --- Validation Helpers (Example - can be expanded) ---
  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your email';
    }
    if (!GetUtils.isEmail(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your password';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    // Add more complex rules if needed (uppercase, number, symbol)
    return null;
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your name';
    }
    if (value.length < 3) {
      return 'Name seems too short';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != signupPasswordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  // --- Car Information Validation ---
  String? validateCarMake(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return 'Please enter car make';
    }
    return null;
  }

  String? validateCarModel(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return 'Please enter car model';
    }
    return null;
  }

  String? validateCarYear(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return 'Please enter car year';
    }
    try {
      int year = int.parse(value);
      if (year < 1990 || year > DateTime.now().year + 1) {
        return 'Please enter a valid year';
      }
    } catch (e) {
      return 'Please enter a valid year';
    }
    return null;
  }

  String? validateCarPlate(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return Get.locale?.languageCode == 'ar'
          ? 'الرجاء إدخال رقم اللوحة'
          : 'Please enter license plate number';
    }
    return null;
  }

  // Validate Arabic letters part of the license plate
  String? validateCarPlateLetters(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return Get.locale?.languageCode == 'ar' ? 'مطلوب' : 'Required';
    }
    if (value.length < 3) {
      return Get.locale?.languageCode == 'ar' ? '3 أحرف' : '3 letters';
    }
    // Check if all characters are Arabic
    final arabicRegex = RegExp(r'^[\u0600-\u06FF]+$');
    if (!arabicRegex.hasMatch(value)) {
      return Get.locale?.languageCode == 'ar'
          ? 'أحرف عربية فقط'
          : 'Arabic only';
    }
    return null;
  }

  // Validate numbers part of the license plate
  String? validateCarPlateNumbers(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return Get.locale?.languageCode == 'ar' ? 'مطلوب' : 'Required';
    }
    if (value.length < 4) {
      return Get.locale?.languageCode == 'ar' ? '4 أرقام' : '4 digits';
    }
    // Check if all characters are digits
    final digitsRegex = RegExp(r'^\d+$');
    if (!digitsRegex.hasMatch(value)) {
      return Get.locale?.languageCode == 'ar' ? 'أرقام فقط' : 'Numbers only';
    }
    return null;
  }

  String? validateCarColor(String? value) {
    if (userType.value != 'driver') return null;
    if (value == null || value.isEmpty) {
      return 'Please enter car color';
    }
    return null;
  }

  // --- Phone Verification Logic ---
  Future<void> verifyPhoneNumber() async {
    if (!phoneFormKey.currentState!.validate()) return;

    isLoading.value = true;
    errorMessage.value = null;

    // The phone number is now handled by IntlPhoneField
    // The controller already contains the full number with country code but without '+'
    String phoneNumber = '${dialCode.value}${phoneController.text.trim()}';

    // Log the phone number for debugging (remove in production)
    // print('Verifying phone number: $phoneNumber');

    try {
      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: _handleVerificationCompleted,
        verificationFailed: _handleVerificationFailed,
        codeSent: _handleCodeSent,
        codeAutoRetrievalTimeout: _handleCodeAutoRetrievalTimeout,
        timeout: const Duration(seconds: 60), // Adjust timeout
        forceResendingToken: resendToken, // For resending OTP
      );
      // Note: No need to set isLoading false immediately here,
      // it will be handled by the callbacks (codeSent, failed, completed)
      print('OTP verification initiated for $phoneNumber');
    } catch (e) {
      print('Error initiating phone verification: $e');
      errorMessage.value = 'Error: ${e.toString()}';
      isLoading.value = false; // Set loading false on initial catch
    }
  }

  // --- Firebase Callbacks ---
  void _handleVerificationCompleted(PhoneAuthCredential credential) async {
    print("Verification Completed Automatically");
    isLoading.value = true; // Show loading for sign-in attempt
    errorMessage.value = null;
    await _signInWithCredential(credential);
  }

  void _handleVerificationFailed(FirebaseAuthException e) {
    print("Verification Failed: ${e.code} - ${e.message}");
    isLoading.value = false;
    isCodeSent.value = false; // Go back to phone entry UI if needed
    errorMessage.value = _mapFirebaseErrorToMessage(e); // User-friendly error
    Get.snackbar('Verification Failed',
        errorMessage.value ?? 'An unknown error occurred.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white);
  }

  void _handleCodeSent(String verId, int? forceResendToken) {
    print(
        "Code Sent: Verification ID: $verId, Resend Token: $forceResendToken");
    isLoading.value = false; // Stop initial loading
    isCodeSent.value = true; // Show OTP input UI
    verificationId.value = verId;
    resendToken = forceResendToken;
    errorMessage.value = null; // Clear previous errors
    Get.snackbar('OTP Sent', 'Enter the code sent to your phone.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white);
  }

  void _handleCodeAutoRetrievalTimeout(String verId) {
    print("Code Auto Retrieval Timeout: Verification ID: $verId");
    // Optionally update UI or inform user if needed
    // If OTP screen is still shown, they need to manually enter
    verificationId.value =
        verId; // Update verificationId in case it's needed for manual entry
    if (isCodeSent.value) {
      // Only show timeout message if user is waiting for code
      errorMessage.value = "OTP timed out. Please try sending again.";
    }
    // Don't automatically set isLoading false, might be waiting for manual entry
  }

  // --- Sign in with OTP ---
  Future<void> signInWithOtp() async {
    if (verificationId.value == null || verificationId.value!.isEmpty) {
      errorMessage.value =
          'Verification ID not found. Please request OTP first.';
      return;
    }
    if (otpController.text.trim().isEmpty ||
        otpController.text.trim().length != 6) {
      errorMessage.value = 'Please enter a valid 6-digit OTP.';
      // Optionally trigger validation UI on the OTP field
      return;
    }

    isLoading.value = true;
    errorMessage.value = null;

    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId.value!,
        smsCode: otpController.text.trim(),
      );
      await _signInWithCredential(credential);
    } on FirebaseAuthException catch (e) {
      print("OTP Sign In Failed: ${e.code} - ${e.message}");
      isLoading.value = false;
      errorMessage.value = _mapFirebaseErrorToMessage(e); // User-friendly error
      Get.snackbar(
          'Sign In Failed', errorMessage.value ?? 'An unknown error occurred.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
    } catch (e) {
      print('Error signing in with OTP: $e');
      errorMessage.value = 'An unexpected error occurred during sign in.';
      isLoading.value = false;
      Get.snackbar(
          'Sign In Failed', errorMessage.value ?? 'An unknown error occurred.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
    }
  }

  void _clearError() {
    errorMessage.value = null;
  }

  void _setLoading(bool value) {
    isLoading.value = value;
  }

  // --- Firestore User Data Handling ---
  Future<void> _saveUserDataToFirestore(User user,
      {String? name, String? phoneNumber}) async {
    try {
      final userDocRef = _firestore.collection('users').doc(user.uid);

      // Basic user data
      final userData = {
        'uid': user.uid,
        'email': user.email,
        'role': userType.value, // Use selected user type
        'name': name ??
            user.displayName, // Use provided name or Google/Apple display name
        'phoneNumber': phoneNumber ??
            user.phoneNumber, // Use provided or Firebase user phone
        'createdAt':
            FieldValue.serverTimestamp(), // Timestamp of creation/first save
        'lastLogin':
            FieldValue.serverTimestamp(), // Update on every login/signup
      };

      // Add car information for drivers
      if (userType.value == 'driver') {
        // Set status to 'underReview' for new driver accounts
        userData['status'] = 'underReview';
        userData['statusReason'] =
            'Your driver account is under review by our team.';

        // Add car information
        userData['carInfo'] = {
          'make': carMakeController.text.trim(),
          'model': carModelController.text.trim(),
          'year': carYearController.text.trim(),
          'plate': carPlateController.text.trim(), // For backward compatibility
          'plateLetters':
              carPlateLettersController.text.trim(), // Arabic letters
          'plateNumbers': carPlateNumbersController.text.trim(), // Numbers
          'plateFormatted':
              '${carPlateNumbersController.text.trim()} ${carPlateLettersController.text.trim()}', // Formatted plate
          'color': carColorController.text.trim(),
        };
      }

      // Use set with merge: true to create or update, avoiding overwriting existing fields
      await userDocRef.set(userData, SetOptions(merge: true));
      print('User data saved/updated in Firestore for UID: ${user.uid}');
    } catch (e) {
      print("Error saving user data to Firestore: $e");
      // Handle error appropriately, maybe show a snackbar
      Get.snackbar('Firestore Error', 'Could not save user profile.');
    }
  }

  Future<void> _signInWithCredential(AuthCredential credential) async {
    try {
      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      print(
          'Credential Sign In Successful: User UID: ${userCredential.user?.uid}');

      if (userCredential.user != null) {
        // Determine if it's phone auth to pass phone number explicitly if needed
        String? phoneNumber;
        if (credential.providerId == PhoneAuthProvider.PROVIDER_ID) {
          phoneNumber =
              userCredential.user?.phoneNumber; // Get phone from user object
          print("Saving phone number from phone auth: $phoneNumber");
        }
        // Save/Update data in Firestore
        await _saveUserDataToFirestore(userCredential.user!,
            phoneNumber: phoneNumber);

        // Reset state and navigate
        resetPhoneAuthState(); // If you have phone auth state
        Get.offAllNamed(Routes.HOME); // Navigate to home/dashboard
        Get.snackbar('Login Successful', 'Welcome!');
      } else {
        throw FirebaseAuthException(
            code: 'signin-failed',
            message: 'Sign in failed after verification.');
      }
    } on FirebaseAuthException catch (e) {
      // ... existing error handling ...
      isLoading.value = false;
      errorMessage.value = _mapFirebaseErrorToMessage(e);
      Get.snackbar(
          'Sign In Failed', errorMessage.value ?? 'An unknown error occurred.');
    } catch (e) {
      // ... existing error handling ...
      isLoading.value = false;
      errorMessage.value = 'An unexpected error occurred.';
      Get.snackbar(
          'Sign In Failed', errorMessage.value ?? 'An unknown error occurred.');
    }
    // No finally isLoading=false here, handled in specific paths
  }

  // --- Apple Login Placeholder (If Keeping) ---
  // Modify similarly to Google Login if you implement it fully. Remember to
  // call _saveUserDataToFirestore on success.

  void goToPhoneLogin() {
    // If keeping
    _clearError();
    resetPhoneAuthState();
    Get.toNamed(Routes.PHONE_LOGIN)?.then((_) => _clearError());
  }

  // --- Firebase Error Mapping Helper ---
  String _mapFirebaseErrorToMessage(FirebaseAuthException e) {
    // Keep existing cases and add/refine as needed
    switch (e.code) {
      case 'user-not-found':
      case 'wrong-password':
        return 'Invalid email or password.';
      case 'invalid-email':
        return 'The email address is badly formatted.';
      case 'email-already-in-use':
        return 'This email is already registered. Please login.';
      case 'weak-password':
        return 'The password is too weak.';
      case 'requires-recent-login':
        return 'This operation requires re-authenticating. Please log out and log back in.';
      // Add Google specific errors if known
      case 'invalid-phone-number':
        return 'The phone number provided is not valid.';
      case 'quota-exceeded':
        return 'SMS quota exceeded. Please try again later.';
      case 'invalid-verification-code':
        return 'The OTP code entered is invalid.';
      case 'session-expired':
        return 'The OTP code has expired. Please request a new one.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.';
      // ... other cases from phone auth ...
      default:
        // return e.message ?? 'An unknown authentication error occurred.';
        print(
            "Unhandled Firebase Auth Error Code: ${e.code}"); // Log unhandled codes
        return 'An authentication error occurred. Please try again.'; // Generic message for unhandled
    }
  }

  // --- Validation Helpers ---
  // ... (validateEmail, validatePassword, etc.) ...

  String? validatePhoneNumber(String? value) {
    // The IntlPhoneField widget handles validation, so this is just a fallback
    if (value == null || value.isEmpty) {
      return 'Please enter your phone number';
    }
    // We're now using IntlPhoneField which handles validation
    // This is just a basic check for non-empty
    return null;
  }

  String? validateOtp(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter the OTP';
    }
    if (value.length != 6 || !GetUtils.isNumericOnly(value)) {
      return 'Please enter a valid 6-digit OTP';
    }
    return null;
  }

  // --- Navigation ---

  void goBackFromPhoneLogin() {
    resetPhoneAuthState(); // Clear state when leaving
    Get.back();
  }
}
