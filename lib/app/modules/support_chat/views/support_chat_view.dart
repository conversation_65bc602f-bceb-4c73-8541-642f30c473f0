import 'package:ai_delivery_app/app/data/models/support_chat_model.dart';
import 'package:ai_delivery_app/app/modules/support_chat/controllers/support_chat_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class SupportChatView extends GetView<SupportChatController> {
  const SupportChatView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الدعم الفني'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.info_outline),
              onPressed: () => controller.showSupportInfo(),
              tooltip: 'معلومات الدعم',
            ),
          ],
        ),
        body: Column(
          children: [
            // Support Status Banner
            Obx(() => _buildStatusBanner(context)),

            // Chat Messages
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.errorMessage.value != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline,
                            size: 48, color: theme.colorScheme.error),
                        const SizedBox(height: 16),
                        Text(
                          controller.errorMessage.value!,
                          style: theme.textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: controller.loadMessages,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                if (controller.messages.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.support_agent,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'ابدأ محادثة مع فريق الدعم',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'سنقوم بالرد في أقرب وقت ممكن',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  reverse: true,
                  itemCount: controller.messages.length,
                  itemBuilder: (context, index) {
                    final message = controller.messages[index];
                    final isUser = message.sender == SupportMessageSender.user;

                    // Check if we need to show date header
                    final showDateHeader =
                        index == controller.messages.length - 1 ||
                            !_isSameDay(
                              message.timestamp.toDate(),
                              controller.messages[index + 1].timestamp.toDate(),
                            );

                    return Column(
                      children: [
                        if (showDateHeader)
                          _buildDateHeader(context, message.timestamp.toDate()),
                        _buildMessageBubble(context, message, isUser),
                      ],
                    );
                  },
                );
              }),
            ),

            // Message Input
            _buildMessageInput(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBanner(BuildContext context) {
    final theme = Theme.of(context);
    final ticketStatus = controller.currentTicket.value?.status ?? 'new';

    Color bannerColor;
    String statusText;
    IconData statusIcon;

    switch (ticketStatus) {
      case 'new':
        bannerColor = Colors.blue;
        statusText = 'تم فتح تذكرة جديدة';
        statusIcon = Icons.fiber_new;
        break;
      case 'open':
        bannerColor = Colors.green;
        statusText = 'المحادثة نشطة';
        statusIcon = Icons.chat;
        break;
      case 'pending':
        bannerColor = Colors.orange;
        statusText = 'في انتظار ردك';
        statusIcon = Icons.hourglass_empty;
        break;
      case 'resolved':
        bannerColor = Colors.grey;
        statusText = 'تم حل المشكلة';
        statusIcon = Icons.check_circle;
        break;
      case 'closed':
        bannerColor = Colors.grey.shade700;
        statusText = 'تم إغلاق التذكرة';
        statusIcon = Icons.cancel;
        break;
      default:
        bannerColor = Colors.blue;
        statusText = 'محادثة الدعم';
        statusIcon = Icons.support_agent;
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      color: bannerColor.withOpacity(0.1),
      child: Row(
        children: [
          Icon(statusIcon, color: bannerColor, size: 20),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: bannerColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (ticketStatus == 'resolved' || ticketStatus == 'closed')
            TextButton(
              onPressed: () => controller.createNewTicket(),
              child: const Text('فتح تذكرة جديدة'),
            ),
        ],
      ),
    );
  }

  Widget _buildDateHeader(BuildContext context, DateTime date) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(child: Divider(color: Colors.grey.shade300)),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              _formatDateHeader(date),
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(child: Divider(color: Colors.grey.shade300)),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(
      BuildContext context, SupportChatMessage message, bool isUser) {
    final theme = Theme.of(context);

    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isUser ? theme.colorScheme.primary : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message Content
            Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                message.message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isUser
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface,
                ),
              ),
            ),

            // Message Timestamp
            Padding(
              padding: const EdgeInsets.only(right: 12, left: 12, bottom: 8),
              child: Text(
                _formatTime(message.timestamp.toDate()),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isUser
                      ? theme.colorScheme.onPrimary.withOpacity(0.7)
                      : Colors.grey.shade600,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Attachment Button
          IconButton(
            icon: const Icon(Icons.attach_file),
            onPressed: () => controller.showAttachmentOptions(),
            color: theme.colorScheme.primary,
          ),

          // Text Input
          Expanded(
            child: TextField(
              controller: controller.messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => controller.sendMessage(),
              maxLines: null,
            ),
          ),

          const SizedBox(width: 8),

          // Send Button
          Obx(() => CircleAvatar(
                backgroundColor: theme.colorScheme.primary,
                radius: 24,
                child: IconButton(
                  icon: controller.isSending.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Icon(Icons.send, color: Colors.white),
                  onPressed: controller.isSending.value
                      ? null
                      : controller.sendMessage,
                ),
              )),
        ],
      ),
    );
  }

  String _formatTime(DateTime date) {
    return intl.DateFormat('HH:mm').format(date);
  }

  String _formatDateHeader(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == DateTime(now.year, now.month, now.day)) {
      return 'اليوم';
    } else if (dateToCheck ==
        DateTime(yesterday.year, yesterday.month, yesterday.day)) {
      return 'الأمس';
    } else {
      return intl.DateFormat('dd/MM/yyyy').format(date);
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
