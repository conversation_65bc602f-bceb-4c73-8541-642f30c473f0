import 'dart:async';

import 'package:ai_delivery_app/app/data/models/support_chat_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';

class SupportChatController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  final TextEditingController messageController = TextEditingController();
  final RxList<SupportChatMessage> messages = <SupportChatMessage>[].obs;
  final Rx<SupportTicket?> currentTicket = Rx<SupportTicket?>(null);
  final RxBool isLoading = false.obs;
  final RxBool isSending = true.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  User? get currentUser => _auth.currentUser;

  StreamSubscription<QuerySnapshot>? _messagesSubscription;
  StreamSubscription<DocumentSnapshot>? _ticketSubscription;

  @override
  void onInit() {
    super.onInit();
    loadActiveTicket();
  }

  @override
  void onClose() {
    messageController.dispose();
    _messagesSubscription?.cancel();
    _ticketSubscription?.cancel();
    super.onClose();
  }

  Future<void> loadActiveTicket() async {
    if (currentUser == null) {
      errorMessage.value = 'يرجى تسجيل الدخول أولاً';
      return;
    }

    isLoading.value = true;
    errorMessage.value = null;

    try {
      // Find the most recent active ticket for this user
      final ticketsSnapshot = await _firestore
          .collection('supportTickets')
          .where('userId', isEqualTo: currentUser!.uid)
          .where('status', whereIn: ['new', 'open', 'pending'])
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (ticketsSnapshot.docs.isNotEmpty) {
        final ticketDoc = ticketsSnapshot.docs.first;
        currentTicket.value = SupportTicket.fromFirestore(ticketDoc);
        _listenToTicketChanges(ticketDoc.id);
        loadMessages();
      } else {
        // Check if there's a resolved or closed ticket
        final closedTicketsSnapshot = await _firestore
            .collection('supportTickets')
            .where('userId', isEqualTo: currentUser!.uid)
            .orderBy('createdAt', descending: true)
            .limit(1)
            .get();

        if (closedTicketsSnapshot.docs.isNotEmpty) {
          final ticketDoc = closedTicketsSnapshot.docs.first;
          currentTicket.value = SupportTicket.fromFirestore(ticketDoc);
          _listenToTicketChanges(ticketDoc.id);
          loadMessages();
        } else {
          // No tickets found, create a new one
          createNewTicket();
        }
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل بيانات التذكرة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  void _listenToTicketChanges(String ticketId) {
    _ticketSubscription?.cancel();

    _ticketSubscription = _firestore
        .collection('supportTickets')
        .doc(ticketId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        currentTicket.value = SupportTicket.fromFirestore(snapshot);
      }
    }, onError: (e) {
      errorMessage.value = 'حدث خطأ أثناء متابعة التذكرة: $e';
    });
  }

  Future<void> loadMessages() async {
    if (currentTicket.value == null || currentUser == null) {
      return;
    }

    isLoading.value = true;
    errorMessage.value = null;

    try {
      _messagesSubscription?.cancel();

      _messagesSubscription = _firestore
          .collection('supportMessages')
          .where('ticketId', isEqualTo: currentTicket.value!.id)
          .orderBy('timestamp', descending: true)
          .snapshots()
          .listen((snapshot) {
        messages.value = snapshot.docs
            .map((doc) => SupportChatMessage.fromFirestore(doc))
            .toList();
      }, onError: (e) {
        errorMessage.value = 'حدث خطأ أثناء تحميل الرسائل: $e';
      });
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الرسائل: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createNewTicket() async {
    if (currentUser == null) {
      errorMessage.value = 'يرجى تسجيل الدخول أولاً';
      return;
    }

    isLoading.value = true;

    try {
      // Create a new ticket
      final ticket = SupportTicket(
        id: '',
        userId: currentUser!.uid,
        subject: 'استفسار جديد',
        status: 'open',
        priority: 2, // medium priority
        createdAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
        assignedTo: null,
      );

      // Add to Firestore
      final docRef = await _firestore.collection('supportTickets').add(
            ticket.toFirestore(),
          );

      // Update with the generated ID
      final newTicket = ticket.copyWith(id: docRef.id);
      await docRef.update({'id': docRef.id});

      // Set as current ticket
      currentTicket.value = newTicket;

      // Listen to changes
      _listenToTicketChanges(docRef.id);

      // Clear messages
      messages.clear();

      // Add system message
      await _addSystemMessage(
        'مرحبًا بك في خدمة الدعم الفني. كيف يمكننا مساعدتك؟',
        docRef.id,
      );

      // Load messages
      loadMessages();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء تذكرة جديدة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _addSystemMessage(String content, String ticketId) async {
    try {
      final message = SupportChatMessage(
        id: '',
        userId: 'system',
        message: content,
        sender: SupportMessageSender.support,
        timestamp: Timestamp.now(),
        isRead: false,
        ticketId: ticketId,
      );

      await _firestore.collection('supportMessages').add(
            message.toFirestore(),
          );
    } catch (e) {
      // Use Get.log instead of print
      Get.log('Error adding system message: $e');
    }
  }

  Future<void> sendMessage() async {
    final content = messageController.text.trim();
    if (content.isEmpty || currentTicket.value == null || currentUser == null) {
      Get.snackbar('خطأ', 'لا يمكن أرسال رسالة فارغة');
      return;
    }

    isSending.value = true;

    try {
      // Create message
      final message = SupportChatMessage(
        id: '',
        userId: currentUser!.uid,
        message: content,
        sender: SupportMessageSender.user,
        timestamp: Timestamp.now(),
        isRead: false,
        ticketId: currentTicket.value!.id,
      );

      // Add to Firestore
      await _firestore.collection('supportMessages').add(
            message.toFirestore(),
          );

      // Update ticket status if it was 'resolved' or 'closed'
      if (currentTicket.value!.status == 'resolved' ||
          currentTicket.value!.status == 'closed') {
        await _firestore
            .collection('supportTickets')
            .doc(currentTicket.value!.id)
            .update({
          'status': 'open',
          'updatedAt': Timestamp.now(),
        });
      }

      // Clear input
      messageController.clear();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إرسال الرسالة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isSending.value = false;
    }
  }

  void showAttachmentOptions() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('اختيار من المعرض'),
                onTap: () {
                  Get.back();
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('التقاط صورة'),
                onTap: () {
                  Get.back();
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.insert_drive_file),
                title: const Text('إرفاق ملف'),
                onTap: () {
                  Get.back();
                  // TODO: Implement file picking
                  Get.snackbar(
                    'قريبًا',
                    'سيتم إضافة هذه الميزة قريبًا',
                    backgroundColor: Colors.orange,
                    colorText: Colors.white,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        _uploadImage(File(image.path));
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الصورة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _uploadImage(File imageFile) async {
    if (currentTicket.value == null || currentUser == null) {
      return;
    }

    isSending.value = true;

    try {
      // Upload to Firebase Storage
      final storageRef = _storage.ref().child(
            'support_attachments/${currentTicket.value!.id}/${DateTime.now().millisecondsSinceEpoch}.jpg',
          );

      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Create message with attachment
      final message = SupportChatMessage(
        id: '',
        userId: currentUser!.uid,
        message: 'صورة مرفقة',
        sender: SupportMessageSender.user,
        timestamp: Timestamp.now(),
        isRead: false,
        attachmentUrl: downloadUrl,
        ticketId: currentTicket.value!.id,
      );

      // Add to Firestore
      await _firestore.collection('supportMessages').add(
            message.toFirestore(),
          );

      // Update ticket status if it was 'resolved' or 'closed'
      if (currentTicket.value!.status == 'resolved' ||
          currentTicket.value!.status == 'closed') {
        await _firestore
            .collection('supportTickets')
            .doc(currentTicket.value!.id)
            .update({
          'status': 'open',
          'updatedAt': Timestamp.now(),
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء رفع الصورة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isSending.value = false;
    }
  }

  void showSupportInfo() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('معلومات الدعم الفني'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'ساعات العمل:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('من الأحد إلى الخميس: 9 صباحًا - 5 مساءً'),
              const Text('الجمعة والسبت: 10 صباحًا - 2 ظهرًا'),
              const SizedBox(height: 16),
              const Text(
                'وقت الاستجابة المتوقع:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('خلال ساعات العمل: 1-2 ساعة'),
              const Text('خارج ساعات العمل: 24 ساعة'),
              const SizedBox(height: 16),
              const Text(
                'للحالات الطارئة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('الاتصال على: 920001234'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      ),
    );
  }

  clearError() {}

  openAttachment(String s) {}
}
