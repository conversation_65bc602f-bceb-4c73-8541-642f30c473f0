import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import '../../../routes/app_pages.dart';
import '../../../services/app_settings_service.dart';

class SplashController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AppSettingsService _appSettingsService = Get.find<AppSettingsService>();
  
  final RxString loadingMessage = 'جاري تحميل التطبيق...'.obs;
  
  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }
  
  Future<void> _initializeApp() async {
    try {
      // Wait for app settings to load
      await _appSettingsService.loadAppSettings();
      
      // Check if app is in maintenance mode
      if (_appSettingsService.isInMaintenanceMode) {
        loadingMessage.value = 'التطبيق قيد الصيانة';
        await Future.delayed(const Duration(seconds: 2));
        _appSettingsService.showMaintenanceDialog();
        return;
      }
      
      // Check if app update is required
      if (_appSettingsService.isUpdateRequired) {
        loadingMessage.value = 'يوجد تحديث جديد للتطبيق';
        await Future.delayed(const Duration(seconds: 2));
        _appSettingsService.showUpdateDialog();
        return;
      }
      
      // Determine initial route based on auth state
      loadingMessage.value = 'جاري التحقق من حالة تسجيل الدخول...';
      
      // Check if user is logged in
      User? firebaseUser = _auth.currentUser;
      if (firebaseUser != null) {
        loadingMessage.value = 'جاري تحميل بيانات المستخدم...';
        
        // Get user data from Firestore
        DocumentSnapshot userDoc = await _firestore
            .collection('users')
            .doc(firebaseUser.uid)
            .get();
        
        if (userDoc.exists) {
          String userRole = userDoc.get('role') ?? 'user';
          
          // Check user status
          String? userStatus = userDoc.get('status');
          if (userStatus == 'blocked') {
            Get.offAllNamed(Routes.BLOCKED_USER);
            return;
          } else if (userStatus == 'suspended') {
            Get.offAllNamed(Routes.SUSPENDED_USER);
            return;
          } else if (userStatus == 'underReview' && userRole == 'driver') {
            Get.offAllNamed(Routes.UNDER_REVIEW);
            return;
          }
          
          // Navigate based on user role
          if (userRole == 'driver') {
            Get.offAllNamed(Routes.DRIVER_HOME);
          } else if (userRole == 'support') {
            Get.offAllNamed(Routes.SUPPORT_DASHBOARD);
          } else if (userRole == 'admin') {
            Get.offAllNamed(Routes.ADMIN_DASHBOARD);
          } else {
            Get.offAllNamed(Routes.HOME);
          }
        } else {
          // User document doesn't exist
          Get.offAllNamed(Routes.HOME);
        }
      } else {
        // User not logged in
        Get.offAllNamed(Routes.LOGIN);
      }
    } catch (e) {
      print('Error initializing app: $e');
      loadingMessage.value = 'حدث خطأ أثناء تحميل التطبيق';
      await Future.delayed(const Duration(seconds: 2));
      Get.offAllNamed(Routes.LOGIN);
    }
  }
}
