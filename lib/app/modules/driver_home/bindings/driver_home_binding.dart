import 'package:get/get.dart';

import '../../home/<USER>/home_controller.dart';
import '../controllers/driver_chat_controller.dart';
import '../controllers/driver_home_controller.dart';

class DriverHomeBinding extends Bindings {
  @override
  void dependencies() {
    // Use lazyPut to create the controller only when the DriverHomeView is navigated to
    Get.lazyPut<DriverHomeController>(
      () => DriverHomeController(),
    );
    Get.lazyPut<HomeController>(
          () => HomeController(),
    );
    // Add the chat controller
    Get.lazyPut<DriverChatController>(
      () => DriverChatController(),
    );
  }
}
