import 'dart:async';
import 'package:ai_delivery_app/app/data/models/trip_request_model.dart';
import 'package:ai_delivery_app/app/widgets/modern_trip_cancellation_dialog.dart';
import 'package:ai_delivery_app/app/widgets/modern_trip_rating_dialog.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DriverTripController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  final Rxn<TripRequest> currentTrip = Rxn<TripRequest>();
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  StreamSubscription? _tripSubscription;

  @override
  void onClose() {
    _tripSubscription?.cancel();
    super.onClose();
  }

  // Show cancellation dialog with reasons
  void showCancellationDialog() {
    if (currentTrip.value == null) return;

    Get.dialog(
      ModernTripCancellationDialog(
        onCancellationConfirmed: (reason) {
          cancelTrip(reason, true); // true for driver
        },
        isDriver: true,
      ),
      barrierDismissible: false,
    );
  }

  // Cancel trip with reason
  Future<void> cancelTrip(String reason, bool isDriver) async {
    if (currentTrip.value == null || isLoading.value) return;

    final tripId = currentTrip.value!.id;
    isLoading.value = true;
    errorMessage.value = null;

    try {
      // Update trip status in Firestore
      await _firestore.collection('tripRequests').doc(tripId).update({
        'status': TripRequest.statusToString(TripStatus.cancelledByDriver),
        'cancellationReason': reason,
        'cancelledAt': FieldValue.serverTimestamp(),
        'cancelledBy': _auth.currentUser?.uid,
      });

      // Update local trip status
      currentTrip.value?.status = TripStatus.cancelledByDriver;

      // Show success message
      Get.snackbar(
        'تم الإلغاء',
        'تم إلغاء الرحلة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Navigate back to home
      Get.back();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إلغاء الرحلة: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إلغاء الرحلة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Show rating dialog when trip is completed
  void showRatingDialog() {
    if (currentTrip.value == null ||
        currentTrip.value?.status != TripStatus.completed) return;

    final userId = currentTrip.value?.userId;
    final userName = currentTrip.value?.userName ?? 'الراكب';
    final userPhoto = ''; // Will be updated when user photos are implemented

    Get.dialog(
      ModernTripRatingDialog(
        name: userName,
        photoUrl: userPhoto,
        onRatingSubmitted: (rating, comment) {
          submitRating(userId!, rating, comment, true);
        },
        isDriver: true,
      ),
      barrierDismissible: false,
    );
  }

  // Submit rating to Firestore
  Future<void> submitRating(
      String ratedUserId, double rating, String? comment, bool isDriver) async {
    if (currentTrip.value == null || isLoading.value) return;

    final tripId = currentTrip.value!.id;
    isLoading.value = true;
    errorMessage.value = null;

    try {
      // Create rating document
      await _firestore.collection('ratings').add({
        'tripId': tripId,
        'ratedByUserId': _auth.currentUser?.uid,
        'ratedUserId': ratedUserId,
        'rating': rating,
        'comment': comment,
        'timestamp': FieldValue.serverTimestamp(),
        'type': 'driver_to_user',
      });

      // Update user's average rating
      final userDoc =
          await _firestore.collection('users').doc(ratedUserId).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final currentRating = (userData['rating'] as num?)?.toDouble() ?? 0.0;
        final ratingCount = (userData['ratingCount'] as num?)?.toInt() ?? 0;

        // Calculate new average rating
        final newRatingCount = ratingCount + 1;
        final newRating =
            ((currentRating * ratingCount) + rating) / newRatingCount;

        // Update user document
        await _firestore.collection('users').doc(ratedUserId).update({
          'rating': newRating,
          'ratingCount': newRatingCount,
        });
      }

      // Show success message
      Get.snackbar(
        'تم التقييم',
        'شكراً على تقييمك',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إرسال التقييم: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إرسال التقييم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Listen to trip updates
  void listenToTrip(String tripId) {
    _tripSubscription?.cancel();

    _tripSubscription = _firestore
        .collection('tripRequests')
        .doc(tripId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists) {
        final updatedTrip = TripRequest.fromFirestore(snapshot);
        currentTrip.value = updatedTrip;

        // Check if trip was just completed
        if (updatedTrip.status == TripStatus.completed) {
          // Show rating dialog after a short delay
          Future.delayed(const Duration(seconds: 1), () {
            showRatingDialog();
          });
        }
      } else {
        // Trip document no longer exists
        currentTrip.value = null;
        _tripSubscription?.cancel();
      }
    }, onError: (error) {
      errorMessage.value = 'حدث خطأ أثناء متابعة الرحلة: $error';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء متابعة الرحلة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    });
  }
}
