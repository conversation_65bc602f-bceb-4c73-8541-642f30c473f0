import 'dart:async';

import 'package:ai_delivery_app/app/config/constants.dart';
import 'package:ai_delivery_app/app/modules/driver_home/controllers/passengers_controller.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:location/location.dart' as loc;

import '../../../data/models/passenger_model.dart';
import '../../../data/models/trip_request_model.dart';
import '../../../services/payment_service.dart';
import '../views/select_passengers_view.dart';
import 'driver_trip_controller.dart';

// Direction model might not be needed if driver only sees TripRequest data, but keep if used
// import 'package:flutter_getx_mvc_delivery_app/app/data/models/direction_model.dart';

// Constants for Firestore collection names
const String DRIVER_LOCATIONS_COLLECTION = 'driverLocations';
const String TRIP_REQUESTS_COLLECTION = 'tripRequests';

// Enum for controller's internal state (simplified for driver view)
enum DriverHomeUiState {
  initializing,
  offline,
  idleOnline, // Online, no requests, no trip
  incomingRequest,
  activeTrip
}

class DriverHomeController extends GetxController {
  // --- Dependencies ---
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PolylinePoints _polylinePoints = PolylinePoints();
  final loc.Location _locationService = loc.Location();

  // --- Driver State ---
  late final User? currentUser;
  final RxBool isOnline = false.obs; // Driver's availability status
  final Rxn<TripRequest> currentAssignedTrip =
      Rxn<TripRequest>(); // The trip currently handled
  final Rxn<TripRequest> incomingRideRequest =
      Rxn<TripRequest>(); // A request waiting for acceptance

  // Trip controller for handling cancellation and rating
  late DriverTripController tripController;
  
  // Passenger data functionality
  final RxBool isRequestingPassengerData = false.obs;
  final RxList<Passenger> tripPassengers = <Passenger>[].obs;

  // --- Location & Map ---
  final Rxn<LatLng> driverLocation =
      Rxn<LatLng>(); // Driver's reactive position
  loc.LocationData?
      _currentLocationData; // Full location data (heading, speed etc.)
  StreamSubscription?
      _locationSubscription; // Listener for device location changes
  Timer? _locationUpdateTimer; // Timer for periodic Firestore updates
  GoogleMapController? mapController; // Map controller instance
  final RxSet<Marker> mapMarkers = <Marker>{}.obs; // Markers on the map
  final RxSet<Polyline> mapPolylines = <Polyline>{}.obs; // Routes on the map

  // --- Calculated Route Info (Optional for Driver View, but needed for route drawing) ---
  final RxnString estimatedDistance = RxnString();
  final RxnString estimatedDuration = RxnString();
  // estimatedPrice is less relevant for driver unless they see fare details

  // --- Listeners ---
  StreamSubscription?
      _incomingRequestsSubscription; // Listener for new ride requests
  StreamSubscription?
      _activeTripSubscription; // Listener for the status of the assigned trip

  // --- UI / General ---
  final isLoading =
      false.obs; // For specific actions (accepting, updating status)
  final errorMessage = RxnString(); // For showing errors

  // --- Internal UI State (derived from other states) ---
  // You could use a computed property for simpler UI logic in the View
  Rx<DriverHomeUiState> get uiState {
    // Check most specific states first
    if (incomingRideRequest.value != null) {
      // If there's an incoming request, that takes priority
      return DriverHomeUiState.incomingRequest.obs;
    }
    if (currentAssignedTrip.value != null) {
      // If the driver is currently on a trip
      return DriverHomeUiState.activeTrip.obs;
    }
    // If no active request or trip, check online status
    if (isOnline.value) {
      // If online but no request/trip
      return DriverHomeUiState.idleOnline.obs;
    }
    // If none of the above conditions are met, assume offline
    // Note: Initialization is handled separately by the isLoading flag in the View
    return DriverHomeUiState.offline.obs;
  }
  // Keep HomeState from user controller if needed for shared logic/navigation? Or remove.
  // Let's remove it for now to simplify driver controller.
  // final Rx<HomeState> homeState = HomeState.idle.obs; // Replaced by uiState getter

  // === Lifecycle Methods ===

  @override
  void onInit() {
    super.onInit();

    // Initialize trip controller
    tripController = Get.put(DriverTripController());

    currentUser = _auth.currentUser;
    if (currentUser == null) {
      print(
          "CRITICAL: DriverHomeController loaded without authenticated user.");
      _logoutUser();
      return;
    }
    print("DriverHomeController initializing for ${currentUser!.uid}");
    _initializeLocation(); // Get initial location first
    _fetchInitialDriverState(); // Then check previous state from Firestore
  }

  @override
  void onClose() {
    print("Closing DriverHomeController for ${currentUser?.uid}...");
    // Stop all background activities and listeners
    _stopLocationUpdates();
    _stopListeningForRequests();
    _stopListeningToActiveTrip();
    _locationUpdateTimer?.cancel();
    mapController?.dispose();
    // Consider setting driver offline in Firestore on close for safety
    // goOffline(updateFirestore: true); // This might interfere if closed during active trip
    print("DriverHomeController closed.");
    super.onClose();
  }

  // === Initialization ===

  Future<void> _initializeLocation() async {
    // Using isLoading flag for initialization phase
    isLoading.value = true;
    errorMessage.value = null;
    try {
      bool serviceEnabled = await _locationService.serviceEnabled();
      if (!serviceEnabled)
        serviceEnabled = await _locationService.requestService();
      if (!serviceEnabled) throw Exception('Location services disabled.');

      loc.PermissionStatus permission = await _locationService.hasPermission();
      if (permission == loc.PermissionStatus.denied)
        permission = await _locationService.requestPermission();
      if (permission != loc.PermissionStatus.granted)
        throw Exception('Location permission denied.');

      _currentLocationData = await _locationService.getLocation();
      if (_currentLocationData?.latitude != null &&
          _currentLocationData?.longitude != null) {
        driverLocation.value = LatLng(
            _currentLocationData!.latitude!, _currentLocationData!.longitude!);
        _updateDriverMarker();
        _centerMapOnDriver(); // Center map on initial location
        print("Initial location obtained: ${driverLocation.value}");
      } else {
        throw Exception('Could not get initial location.');
      }
      // Background mode might be needed for real tracking
      // await _locationService.enableBackgroundMode(enable: true);
    } catch (e) {
      print("Location Initialization Error: $e");
      errorMessage.value =
          "Location Error: ${e.toString()}. Please enable permissions/service.";
    } finally {
      isLoading.value = false; // Finish initialization loading
    }
  }

  void _centerMapOnDriver() {
    // Check if both the map controller is ready and the driver's location is known
    if (driverLocation.value != null && mapController != null) {
      try {
        // Animate the camera to the driver's LatLng with a default zoom level (e.g., 15)
        mapController?.animateCamera(
            CameraUpdate.newLatLngZoom(driverLocation.value!, 15.0));
      } catch (e) {
        // Catch potential errors during animation (though less common for basic zoom)
        print("Error centering map on driver: $e");
      }
    } else {
      print(
          "_centerMapOnDriver skipped: mapController (${mapController != null}) or driverLocation (${driverLocation.value != null}) is null.");
    }
  }

  Future<void> _fetchInitialDriverState() async {
    if (currentUser == null) return;
    isLoading.value = true; // Use loading flag
    try {
      final docSnap = await _firestore
          .collection(DRIVER_LOCATIONS_COLLECTION)
          .doc(currentUser!.uid)
          .get();
      bool wasOnline = false;
      String? activeTripId;

      if (docSnap.exists && docSnap.data() != null) {
        final data = docSnap.data()!;
        wasOnline = data['isOnline'] ?? false;
        activeTripId = data['activeTripId'] as String?; // Cast safely
      }

      isOnline.value = wasOnline; // Set local state based on fetched data

      // Check for and potentially resume active trip
      if (activeTripId != null) {
        await _resumeActiveTrip(activeTripId);
      }

      // Start updates/listeners if needed based on initial state
      if (isOnline.value) {
        _startLocationUpdates(); // Start location updates if online
        if (currentAssignedTrip.value == null) {
          // Only listen for NEW requests if not resuming a trip
          _listenForRideRequests();
        }
      }

      print(
          "Initial driver state fetched: isOnline=${isOnline.value}, activeTripId=$activeTripId");
    } catch (e) {
      print("Error fetching initial driver state: $e");
      errorMessage.value = "Could not fetch initial state.";
      isOnline.value = false; // Default to offline on error
    } finally {
      isLoading.value = false;
    }
  }

  // Helper to fetch and validate a trip referenced by activeTripId
  Future<void> _resumeActiveTrip(String tripId) async {
    try {
      final tripSnap = await _firestore
          .collection(TRIP_REQUESTS_COLLECTION)
          .doc(tripId)
          .get();
      if (tripSnap.exists) {
        final trip = TripRequest.fromFirestore(tripSnap);
        // Check if the trip is actually still active
        if (![
          TripStatus.completed,
          TripStatus.cancelledByDriver,
          TripStatus.cancelledByUser
        ].contains(trip.status)) {
          currentAssignedTrip.value = trip;
          _listenToActiveTripStatus(trip.id); // Start listening
          _updateMapAndRouteForActiveTripStatus(trip.status); // Draw route etc.
          print("Resumed active trip: ${trip.id}");
        } else {
          print(
              "Trip $tripId found but is already terminal. Clearing from driver doc.");
          _clearActiveTripFromDriverDoc(); // Clean up Firestore link
        }
      } else {
        print(
            "Referenced activeTripId $tripId not found. Clearing from driver doc.");
        _clearActiveTripFromDriverDoc(); // Clean up Firestore link
      }
    } catch (e) {
      print("Error resuming active trip $tripId: $e");
      _clearActiveTripFromDriverDoc(); // Clean up on error
    }
  }

  // === Core Driver Actions ===

  Future<void> toggleOnlineStatus(bool goOnline) async {
    if (currentUser == null || isLoading.value)
      return; // Prevent rapid toggling
    if (goOnline == isOnline.value) return;

    isLoading.value = true;
    errorMessage.value = null;
    final originalState =
        isOnline.value; // Store original state for potential revert

    try {
      if (goOnline) {
        // --- Going Online ---
        if (driverLocation.value == null) {
          await _initializeLocation(); // Re-attempt getting location
          if (driverLocation.value == null)
            throw Exception("Cannot go online without location.");
        }
        // Update Firestore FIRST
        await _updateDriverDocInFirestore(
            isOnline: true,
            location: driverLocation.value!,
            isAvailableForRequest: currentAssignedTrip.value == null);
        // Then update local state and start listeners/updates
        isOnline.value = true;
        _startLocationUpdates();
        if (currentAssignedTrip.value == null) _listenForRideRequests();
        print("Driver ${currentUser!.uid} is now ONLINE.");
      } else {
        // --- Going Offline ---
        // Update Firestore FIRST
        await _updateDriverDocInFirestore(
            isOnline: false,
            isAvailableForRequest: false); // Mark offline and unavailable
        // Then update local state and stop listeners/updates
        isOnline.value = false;
        _stopLocationUpdates();
        _stopListeningForRequests();
        _locationUpdateTimer?.cancel(); // Explicitly cancel timer
        print("Driver ${currentUser!.uid} is now OFFLINE.");
      }
    } catch (e) {
      print("Error toggling online status: $e");
      errorMessage.value = "Error: ${e.toString()}";
      isOnline.value = originalState; // Revert local state on error
      // Attempt to stop listeners/updates if error occurred while going online
      if (goOnline) {
        _stopLocationUpdates();
        _stopListeningForRequests();
        _locationUpdateTimer?.cancel();
      }
    } finally {
      isLoading.value = false;
    }
  }

  // Simplified offline logic used internally or on logout/close
  Future<void> goOffline({bool updateFirestore = false}) async {
    isOnline.value = false; // Update local state immediately
    _stopLocationUpdates();
    _stopListeningForRequests();
    _locationUpdateTimer?.cancel();
    if (updateFirestore && currentUser != null) {
      try {
        await _updateDriverDocInFirestore(
            isOnline: false, isAvailableForRequest: false);
      } catch (e) {
        print("Error updating Firestore to offline: $e");
      }
    }
  }

  Future<void> acceptRideRequest(String tripId) async {
    if (currentUser == null ||
        currentAssignedTrip.value != null ||
        isLoading.value) return;

    isLoading.value = true;
    errorMessage.value = null;
    final requestToAccept = incomingRideRequest.value;
    incomingRideRequest.value = null; // Clear incoming UI

    if (requestToAccept == null || requestToAccept.id != tripId) {
      print(
          "Error: Request to accept ($tripId) doesn't match incoming request.");
      isLoading.value = false;
      // Potentially re-enable listening for requests if needed
      if (isOnline.value) _listenForRideRequests();
      return;
    }

    try {
      final tripRef =
          _firestore.collection(TRIP_REQUESTS_COLLECTION).doc(tripId);
      final driverRef = _firestore
          .collection(DRIVER_LOCATIONS_COLLECTION)
          .doc(currentUser!.uid);
      final batch = _firestore.batch();

      // Fetch driver details from user profile
      final driverProfileDoc =
          await _firestore.collection('users').doc(currentUser!.uid).get();
      String driverName = 'Driver';
      String driverVehicle = 'Vehicle';
      double driverRating = 0.0;

      if (driverProfileDoc.exists) {
        final driverData = driverProfileDoc.data() as Map<String, dynamic>;
        driverName = driverData['name'] ??
            currentUser!.displayName ??
            'Driver ${currentUser!.uid.substring(0, 5)}';
        driverVehicle = driverData['vehicleInfo'] ?? 'Vehicle';
        driverRating = (driverData['rating'] as num?)?.toDouble() ?? 0.0;
      }

      // Update Trip: status, driverId, timestamp, driver details
      batch.update(tripRef, {
        'status': TripRequest.statusToString(TripStatus.assigned),
        'driverId': currentUser!.uid,
        'assignedAt': FieldValue.serverTimestamp(),
        'driverName': driverName,
        'driverVehicle': driverVehicle,
        'driverRating': driverRating,
      });

      // Update Driver: link trip, mark as unavailable
      batch.update(driverRef, {
        'activeTripId': tripId,
        'isAvailableForRequest': false, // Driver is now busy
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      await batch.commit(); // Execute atomic update

      // Update Local State
      currentAssignedTrip.value = requestToAccept;
      currentAssignedTrip.value?.status =
          TripStatus.assigned; // Reflect immediate status change
      currentAssignedTrip.value?.driverId = currentUser!.uid;
      currentAssignedTrip.refresh(); // Force update if needed

      _stopListeningForRequests(); // Stop looking for NEW requests
      _listenToActiveTripStatus(tripId); // Start monitoring THIS trip
      _calculateAndDrawRouteToPickup(); // Show route to pickup location

      print("Driver ${currentUser!.uid} accepted Trip ${tripId}");
    } catch (e) {
      print("Error accepting ride request $tripId: $e");
      errorMessage.value =
          "Failed to accept trip. It might have been assigned to another driver.";
      // Don't re-show the request. If still online, restart listening.
      if (isOnline.value) _listenForRideRequests();
    } finally {
      isLoading.value = false;
    }
  }

  void rejectRideRequest() {
    if (incomingRideRequest.value != null) {
      print("Driver rejected trip ${incomingRideRequest.value!.id}");
      incomingRideRequest.value = null; // Clear UI
      // Backend might log this rejection. No immediate Firestore update needed here.
      // Continue listening for other requests if online.
      if (isOnline.value &&
          currentAssignedTrip.value == null &&
          _incomingRequestsSubscription == null) {
        _listenForRideRequests();
      }
    }
  }

  Future<void> updateActiveTripStatus(TripStatus newStatus) async {
    if (currentAssignedTrip.value == null || isLoading.value) return;

    isLoading.value = true;
    errorMessage.value = null;
    final tripId = currentAssignedTrip.value!.id;
    final currentStatus = currentAssignedTrip.value!.status;

    // Basic state transition validation (optional but good)
    bool isValidTransition = false;
    if ((currentStatus == TripStatus.assigned ||
            currentStatus == TripStatus.enRouteToPickup) &&
        newStatus == TripStatus.arrivedAtPickup) isValidTransition = true;
    if (currentStatus == TripStatus.arrivedAtPickup &&
        newStatus == TripStatus.ongoing) isValidTransition = true;
    if (currentStatus == TripStatus.ongoing &&
        newStatus == TripStatus.completed) isValidTransition = true;

    if (!isValidTransition) {
      print(
          "Invalid status transition requested: $currentStatus -> $newStatus");
      errorMessage.value = "Invalid action for current trip status.";
      isLoading.value = false;
      return;
    }

    try {
      Map<String, dynamic> updateData = {
        'status': TripRequest.statusToString(newStatus),
      };

      // Add relevant timestamps for specific transitions
      if (newStatus == TripStatus.arrivedAtPickup)
        updateData['driverArrivedAt'] = FieldValue.serverTimestamp();
      if (newStatus == TripStatus.ongoing)
        updateData['tripStartedAt'] = FieldValue.serverTimestamp();
      if (newStatus == TripStatus.completed)
        updateData['completedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(TRIP_REQUESTS_COLLECTION)
          .doc(tripId)
          .update(updateData);

      // Update local state (will also be updated by listener, but good for immediate feedback)
      currentAssignedTrip.value?.status = newStatus;
      currentAssignedTrip.refresh();

      print("Trip $tripId status updated to $newStatus by driver.");

      // Perform actions based on new status
      if (newStatus == TripStatus.ongoing) {
        _calculateAndDrawRouteToDestination(); // Switch route to destination
      } else if (newStatus == TripStatus.completed) {
        _handleTripCompletion(); // Clean up driver state
      } else {
        // For arrivedAtPickup, maybe just update UI, no new route needed yet
        _updateMapAndRouteForActiveTripStatus(newStatus); // Refresh map state
      }
    } catch (e) {
      print("Error updating trip $tripId status to $newStatus: $e");
      errorMessage.value = "Failed to update trip status.";
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _handleTripCompletion() async {
    print("Handling trip completion cleanup...");

    // Store trip for rating before clearing
    final completedTrip = currentAssignedTrip.value;

    // Process payment if trip was completed successfully
    if (completedTrip != null && completedTrip.status == TripStatus.completed) {
      try {
        // Check if payment has already been processed
        final paymentQuery = await _firestore
            .collection('payments')
            .where('tripId', isEqualTo: completedTrip.id)
            .where('status', isEqualTo: 'completed')
            .get();

        bool paymentAlreadyProcessed = paymentQuery.docs.isNotEmpty;

        // Only process payment if it hasn't been processed already
        if (!paymentAlreadyProcessed) {
          // Get payment service
          final paymentService = Get.find<PaymentService>();

          // Get payment method directly from the trip
          final paymentMethod = completedTrip.paymentMethod;

          if (paymentMethod == 'cash') {
            // For cash payments, create a wallet transaction for the app fee
            await _processCashPayment(completedTrip);
          } else {
            // For non-cash payments, process driver payment
            await paymentService.processDriverPayment(
              completedTrip.id,
              currentUser!.uid,
              completedTrip, // Pass the trip object directly
            );

            // Show success message
            Get.snackbar(
              'تم إضافة المبلغ',
              'تم إضافة مبلغ الرحلة إلى محفظتك',
              backgroundColor: Colors.green,
              colorText: Colors.white,
              duration: const Duration(seconds: 3),
            );
          }
        }
      } catch (e) {
        print("Error processing payment: $e");
        // Don't show error to user, just log it
      }

      // Pass the completed trip to the trip controller for rating
      tripController.currentTrip.value = completedTrip;

      // Show rating dialog after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        tripController.showRatingDialog();
      });
    }

    // Clean up UI and state
    _stopListeningToActiveTrip();
    currentAssignedTrip.value = null;
    _clearActiveTripFromDriverDoc();
    mapPolylines.clear();
    mapMarkers.removeWhere((m) =>
        m.markerId.value == 'pickup' || m.markerId.value == 'destination');
    _clearRouteDetails(); // Clear distance/duration if shown

    // If driver is still marked as online, start listening for new requests again
    if (isOnline.value) {
      _listenForRideRequests();
      _updateDriverDocInFirestore(
          isAvailableForRequest: true); // Mark available
      _centerMapOnDriver(); // Recenter map
    }
  }

  // Process cash payment by creating a wallet transaction for the app fee
  Future<void> _processCashPayment(TripRequest trip) async {
    try {
      // Check if app fee transaction has already been processed
      final walletTransactionQuery = await _firestore
          .collection('walletTransactions')
          .where('tripId', isEqualTo: trip.id)
          .where('userId', isEqualTo: currentUser!.uid)
          .where('type', isEqualTo: 'appFee')
          .get();

      // If app fee transaction has already been processed, return
      if (walletTransactionQuery.docs.isNotEmpty) {
        print("App fee transaction already processed for trip ${trip.id}");
        return;
      }

      // Get payment information for the trip
      final paymentQuery = await _firestore
          .collection('payments')
          .where('tripId', isEqualTo: trip.id)
          .get();

      String paymentId = '';

      if (paymentQuery.docs.isNotEmpty) {
        final paymentDoc = paymentQuery.docs.first;
        paymentId = paymentDoc.id;

        // Mark payment as completed
        await _firestore.collection('payments').doc(paymentId).update({
          'status': 'completed',
          'completedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Create a new payment record if none exists
        final payment = {
          'tripId': trip.id,
          'userId': trip.userId,
          'driverId': currentUser!.uid,
          'amount': trip.totalPrice,
          'baseAmount': trip.basePrice,
          'appFee': trip.appFee,
          'taxAmount': trip.taxAmount,
          'discountAmount': trip.discountAmount,
          'method': 'cash',
          'status': 'completed',
          'createdAt': FieldValue.serverTimestamp(),
          'completedAt': FieldValue.serverTimestamp(),
        };

        final paymentRef = await _firestore.collection('payments').add(payment);
        paymentId = paymentRef.id;
      }

      // Calculate driver amount (base price minus app fee and tax)
      final driverAmount = trip.totalPrice - trip.appFee - trip.taxAmount;

      // Create a wallet transaction for the app fee (driver owes the app fee to the platform)
      final transaction = {
        'userId': currentUser!.uid,
        'amount': -trip
            .appFee, // Negative amount because driver owes this to the platform
        'type': 'appFee',
        'description': 'App fee for trip #${trip.id}',
        'referenceId': paymentId,
        'tripId': trip.id,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'completed',
      };

      await _firestore.collection('walletTransactions').add(transaction);

      // Update driver's wallet balance
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'walletBalance': FieldValue.increment(-trip.appFee),
      });



      // Show success message
      Get.snackbar(
        'تم تسجيل الرحلة',
        'تم خصم عمولة التطبيق من محفظتك',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      print("Error processing cash payment: $e");
      // Don't show error to user, just log it
    }
  }

  Future<void> _clearActiveTripFromDriverDoc() async {
    if (currentUser == null) return;
    try {
      await _firestore
          .collection(DRIVER_LOCATIONS_COLLECTION)
          .doc(currentUser!.uid)
          .update({
        'activeTripId': FieldValue.delete(),
        'isAvailableForRequest': true, // Become available again
        'lastUpdated': FieldValue.serverTimestamp(),
      });
      print("Cleared activeTripId from driver doc.");
    } catch (e) {
      // Log error, but don't block UI
      print("Error clearing activeTripId from driver doc: $e");
    }
  }

  Future<void> cancelTripRequest({bool showMessage = true}) async {
    if (currentAssignedTrip.value == null || isLoading.value)
      return; // Can only cancel assigned trip

    // Pass the current trip to the trip controller
    tripController.currentTrip.value = currentAssignedTrip.value;

    // Show cancellation dialog with reasons
    tripController.showCancellationDialog();

    // Clean up map after cancellation
    mapPolylines.clear();
    mapMarkers.removeWhere((m) =>
        m.markerId.value == 'pickup' || m.markerId.value == 'destination');
    _clearRouteDetails();

    // Stop listeners
    _stopListeningToActiveTrip();
    currentAssignedTrip.value = null; // Clear local state

    // Restart listening if online
    if (isOnline.value) {
      _listenForRideRequests();
    }
  }

  // === Location Updates ===

  void _startLocationUpdates() {
    if (currentUser == null || _locationSubscription != null)
      return; // Already running or no user

    print("Starting location updates for ${currentUser!.uid}");
    _locationService.changeSettings(
        accuracy: loc.LocationAccuracy.high,
        interval: 5000,
        distanceFilter: 10); // Update every 5s or 10m

    _locationSubscription = _locationService.onLocationChanged.listen(
        (loc.LocationData currentLocation) {
      if (currentLocation.latitude != null &&
          currentLocation.longitude != null) {
        driverLocation.value =
            LatLng(currentLocation.latitude!, currentLocation.longitude!);
        _currentLocationData = currentLocation;
        _updateDriverMarker();
        // Firestore update handled by timer
      }
    }, onError: (error) {
      print("Error in location stream: $error");
      errorMessage.value = "Location stream error. Updates stopped.";
      _stopLocationUpdates();
      goOffline(
          updateFirestore: true); // Go offline if location fails critically
    });

    _locationUpdateTimer?.cancel();
    _locationUpdateTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      // Update Firestore every 15s
      if (driverLocation.value != null && isOnline.value) {
        _updateDriverDocInFirestore(location: driverLocation.value!);
      } else {
        timer.cancel(); // Stop timer if location lost or offline
        if (!isOnline.value) _stopLocationUpdates();
      }
    });
  }

  void _stopLocationUpdates() {
    if (_locationSubscription != null) {
      _locationSubscription?.cancel();
      _locationSubscription = null;
      print("Stopped location stream listener.");
    }
    if (_locationUpdateTimer != null) {
      _locationUpdateTimer?.cancel();
      _locationUpdateTimer = null;
      print("Stopped location Firestore update timer.");
    }
  }

  Future<void> _updateDriverDocInFirestore(
      {LatLng? location, bool? isOnline, bool? isAvailableForRequest}) async {
    if (currentUser == null) return;
    final docRef = _firestore
        .collection(DRIVER_LOCATIONS_COLLECTION)
        .doc(currentUser!.uid);
    final Map<String, dynamic> data = {
      'lastUpdated': FieldValue.serverTimestamp()
    };
    if (location != null)
      data['location'] = GeoPoint(location.latitude, location.longitude);
    if (isOnline != null) data['isOnline'] = isOnline;
    if (isAvailableForRequest != null)
      data['isAvailableForRequest'] = isAvailableForRequest;

    try {
      await docRef.set(data, SetOptions(merge: true));
    } catch (e) {
      print("Error updating driver doc in Firestore: $e");
      // Don't block UI, maybe log silently
    }
  }

  // === Ride Request Listening ===

  void _listenForRideRequests() {
    if (currentUser == null ||
        !isOnline.value ||
        currentAssignedTrip.value != null ||
        _incomingRequestsSubscription != null) {
      print(
          "Skipping _listenForRideRequests: Preconditions not met (User: ${currentUser != null}, Online: ${isOnline.value}, OnTrip: ${currentAssignedTrip.value != null}, AlreadyListening: ${_incomingRequestsSubscription != null})");
      return;
    }

    print("Starting to listen for ride requests...");

    // *** WARNING: Inefficient Query - Replace with GeoQueries for Production ***
    _incomingRequestsSubscription = _firestore
        .collection(TRIP_REQUESTS_COLLECTION)
        .where('status',
            isEqualTo: TripRequest.statusToString(TripStatus.searching))
        .limit(5) // Heavy limit for demo
        .snapshots()
        .listen((snapshot) {
      if (!isOnline.value || currentAssignedTrip.value != null) {
        _stopListeningForRequests();
        return; // Stop if state changed
      }
      if (incomingRideRequest.value != null)
        return; // Don't process if already showing one

      TripRequest? potentialRequest;
      for (var doc in snapshot.docs) {
        try {
          potentialRequest = TripRequest.fromFirestore(doc);
          // TODO: Add filtering logic (distance, ride type, etc.)
          // double distance = calculateDistance(...); if (distance < threshold) break;
          break; // Simple: take the first one found
        } catch (e) {
          potentialRequest = null;
          print("Parse error on request ${doc.id}: $e");
        }
      }

      if (potentialRequest != null) {
        incomingRideRequest.value = potentialRequest;
        print("Incoming ride request detected: ${potentialRequest.id}");
      } else {
        // No suitable request found in this batch
        if (incomingRideRequest.value == null) {
          // Only log if not already showing one
          print("No suitable incoming requests found in snapshot.");
        }
      }
    }, onError: (error) {
      print("Error listening for ride requests: $error");
      errorMessage.value = "Error getting ride requests.";
      _stopListeningForRequests();
    });
  }

  void _stopListeningForRequests() {
    if (_incomingRequestsSubscription != null) {
      _incomingRequestsSubscription?.cancel();
      _incomingRequestsSubscription = null;
      print("Stopped listening for new ride requests.");
    }
  }

  // === Active Trip Status Listening ===

  void _listenToActiveTripStatus(String tripId) {
    _stopListeningToActiveTrip();
    print("Listening to active trip: $tripId");
    _activeTripSubscription = _firestore
        .collection(TRIP_REQUESTS_COLLECTION)
        .doc(tripId)
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists &&
          currentAssignedTrip.value != null &&
          currentAssignedTrip.value?.id == tripId) {
        // Check if still the active trip
        try {
          final updatedTrip = TripRequest.fromFirestore(snapshot);
          currentAssignedTrip.value = updatedTrip; // Update local copy
          print("Active trip $tripId status sync: ${updatedTrip.status}");
          // Check for terminal states again here as a failsafe
          if ([
            TripStatus.completed,
            TripStatus.cancelledByDriver,
            TripStatus.cancelledByUser
          ].contains(updatedTrip.status)) {
            // _handleTripCompletion();
          } else {
            _updateMapAndRouteForActiveTripStatus(
                updatedTrip.status); // Refresh map/route if needed
          }
        } catch (e) {
          print("Error parsing active trip snapshot $tripId: $e");
        }
      } else {
        print("Active trip $tripId not found or mismatch. Cleaning up.");
        // _handleTripCompletion(); // Assume trip ended if doc gone or mismatch
      }
    }, onError: (error) {
      print("Error listening to active trip $tripId: $error");
      // Consider cleanup on error? _handleTripCompletion();
    });
  }

  void _stopListeningToActiveTrip() {
    if (_activeTripSubscription != null) {
      _activeTripSubscription?.cancel();
      _activeTripSubscription = null;
      print("Stopped listening to active trip.");
    }
  }

  // === Map & Route Drawing ===

  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
    print("Driver Map Controller Initialized.");
    if (driverLocation.value != null)
      _updateMapCamera(driverLocation.value!, zoom: 15.0);
    _updateDriverMarker();
    // If resuming an active trip, show its state immediately
    if (currentAssignedTrip.value != null) {
      _updateMapAndRouteForActiveTripStatus(currentAssignedTrip.value!.status);
    }
  }

  void _updateMapCamera(LatLng target, {double zoom = 14.0}) {
    mapController?.animateCamera(CameraUpdate.newLatLngZoom(target, zoom));
  }

  void _recenterMapOnUser() {
    if (driverLocation.value != null)
      _updateMapCamera(driverLocation.value!, zoom: 15.0);
  }

  void _addOrUpdateMarker(LatLng position, String markerId, double hue,
      {bool draggable = false, String? info}) {
    final Marker marker = Marker(
      markerId: MarkerId(markerId),
      position: position,
      icon: BitmapDescriptor.defaultMarkerWithHue(hue),
      infoWindow: info != null ? InfoWindow(title: info) : InfoWindow.noText,
      draggable: draggable,
      onDragEnd: draggable
          ? (newPosition) {
              print(
                  "Warning: Draggable marker '$markerId' moved in Driver App - Ignoring.");
              // Driver shouldn't typically drag trip markers. If needed, add specific logic.
            }
          : null,
    );
    mapMarkers.removeWhere((m) => m.markerId.value == markerId);
    mapMarkers.add(marker);
  }

  void _updateDriverMarker() {
    if (driverLocation.value != null) {
      _addOrUpdateMarker(
          driverLocation.value!, 'driverLocation', BitmapDescriptor.hueAzure,
          info: 'My Location');
    }
  }

  void _addTripMarkers() {
    mapMarkers.removeWhere(
        (m) => m.markerId.value != 'driverLocation'); // Keep driver marker
    final trip = currentAssignedTrip.value;
    if (trip != null) {
      _addOrUpdateMarker(
          LatLng(trip.pickupLocation.latitude, trip.pickupLocation.longitude),
          'pickup',
          BitmapDescriptor.hueOrange,
          info: 'Pickup');
      _addOrUpdateMarker(
          LatLng(trip.dropoffLocation.latitude, trip.dropoffLocation.longitude),
          'destination',
          BitmapDescriptor.hueViolet,
          info: 'Destination');
    }
  }

  Future<void> _updateMapAndRouteForActiveTripStatus(TripStatus status) async {
    mapPolylines.clear();
    _addTripMarkers(); // Ensure pickup/dest markers are correct

    LatLng? origin = driverLocation.value;
    LatLng? destination;
    final trip = currentAssignedTrip.value;

    if (origin != null && trip != null) {
      if (status == TripStatus.assigned ||
          status == TripStatus.enRouteToPickup) {
        destination =
            LatLng(trip.pickupLocation.latitude, trip.pickupLocation.longitude);
      } else if (status == TripStatus.ongoing) {
        destination = LatLng(
            trip.dropoffLocation.latitude, trip.dropoffLocation.longitude);
      }
    }

    if (destination != null) {
      await _calculateAndDrawRouteBetween(origin!, destination);
    } else {
      _zoomToFitActiveTripMarkers(); // Zoom out if no route needed
    }
  }

  Future<void> _calculateAndDrawRouteToPickup() async {
    if (driverLocation.value != null && currentAssignedTrip.value != null) {
      await _calculateAndDrawRouteBetween(
          driverLocation.value!,
          LatLng(currentAssignedTrip.value!.pickupLocation.latitude,
              currentAssignedTrip.value!.pickupLocation.longitude));
    }
  }

  Future<void> _calculateAndDrawRouteToDestination() async {
    if (driverLocation.value != null && currentAssignedTrip.value != null) {
      await _calculateAndDrawRouteBetween(
          driverLocation.value!,
          LatLng(currentAssignedTrip.value!.dropoffLocation.latitude,
              currentAssignedTrip.value!.dropoffLocation.longitude));
    }
  }

  Future<void> _calculateAndDrawRouteBetween(
      LatLng origin, LatLng destination) async {
    mapPolylines.clear();
    _clearRouteDetails(); // Clear old distance/duration
    isLoading.value = true; // Show loading for route calculation
    try {
      PolylineResult result = await _polylinePoints.getRouteBetweenCoordinates(
        googleApiKey: Constants.GOOGLE_MAPS_API_KEY,
        request: PolylineRequest(
            origin: PointLatLng(origin.latitude, origin.longitude),
            destination:
                PointLatLng(destination.latitude, destination.longitude),
            mode: TravelMode.driving),
      );
      if (result.points.isNotEmpty) {
        List<LatLng> coords =
            result.points.map((p) => LatLng(p.latitude, p.longitude)).toList();
        mapPolylines.add(Polyline(
            polylineId: const PolylineId('active_route'),
            color: Colors.green.shade600,
            width: 6,
            points: coords));
        _zoomToFitRoute(coords); // Zoom to the new route
        // Optionally fetch distance/duration for driver info
        // await _fetchRouteDetails(origin, destination);
        print("Driver route calculated.");
      } else {
        print("Could not get driver route: ${result.errorMessage}");
        _zoomToFitActiveTripMarkers();
      }
    } catch (e) {
      print("Error calculating driver route: $e");
      _zoomToFitActiveTripMarkers();
    } finally {
      isLoading.value = false;
    }
  }

  // Method to fetch distance/duration (Optional for driver view)
  Future<void> _fetchRouteDetails(LatLng origin, LatLng destination) async {
    final String url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=${Constants.GOOGLE_MAPS_API_KEY}';
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        /* ... parse and set estimatedDistance/Duration ... */
      }
    } catch (e) {
      print("Error fetching route details: $e");
    }
  }

  void _clearRouteDetails() {
    estimatedDistance.value = null;
    estimatedDuration.value = null;
  }

  void _zoomToFitRoute(List<LatLng> points) {
    /* ... copy from previous response ... */
    if (points.isEmpty || mapController == null) return;
    try {
      if (points.length == 1) {
        mapController
            ?.animateCamera(CameraUpdate.newLatLngZoom(points.first, 15));
      } else {
        LatLngBounds bounds = _boundsFromLatLngList(points);
        mapController
            ?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 60.0));
      }
    } catch (e) {
      print("Error zooming to fit route: $e");
      _recenterMapOnUser();
    }
  }

  void _zoomToFitActiveTripMarkers() {
    /* ... copy from previous response ... */
    if (mapController == null) return;
    List<LatLng> pointsToFit = [];
    if (driverLocation.value != null) pointsToFit.add(driverLocation.value!);
    final trip = currentAssignedTrip.value;
    if (trip != null) {
      pointsToFit.add(
          LatLng(trip.pickupLocation.latitude, trip.pickupLocation.longitude));
      pointsToFit.add(LatLng(
          trip.dropoffLocation.latitude, trip.dropoffLocation.longitude));
    }
    if (pointsToFit.length >= 2) {
      try {
        LatLngBounds bounds = _boundsFromLatLngList(pointsToFit);
        mapController
            ?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 70.0));
      } catch (e) {
        print("Error zooming to fit active trip markers: $e");
        _recenterMapOnUser();
      }
    } else {
      _recenterMapOnUser();
    }
  }

  LatLngBounds _boundsFromLatLngList(List<LatLng> list) {
    /* ... copy from previous response ... */
    assert(list.isNotEmpty);
    double? x0, x1, y0, y1;
    for (LatLng latLng in list) {
      if (x0 == null) {
        x0 = x1 = latLng.latitude;
        y0 = y1 = latLng.longitude;
      } else {
        if (latLng.latitude > x1!) x1 = latLng.latitude;
        if (latLng.latitude < x0) x0 = latLng.latitude;
        if (latLng.longitude > y1!) y1 = latLng.longitude;
        if (latLng.longitude < y0!) y0 = latLng.longitude;
      }
    }
    double latPadding = (x1! - x0!) * 0.1;
    double lngPadding = (y1! - y0!) * 0.1;
    if (latPadding == 0) latPadding = 0.002;
    if (lngPadding == 0) lngPadding = 0.002;
    return LatLngBounds(
        northeast: LatLng(x1 + latPadding, y1 + lngPadding),
        southwest: LatLng(x0 - latPadding, y0 - lngPadding));
  }

  // === Navigation Methods ===
  void navigateToLocation(GeoPoint location) async {
    try {
      // Convert GeoPoint to LatLng
      final LatLng destination = LatLng(location.latitude, location.longitude);

      // First, update the map to show this location
      _updateMapCamera(destination, zoom: 16.0);

      // Add a temporary marker if not already present
      _addOrUpdateMarker(
        destination,
        'navigation_target',
        BitmapDescriptor.hueRed,
        info: 'Navigation Target'
      );

      // Calculate route from driver's current location to the target
      if (driverLocation.value != null) {
        final LatLng origin = driverLocation.value!;
        await _calculateAndDrawRouteBetween(origin, destination);
      }

      // Launch external navigation app (Google Maps, Waze, etc.)
      final url = 'https://www.google.com/maps/dir/?api=1&destination=${location.latitude},${location.longitude}&travelmode=driving';

      // TODO: Add url_launcher package and uncomment this code
      // await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      
      // For now, just show a message since we don't have url_launcher implemented yet
      Get.snackbar(
        'Navigation',
        'Opening navigation to destination...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade900,
        margin: const EdgeInsets.all(10),
        duration: const Duration(seconds: 3),
      );
      
      print('Navigation URL: $url');
    } catch (e) {
      print('Error navigating to location: $e');
      _showErrorSnackbar('Could not open navigation: $e');
    }
  }

  // Show error message to user
  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade900,
      margin: const EdgeInsets.all(10),
      duration: const Duration(seconds: 3),
    );
  }
  
  // Request passenger data during a trip
  void requestPassengerData() async {
    try {
      isRequestingPassengerData.value = true;
      
      // Initialize the passengers controller if not already done
      if (!Get.isRegistered<PassengersController>()) {
        Get.put(PassengersController());
      }
      
      final passengersController = Get.find<PassengersController>();
      await passengersController.fetchPassengers();
      
      // Show the select passengers view
      Get.to(() => SelectPassengersView(
        onPassengersSelected: (selectedPassengers) {
          tripPassengers.value = selectedPassengers;
          _savePassengersToTrip(selectedPassengers);
        },
      ));
    } catch (e) {
      print('Error requesting passenger data: $e');
      _showErrorSnackbar('Could not request passenger data: $e');
    } finally {
      isRequestingPassengerData.value = false;
    }
  }
  
  // Save selected passengers to the current trip
  Future<void> _savePassengersToTrip(List<Passenger> passengers) async {
    try {
      if (currentAssignedTrip.value == null) return;
      
      // Convert passengers to JSON for storage
      final List<Map<String, dynamic>> passengersJson = 
          passengers.map((p) => p.toJson()).toList();
      
      // Update the trip document in Firestore
      await _firestore
          .collection(TRIP_REQUESTS_COLLECTION)
          .doc(currentAssignedTrip.value!.id)
          .update({'passengerDetails': passengersJson});
      
      Get.snackbar(
        'Success',
        'Passenger data saved successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade900,
        margin: const EdgeInsets.all(10),
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      print('Error saving passengers to trip: $e');
      _showErrorSnackbar('Could not save passenger data: $e');
    }
  }

  // === Logout ===
  Future<void> logout() async {
    /* ... copy from previous response ... */
    isLoading.value = true;
    try {
      print("Logging out driver: ${currentUser?.uid}");
      await goOffline(updateFirestore: true); // Ensure driver is marked offline
      _stopListeningToActiveTrip();
      _stopListeningForRequests();
      _stopLocationUpdates();
      _locationUpdateTimer?.cancel();
      await _auth.signOut();
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      print("Error during driver logout: $e");
      Get.snackbar('Logout Error', 'Could not log out.');
    } finally {
      isLoading.value = false;
    }
  }

  void _logoutUser() {
    Get.offAllNamed(Routes.LOGIN);
  }

  // === Getters (for View) ===
  String get userDisplayName =>
      currentUser?.displayName ?? currentUser?.email ?? 'Driver';
} // End of DriverHomeController