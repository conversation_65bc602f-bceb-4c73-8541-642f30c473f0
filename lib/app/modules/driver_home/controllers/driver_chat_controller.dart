import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/models/chat_message_model.dart';
import '../../../data/models/trip_request_model.dart';

class DriverChatController extends GetxController {
  // Dependencies
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // State
  final RxList<ChatMessage> messages = <ChatMessage>[].obs;
  final RxBool isLoading = false.obs;
  final RxnString errorMessage = RxnString();
  final Rxn<TripRequest> currentTrip = Rxn<TripRequest>();

  // Message input
  final messageController = TextEditingController();

  // Listeners
  StreamSubscription? _messagesSubscription;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    _stopListeningToMessages();
    messageController.dispose();
    super.onClose();
  }

  // Initialize chat for a specific trip
  void initializeChat(TripRequest trip) {
    currentTrip.value = trip;
    _listenToMessages(trip.id);
    _markMessagesAsRead(trip.id);
  }

  // Listen to messages for a specific trip
  void _listenToMessages(String tripId) {
    _stopListeningToMessages();

    _messagesSubscription = _firestore
        .collection('tripMessages')
        .where('tripId', isEqualTo: tripId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .listen((snapshot) {
      try {
        final List<ChatMessage> newMessages =
            snapshot.docs.map((doc) => ChatMessage.fromFirestore(doc)).toList();

        messages.value = newMessages;
      } catch (e) {
        errorMessage.value = "Error loading messages: $e";
      }
    }, onError: (error) {
      errorMessage.value = "Error listening to messages: $error";
    });
  }

  // Stop listening to messages
  void _stopListeningToMessages() {
    if (_messagesSubscription != null) {
      _messagesSubscription?.cancel();
      _messagesSubscription = null;
    }
  }

  // Send a new message
  Future<void> sendMessage() async {
    if (messageController.text.trim().isEmpty || currentTrip.value == null)
      return;

    isLoading.value = true;
    errorMessage.value = null;

    try {
      final String message = messageController.text.trim();
      final String tripId = currentTrip.value!.id;
      final String driverId = _auth.currentUser?.uid ?? '';

      // Create a new message document
      final newMessage = ChatMessage(
        id: '', // Will be set by Firestore
        tripId: tripId,
        senderId: driverId,
        sender: MessageSender.driver,
        message: message,
        timestamp: Timestamp.now(),
        isRead: false,
      );

      // Add to Firestore
      await _firestore.collection('tripMessages').add(newMessage.toFirestore());

      // Update unread count in trip document
      await _firestore
          .collection('tripRequests')
          .doc(tripId)
          .update({'unreadMessageCount': FieldValue.increment(1)});

      // Send notification to user (in a real app, this would trigger a push notification)
      await _firestore.collection('notifications').add({
        'recipientId': currentTrip.value!.userId,
        'tripId': tripId,
        'title': 'New message from driver',
        'body':
            message.length > 30 ? '${message.substring(0, 30)}...' : message,
        'type': 'chat',
        'timestamp': Timestamp.now(),
        'isRead': false,
        'sound': 'default',
        'priority': 'high',
      });

      // Clear the input field
      messageController.clear();
    } catch (e) {
      errorMessage.value = "Error sending message: $e";
    } finally {
      isLoading.value = false;
    }
  }

  // Mark all messages as read
  Future<void> _markMessagesAsRead(String tripId) async {
    try {
      // Get all unread messages from the user
      final unreadMessages = await _firestore
          .collection('tripMessages')
          .where('tripId', isEqualTo: tripId)
          .where('sender',
              isEqualTo: ChatMessage.senderToString(MessageSender.user))
          .where('isRead', isEqualTo: false)
          .get();

      // Create a batch to update all messages at once
      final batch = _firestore.batch();

      for (final doc in unreadMessages.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      // Commit the batch
      await batch.commit();
    } catch (e) {
      print(e);
      errorMessage.value = "Error marking messages as read: $e";
    }
  }
}
