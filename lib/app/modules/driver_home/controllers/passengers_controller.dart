import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/passenger_model.dart';
import '../views/widgets/add_passenger_bottom_sheet.dart';

class PassengersController extends GetxController {
  RxList<Passenger> passengers = <Passenger>[].obs;
  RxList<Passenger> selectedPassengers = <Passenger>[].obs;
  RxBool isLoading = false.obs;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  void onInit() {
    super.onInit();
    fetchPassengers();
  }

  /// Fetch all passengers from the user's document
  Future<void> fetchPassengers() async {
    try {
      isLoading.value = true;
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final docSnapshot = await _firestore.collection('users').doc(userId).get();
      if (!docSnapshot.exists || docSnapshot.data() == null) return;

      final data = docSnapshot.data()!;
      if (data.containsKey('passengers') && data['passengers'] is List) {
        passengers.value = (data['passengers'] as List)
            .map((json) => Passenger.fromJson(json))
            .toList();
      }
    } catch (e) {
      print('Error fetching passengers: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Add a new passenger
  Future<void> addPassenger(Passenger passenger) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final docRef = _firestore.collection('users').doc(userId);
      final docSnapshot = await docRef.get();
      List<dynamic> existingPassengers = docSnapshot.exists
          ? (docSnapshot.data()?['passengers'] ?? [])
          : [];

      existingPassengers.add(passenger.toJson());

      await docRef.update({'passengers': existingPassengers});
      passengers.add(passenger);

      Get.snackbar('success'.tr, 'passenger_added'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white);
    } catch (e) {
      Get.snackbar('error'.tr, 'add_passenger_error'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
    }
  }

  /// Update an existing passenger
  Future<void> updatePassenger(Passenger oldPassenger, Passenger newPassenger) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final docRef = _firestore.collection('users').doc(userId);
      final docSnapshot = await docRef.get();
      List<dynamic> existingPassengers = docSnapshot.exists
          ? (docSnapshot.data()?['passengers'] ?? [])
          : [];

      int index = existingPassengers.indexWhere(
              (p) => p['documentNumber'] == oldPassenger.documentNumber);

      if (index != -1) {
        existingPassengers[index] = newPassenger.toJson();
        await docRef.update({'passengers': existingPassengers});
        passengers[index] = newPassenger;

        Get.snackbar('success'.tr, 'passenger_updated'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white);
      } else {
        Get.snackbar('error'.tr, 'passenger_not_found'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white);
      }
    } catch (e) {
      Get.snackbar('error'.tr, 'update_passenger_error'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
    }
  }

  /// Delete a passenger
  Future<void> deletePassenger(Passenger passenger) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final docRef = _firestore.collection('users').doc(userId);
      final docSnapshot = await docRef.get();
      List<dynamic> existingPassengers = docSnapshot.exists
          ? (docSnapshot.data()?['passengers'] ?? [])
          : [];

      existingPassengers.removeWhere((p) => p['documentNumber'] == passenger.documentNumber);

      await docRef.update({'passengers': existingPassengers});
      passengers.removeWhere((p) => p.documentNumber == passenger.documentNumber);

      Get.snackbar('success'.tr, 'passenger_deleted'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white);
    } catch (e) {
      Get.snackbar('error'.tr, 'delete_passenger_error'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
    }
  }

  void editPassenger(Passenger passenger) {
    Get.bottomSheet(
      AddPassengerBottomSheet(passengerToEdit: passenger),
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }
}
