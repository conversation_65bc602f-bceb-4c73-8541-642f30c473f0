import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/passengers_controller.dart';
import '../../../data/models/passenger_model.dart';
import 'widgets/add_passenger_bottom_sheet.dart';

class SelectPassengersView extends GetView<PassengersController> {
  final Function(List<Passenger>) onPassengersSelected;

  const SelectPassengersView({
    Key? key,
    required this.onPassengersSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('select_passengers'.tr),
        actions: [
          // Select All Button
          Obx(() {
            final allSelected = controller.passengers.isNotEmpty &&
                controller.selectedPassengers.length == controller.passengers.length;
            return IconButton(
              icon: Icon(
                allSelected ? Icons.check_box : Icons.check_box_outline_blank,
                color: allSelected ? Theme.of(context).primaryColor : Colors.grey,
              ),
              onPressed: () {
                if (allSelected) {
                  controller.selectedPassengers.clear();
                } else {
                  controller.selectedPassengers.value = List.from(controller.passengers);
                }
              },
            );
          }),
        ],
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : controller.passengers.isEmpty
                ? Center(
                    child: Text(
                      'no_passengers'.tr,
                      style: const TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    itemCount: controller.passengers.length,
                    padding: const EdgeInsets.all(16),
                    itemBuilder: (context, index) {
                      final passenger = controller.passengers[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: Obx(
                          () => CheckboxListTile(
                            value: controller.selectedPassengers
                                .contains(passenger),
                            onChanged: (bool? value) {
                              if (value == true) {
                                controller.selectedPassengers.add(passenger);
                              } else {
                                controller.selectedPassengers
                                    .remove(passenger);
                              }
                            },
                            title: Text(
                              passenger.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: Text(
                              '${passenger.documentType.tr} • ${passenger.documentNumber}',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                            secondary: CircleAvatar(
                              backgroundColor: Theme.of(context).primaryColor,
                              child: Text(
                                passenger.name[0].toUpperCase(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Add Passenger Button
          FloatingActionButton(
            heroTag: 'add_passenger',
            onPressed: () {
              Get.bottomSheet(
                const AddPassengerBottomSheet(),
                isScrollControlled: true,
                backgroundColor: Colors.white,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
              );
            },
            child: const Icon(Icons.add),
          ),
          const SizedBox(height: 16),
          // Confirm Selection Button
          Obx(
            () => controller.selectedPassengers.isNotEmpty
                ? FloatingActionButton.extended(
                    heroTag: 'confirm_selection',
                    onPressed: () {
                      onPassengersSelected(controller.selectedPassengers);
                      // Close any active snackbars before navigation
                      if (Get.isSnackbarOpen) {
                        Get.closeAllSnackbars();
                      }
                      Get.back();
                    },
                    icon: const Icon(Icons.check),
                    label: Text(
                      'select_n_passengers'
                          .trParams({'count': '${controller.selectedPassengers.length}'}),
                    ),
                  )
                : const SizedBox(),
          ),
        ],
      ),
    );
  }
}
