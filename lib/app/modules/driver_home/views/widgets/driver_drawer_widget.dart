import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DriverDrawerWidget extends StatelessWidget {
  const DriverDrawerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = FirebaseAuth.instance.currentUser;
    
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(user?.displayName ?? 'Driver'),
            accountEmail: Text(user?.email ?? user?.phoneNumber ?? 'No Email'),
            currentAccountPicture: CircleAvatar(
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
              backgroundImage: user?.photoURL != null
                  ? NetworkImage(user!.photoURL!)
                  : null,
              child: user?.photoURL == null
                  ? Icon(
                      Icons.person,
                      size: 40,
                      color: theme.colorScheme.primary,
                    )
                  : null,
            ),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
            ),
          ),
          
          // Profile
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('الملف الشخصي'),
            onTap: () {
              Get.back(); // Close drawer
              Get.toNamed(Routes.PROFILE);
            },
          ),
          
          // Wallet
          ListTile(
            leading: const Icon(Icons.account_balance_wallet),
            title: const Text('المحفظة'),
            onTap: () {
              Get.back(); // Close drawer
              Get.toNamed(Routes.WALLET);
            },
          ),
          
          // Trip History
          ListTile(
            leading: const Icon(Icons.history),
            title: const Text('سجل الرحلات'),
            onTap: () {
              Get.back(); // Close drawer
              Get.toNamed(Routes.USER_TRIPS);
            },
          ),
          
          const Divider(),
          
          // Help Center
          ListTile(
            leading: const Icon(Icons.help),
            title: const Text('مركز المساعدة'),
            onTap: () {
              Get.back(); // Close drawer
              Get.toNamed(Routes.HELP_CENTER);
            },
          ),
          
          // Support Chat
          ListTile(
            leading: const Icon(Icons.support_agent),
            title: const Text('الدعم الفني'),
            onTap: () {
              Get.back(); // Close drawer
              Get.toNamed(Routes.SUPPORT_CHAT);
            },
          ),
          
          // FAQ
          ListTile(
            leading: const Icon(Icons.question_answer),
            title: const Text('الأسئلة الشائعة'),
            onTap: () {
              Get.back(); // Close drawer
              Get.toNamed(Routes.FAQ);
            },
          ),
          
          const Divider(),
          
          // Logout
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('تسجيل الخروج'),
            onTap: () async {
              Get.back(); // Close drawer
              await FirebaseAuth.instance.signOut();
              Get.offAllNamed(Routes.LOGIN);
            },
          ),
        ],
      ),
    );
  }
}
