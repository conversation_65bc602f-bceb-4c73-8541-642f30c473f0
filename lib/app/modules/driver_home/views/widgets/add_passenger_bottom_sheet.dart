import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../data/models/passenger_model.dart';
import '../../controllers/passengers_controller.dart';

class AddPassengerBottomSheet extends StatefulWidget {
  final Passenger? passengerToEdit;

  const AddPassengerBottomSheet({Key? key, this.passengerToEdit}) : super(key: key);

  @override
  State<AddPassengerBottomSheet> createState() => _AddPassengerBottomSheetState();
}

class _AddPassengerBottomSheetState extends State<AddPassengerBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _documentNumberController = TextEditingController();
  final _countryController = TextEditingController();
  String _selectedDocumentType = 'passport';
  String _selectedGender = 'male';

  final List<String> _documentTypes = ['passport', 'id_card', 'drivers_license'];
  final List<String> _genders = ['male', 'female', 'other'];

  @override
  void initState() {
    super.initState();
    if (widget.passengerToEdit != null) {
      _nameController.text = widget.passengerToEdit!.name;
      _documentNumberController.text = widget.passengerToEdit!.documentNumber;
      _countryController.text = widget.passengerToEdit!.country;
      _selectedDocumentType = widget.passengerToEdit!.documentType;
      _selectedGender = widget.passengerToEdit!.gender;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _documentNumberController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PassengersController>();
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.passengerToEdit == null
                      ? 'add_passenger'.tr
                      : 'edit_passenger'.tr,
                  style: theme.textTheme.titleLarge!.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Form
            Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name Field
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'name'.tr,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.person),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'name_required'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Document Type Dropdown
                  DropdownButtonFormField<String>(
                    value: _selectedDocumentType,
                    decoration: InputDecoration(
                      labelText: 'document_type'.tr,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.article),
                    ),
                    items: _documentTypes
                        .map((type) => DropdownMenuItem(
                              value: type,
                              child: Text(type.tr),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedDocumentType = value;
                        });
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'document_type_required'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Document Number Field
                  TextFormField(
                    controller: _documentNumberController,
                    decoration: InputDecoration(
                      labelText: 'document_number'.tr,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.numbers),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'document_number_required'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Country Field
                  TextFormField(
                    controller: _countryController,
                    decoration: InputDecoration(
                      labelText: 'country'.tr,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.flag),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'country_required'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Gender Dropdown
                  DropdownButtonFormField<String>(
                    value: _selectedGender,
                    decoration: InputDecoration(
                      labelText: 'gender'.tr,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.person_outline),
                    ),
                    items: _genders
                        .map((gender) => DropdownMenuItem(
                              value: gender,
                              child: Text(gender.tr),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedGender = value;
                        });
                      }
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'gender_required'.tr;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // Submit Button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          final passenger = Passenger(
                            name: _nameController.text.trim(),
                            documentNumber: _documentNumberController.text.trim(),
                            country: _countryController.text.trim(),
                            documentType: _selectedDocumentType,
                            gender: _selectedGender,
                          );

                          if (widget.passengerToEdit == null) {
                            controller.addPassenger(passenger);
                          } else {
                            controller.updatePassenger(
                                widget.passengerToEdit!, passenger);
                          }
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        widget.passengerToEdit == null
                            ? 'add'.tr
                            : 'update'.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
