import 'package:ai_delivery_app/app/modules/driver_home/views/widgets/driver_drawer_widget.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/widgets/home_drawer_widget.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../data/models/trip_request_model.dart';
import '../controllers/driver_chat_controller.dart';
import '../controllers/driver_home_controller.dart'; // Controller
import 'driver_chat_view.dart';

class DriverHomeView extends GetView<DriverHomeController> {
  const DriverHomeView({super.key});

  // Helper method to format scheduled time
  String _formatScheduledTime(Timestamp timestamp) {
    final dateTime = timestamp.toDate();
    final day = dateTime.day.toString().padLeft(2, '0');
    final month = dateTime.month.toString().padLeft(2, '0');
    final year = dateTime.year;
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$day/$month/$year at $hour:$minute';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
        appBar: AppBar(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(16),
            ),
          ),
          title: Obx(() => Row(
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: controller.isOnline.value ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                controller.isOnline.value
                  ? 'you_are_online'.tr
                  : 'you_are_offline'.tr,
                style: const TextStyle(color: Colors.white),
              ),
            ],
          )),
          actions: [
            // Logout Button
            IconButton(
              icon: const Icon(Icons.logout),
              tooltip: 'logout'.tr,
              onPressed: controller.logout,
            ),
          ],
        ),
        body: Obx(() {
          // Rebuild body based on reactive state changes
          return Stack(
            children: [
              // --- Google Map ---
              // Show map if driver location is available
              if (controller.driverLocation.value != null)
                Obx(() => GoogleMap(
                      onMapCreated: controller.onMapCreated,
                      initialCameraPosition: CameraPosition(
                        target: controller
                            .driverLocation.value!, // Start centered on driver
                        zoom: 15.0,
                      ),
                      markers: controller
                          .mapMarkers, // Driver, pickup, destination markers
                      polylines: controller.mapPolylines, // Route lines
                      myLocationEnabled: false, // Use custom marker
                      myLocationButtonEnabled: true, // Button to recenter map
                      padding: EdgeInsets.only(
                        // Adjust padding based on bottom sheet height
                        bottom: _getMapBottomPadding(context),
                        top: 60, // Padding for the online/offline toggle
                      ),
                      zoomControlsEnabled: false,
                    ))
              // Show loading or message if location isn't ready yet
              else
                Center(child: Text('initializing_map'.tr)),

              // --- Online/Offline Toggle ---
              // Positioned at the top center
              Positioned(
                top: 16,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Obx(() => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  controller.isOnline.value ? 'online'.tr : 'offline'.tr,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: controller.isOnline.value
                                        ? Colors.green.shade800
                                        : Colors.red.shade800,
                                  ),
                                ),
                                Text(
                                  controller.isOnline.value
                                      ? 'receiving_ride_requests'.tr
                                      : 'not_receiving_requests'.tr,
                                  style: theme.textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                          // Custom toggle switch
                          GestureDetector(
                            onTap: () {
                              if (!controller.isLoading.value) {
                                controller.toggleOnlineStatus(!controller.isOnline.value);
                              }
                            },
                            child: Container(
                              width: 70,
                              height: 36,
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                color: controller.isOnline.value 
                                    ? Colors.green.shade100 
                                    : Colors.red.shade100,
                              ),
                              child: Row(
                                mainAxisAlignment: controller.isOnline.value 
                                    ? MainAxisAlignment.end 
                                    : MainAxisAlignment.start,
                                children: [
                                  Container(
                                    width: 28,
                                    height: 28,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: controller.isOnline.value 
                                          ? Colors.green 
                                          : Colors.red,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.1),
                                          blurRadius: 4,
                                          spreadRadius: 1,
                                        ),
                                      ],
                                    ),
                                    child: controller.isLoading.value
                                      ? SizedBox(
                                          width: 12,
                                          height: 12,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white,
                                          ),
                                        )
                                      : Icon(
                                          controller.isOnline.value 
                                              ? Icons.check 
                                              : Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
                  ),
                ),
              ),

              // --- Bottom Information Panels ---
              // Conditionally display Incoming Request or Active Trip UI
              _buildBottomInfoPanel(context, theme),

              // --- Global Loading Indicator (for accepting etc.) ---
              if (controller
                  .isLoading.value) // Show if any specific action is loading
                Positioned.fill(
                  child: Container(
                    color: Colors.black.withOpacity(0.3),
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                ),

              // --- Error Snackbar Trigger ---
              if (controller.errorMessage.value != null)
                _showErrorSnackbar(controller.errorMessage.value!, theme),
            ],
          );
        }),
        drawer: const HomeDrawerWidget(),
      
    );
  }

  // === Helper Widgets for UI Sections ===

  // Determine padding needed for map based on overlays
  double _getMapBottomPadding(BuildContext context) {
    // Simple check: if either overlay is potentially visible, add padding
    if (controller.incomingRideRequest.value != null ||
        controller.currentAssignedTrip.value != null) {
      return 250; // Estimate height of the bottom panels
    }
    return 50; // Minimal padding otherwise
  }

  // Helper to build the bottom panel based on controller state
  Widget _buildBottomInfoPanel(BuildContext context, ThemeData theme) {
    return Obx(() {
      // Show incoming ride request panel
      if (controller.incomingRideRequest.value != null) {
        return _buildIncomingRequestCard(
            context, theme, controller.incomingRideRequest.value!);
      }

      // Show active trip panel
      if (controller.currentAssignedTrip.value != null) {
        return _buildActiveTripCard(
            context, theme, controller.currentAssignedTrip.value!);
      }

      // Show waiting for requests message when online but no active requests
      if (controller.isOnline.value) {
        return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.search,
                      color: Colors.green.shade600,
                      size: 30,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'waiting_for_ride_requests'.tr,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade800,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You will be notified when a new ride request is available',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  // Animated pulse effect
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.green.shade400),
                    ),
                  ),
                ],
              ),
            ),
          );
      }

      // Default: no bottom panel when offline
      return const SizedBox.shrink();
    });
  }

  // --- UI: Incoming Ride Request Card ---
  Widget _buildIncomingRequestCard(
      BuildContext context, ThemeData theme, TripRequest request) {
    // TODO: Calculate estimated distance/time from driver to pickup
    String distanceToPickup = 'calculating'.tr; // Placeholder
    String timeToPickup = 'few_mins'.tr; // Placeholder

    return DraggableScrollableSheet(
      initialChildSize: 0.4, // Start with 40% of screen height
      minChildSize: 0.2, // Allow collapsing to 20%
      maxChildSize: 0.9, // Allow expanding to 90%
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Drag handle
                Center(
                  child: Container(
                    margin: const EdgeInsets.only(top: 8, bottom: 12),
                    width: 40,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text('new_ride_request'.tr,
                          style: theme.textTheme.headlineSmall?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center),
                      const Divider(height: 20),
                      
                      // Price and trip details card
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              theme.colorScheme.primary.withOpacity(0.1),
                              theme.colorScheme.primary.withOpacity(0.05),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.primary.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Price row
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'total'.tr,
                                  style: theme.textTheme.titleMedium!.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary.withOpacity(0.15),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    '\$${request.totalPrice.toStringAsFixed(2)}',
                                    style: theme.textTheme.titleLarge!.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            // Distance and duration row
                            if (request.distanceText != null && request.durationText != null)
                              Container(
                                padding: const EdgeInsets.symmetric(vertical: 8),
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: BorderSide(color: theme.colorScheme.primary.withOpacity(0.1), width: 1),
                                    bottom: BorderSide(color: theme.colorScheme.primary.withOpacity(0.1), width: 1),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withOpacity(0.1),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(Icons.route, size: 16, color: Colors.blue.shade700),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          request.distanceText!,
                                          style: theme.textTheme.bodyMedium!.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.amber.withOpacity(0.1),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(Icons.access_time, size: 16, color: Colors.amber.shade700),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          request.durationText!,
                                          style: theme.textTheme.bodyMedium!.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            const SizedBox(height: 12),
                            // Payment method
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.1),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    request.paymentMethod == 'cash'
                                        ? Icons.money
                                        : request.paymentMethod == 'wallet'
                                            ? Icons.account_balance_wallet
                                            : Icons.credit_card,
                                    size: 16,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  request.paymentMethod.tr,
                                  style: theme.textTheme.bodyMedium!.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Location cards container
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Column(
                          children: [
                            // Pickup Info
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                children: [
                                  Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: Colors.orange.shade50,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.location_on_outlined,
                                      color: Colors.orange.shade700,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'pickup'.trParams({'s': request.directionTitle}),
                                          style: theme.textTheme.titleMedium!.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'approx_distance_away'.trParams({'s': distanceToPickup, 's1': timeToPickup}),
                                          style: theme.textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    onPressed: () => controller.navigateToLocation(request.pickupLocation),
                                    icon: const Icon(Icons.navigation_outlined, size: 16),
                                    label: Text('Navigate'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue.shade100,
                                      foregroundColor: Colors.blue.shade800,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                      textStyle: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            // Divider with direction arrow
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Row(
                                children: [
                                  const SizedBox(width: 20),
                                  Container(
                                    height: 30,
                                    width: 1,
                                    color: Colors.grey.shade300,
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Divider(color: Colors.grey.shade300),
                                  ),
                                ],
                              ),
                            ),
                            
                            // Destination Info
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                children: [
                                  Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade50,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.flag_outlined,
                                      color: Colors.green.shade700,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'destination'.trParams({'s': request.directionTitle}),
                                          style: theme.textTheme.titleMedium!.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'dropoff'.trParams({
                                            's': '${request.dropoffLocation.latitude.toStringAsFixed(4)}, ${request.dropoffLocation.longitude.toStringAsFixed(4)}'
                                          }),
                                          style: theme.textTheme.bodySmall,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    onPressed: () => controller.navigateToLocation(request.dropoffLocation),
                                    icon: const Icon(Icons.navigation_outlined, size: 16),
                                    label: Text('Navigate'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green.shade100,
                                      foregroundColor: Colors.green.shade800,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                      textStyle: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Passenger and bags info
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                        child: Row(
                          children: [
                            Icon(Icons.person, size: 16, color: theme.colorScheme.primary),
                            const SizedBox(width: 5),
                            Text(
                              request.passengers > 1
                                  ? 'passenger_plural'.trParams({'s': request.passengers.toString()})
                                  : 'passenger_singular'.trParams({'s': request.passengers.toString()}),
                              style: theme.textTheme.bodyMedium,
                            ),
                            const SizedBox(width: 15),
                            Icon(Icons.luggage, size: 16, color: theme.colorScheme.primary),
                            const SizedBox(width: 5),
                            Text(
                              request.bags > 1
                                  ? 'bag_plural'.trParams({'s': request.bags.toString()})
                                  : 'bag_singular'.trParams({'s': request.bags.toString()}),
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                      
                      // Notes if available
                      if (request.notes.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(Icons.notes, size: 16, color: theme.colorScheme.primary),
                              const SizedBox(width: 5),
                              Expanded(
                                child: Text(
                                  'notes'.trParams({'s': request.notes}),
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ),
                            ],
                          ),
                        ),
                      
                      const SizedBox(height: 20),
                      
                      // Accept/Reject Buttons
                      Row(
                        children: [
                          // Reject Button
                          Expanded(
                            child: OutlinedButton.icon(
                              icon: const Icon(Icons.close_rounded),
                              label: Text('reject'.tr),
                              onPressed: controller.rejectRideRequest,
                              style: OutlinedButton.styleFrom(
                                foregroundColor: theme.colorScheme.error,
                                side: BorderSide(
                                    color: theme.colorScheme.error.withOpacity(0.7)),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          // Accept Button
                          Expanded(
                            flex: 2,
                            child: ElevatedButton.icon(
                              icon: const Icon(Icons.check_circle_outline_rounded),
                              label: Text('accept_ride'.tr),
                              onPressed: () => controller.acceptRideRequest(request.id),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(vertical: 14),
                                textStyle: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10)),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20), // Bottom padding for scrolling
              ],
            ),
          ),
        );
      },
    );
  }

  // --- UI: Active Trip Control Card ---
  Widget _buildActiveTripCard(
      BuildContext context, ThemeData theme, TripRequest trip) {
    String cardTitle = 'active_trip'.tr;
    String buttonText = "";
    IconData buttonIcon = Icons.navigation_rounded; // Default icon
    VoidCallback? buttonAction;
    Widget? tripDetailsWidget; // To show pickup/dropoff info

    // Determine button text and action based on current trip status
    switch (trip.status) {
      case TripStatus.assigned:
      case TripStatus.enRouteToPickup:
        cardTitle = 'navigate_to_pickup'.tr;
        buttonText = 'arrived_at_pickup'.tr;
        buttonIcon = Icons.location_on_outlined;
        buttonAction =
            () => controller.updateActiveTripStatus(TripStatus.arrivedAtPickup);
        tripDetailsWidget = _buildTripDetailRow(Icons.location_on_outlined,
            'pickup'.trParams({'s': trip.directionTitle}), Colors.orange.shade700);
        break;
      case TripStatus.arrivedAtPickup:
        cardTitle = 'waiting_for_passenger'.tr;
        buttonText = 'start_trip'.tr;
        buttonIcon = Icons.play_circle_outline_rounded;
        buttonAction =
            () => controller.updateActiveTripStatus(TripStatus.ongoing);
        tripDetailsWidget = _buildTripDetailRow(Icons.location_on_rounded,
            'you_have_arrived'.tr, theme.colorScheme.primary);
        break;
      case TripStatus.ongoing:
        cardTitle = 'navigate_to_destination'.tr;
        buttonText = 'complete_trip'.tr;
        buttonIcon = Icons.check_circle_outline_rounded;
        buttonAction =
            () => controller.updateActiveTripStatus(TripStatus.completed);
        tripDetailsWidget = _buildTripDetailRow(Icons.flag_outlined,
            'dropoff'.trParams({'s': trip.directionTitle}), theme.colorScheme.secondary);
        break;
      // Terminal states handled by listener resetting state, card shouldn't show long
      case TripStatus.completed:
      case TripStatus.cancelledByDriver:
      case TripStatus.cancelledByUser:
        return const SizedBox.shrink(); // Hide card if trip just ended
      default:
        cardTitle = 'trip_status'.trParams({'s': trip.status.toString().split('.').last});
        buttonText = 'update_status'.tr; // Fallback
        buttonAction = null; // Disable button for unknown states
        break;
    }

    return DraggableScrollableSheet(
      initialChildSize: 0.25, // Start with a smaller size
      minChildSize: 0.15, // Allow collapsing to just show the header
      maxChildSize: 0.5, // Prevent taking up too much space
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: ListView(
            controller: scrollController,
            padding: EdgeInsets.zero,
            children: [
              // Handle bar for dragging
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              
              // Header with app bar style
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                ),
                child: Row(
                  children: [
                    Icon(
                      trip.status == TripStatus.enRouteToPickup || trip.status == TripStatus.assigned
                          ? Icons.location_on_outlined
                          : trip.status == TripStatus.arrivedAtPickup
                              ? Icons.directions_car_outlined
                              : Icons.flag_outlined,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        cardTitle,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    // Distance and time if available
                    if (trip.distanceText != null && trip.durationText != null)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.timer, size: 14, color: Colors.white),
                            const SizedBox(width: 4),
                            Text(
                              trip.durationText!,
                              style: const TextStyle(color: Colors.white, fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              
              // Content padding
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Show trip pickup/dropoff details
                    if (tripDetailsWidget != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 12.0),
                        child: tripDetailsWidget,
                      ),
                    
                    // Trip progress indicator
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          // Pickup point
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.green, width: 2),
                            ),
                            child: const Icon(Icons.location_on, size: 14, color: Colors.green),
                          ),
                          // Progress line
                          Expanded(
                            child: Container(
                              height: 3,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.green,
                                    trip.status == TripStatus.ongoing ? Colors.blue : Colors.grey[300]!,
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Destination point
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.1),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: trip.status == TripStatus.ongoing ? Colors.blue : Colors.grey[400]!,
                                width: 2,
                              ),
                            ),
                            child: Icon(
                              Icons.flag,
                              size: 14,
                              color: trip.status == TripStatus.ongoing ? Colors.blue : Colors.grey[400]!,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Distance and time info
                    if (trip.distanceText != null && trip.durationText != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Row(
                          children: [
                            // Distance
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(Icons.route, size: 16, color: theme.colorScheme.primary),
                                  const SizedBox(width: 6),
                                  Text(
                                    trip.distanceText!,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Colors.grey[800],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Divider
                            Container(
                              height: 24,
                              width: 1,
                              color: Colors.grey[300],
                            ),
                            // Duration
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.access_time, size: 16, color: theme.colorScheme.primary),
                                  const SizedBox(width: 6),
                                  Text(
                                    trip.durationText!,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Colors.grey[800],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              
              // Action buttons container
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  border: Border(top: BorderSide(color: Colors.grey[200]!)),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Navigation and Chat buttons row
                    Row(
                      children: [
                        // Navigation Button
                        Expanded(
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.navigation_outlined, size: 18),
                            label: Text('navigate'.tr),
                            onPressed: () => controller.navigateToLocation(
                              trip.status == TripStatus.ongoing || trip.status == TripStatus.arrivedAtPickup
                                  ? trip.dropoffLocation
                                  : trip.pickupLocation,
                            ),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.blue[700],
                              side: BorderSide(color: Colors.blue[300]!),
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        // Chat Button
                        Expanded(
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.chat_outlined, size: 18),
                            label: Text('chat'.tr),
                            onPressed: () {
                              // Open chat view
                              final chatController = Get.find<DriverChatController>();
                              chatController.initializeChat(trip);
                              Get.to(() => const DriverChatView());
                            },
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.green[700],
                              side: BorderSide(color: Colors.green[300]!),
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Main Action Button (Arrived/Start/Complete)
                    ElevatedButton.icon(
                      icon: Icon(buttonIcon, size: 20),
                      label: Text(buttonText),
                      onPressed: buttonAction, // Will be null if no action defined
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        textStyle: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                        elevation: 0,
                      ),
                    ),

                    // Optional: Cancel Trip Button (maybe only allowed before 'ongoing'?)
                    if (trip.status == TripStatus.assigned ||
                        trip.status == TripStatus.enRouteToPickup)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: TextButton(
                          onPressed: () => controller.cancelTripRequest(
                              showMessage: true),
                          style: TextButton.styleFrom(
                            foregroundColor: theme.colorScheme.error,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                          child: Text('cancel_trip'.tr),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Helper to build a row for trip details card
  Widget _buildTripDetailRow(IconData icon, String text, Color iconColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, size: 16, color: iconColor),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  // Helper to build a detailed trip info card
  Widget _buildDetailedTripInfo(TripRequest trip, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trip title with scheduled indicator if needed
          Row(
            children: [
              Expanded(
                child: Text(
                  trip.directionTitle,
                  style: theme.textTheme.titleMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // Scheduled trip indicator
              if (trip.isScheduled)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.amber.shade700),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.schedule,
                          size: 14, color: Colors.amber.shade900),
                      const SizedBox(width: 4),
                      Text(
                        'scheduled'.tr,
                        style: theme.textTheme.bodySmall!.copyWith(
                          color: Colors.amber.shade900,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),

          // Scheduled time if applicable
          if (trip.isScheduled && trip.scheduledTime != null)
            Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 8),
              child: Text(
                'scheduled_for'.trParams({'s': _formatScheduledTime(trip.scheduledTime!)}),
                style: theme.textTheme.bodySmall!.copyWith(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),

          const Divider(height: 16),

          // Price information section
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'subtotal'.tr,
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      '\$${trip.basePrice.toStringAsFixed(2)}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'app_fee'.tr,
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      '\$${trip.appFee.toStringAsFixed(2)}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'tax'.tr,
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      '\$${trip.taxAmount.toStringAsFixed(2)}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
                if (trip.discountAmount > 0) ...[
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'discount'.tr,
                        style: theme.textTheme.bodyMedium!.copyWith(
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        '-\$${trip.discountAmount.toStringAsFixed(2)}',
                        style: theme.textTheme.bodyMedium!.copyWith(
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'total'.tr,
                      style: theme.textTheme.titleMedium!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '\$${trip.totalPrice.toStringAsFixed(2)}',
                      style: theme.textTheme.titleMedium!.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                // Payment method
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      trip.paymentMethod == 'cash'
                          ? Icons.money
                          : trip.paymentMethod == 'wallet'
                              ? Icons.account_balance_wallet
                              : Icons.credit_card,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 5),
                    Text(
                      trip.paymentMethod.tr,
                      style: theme.textTheme.bodySmall!.copyWith(
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Passenger and bags info
          Row(
            children: [
              Icon(Icons.person, size: 16, color: theme.colorScheme.primary),
              const SizedBox(width: 5),
              Text(
                trip.passengers > 1
                    ? 'passenger_plural'.trParams({'s': trip.passengers.toString()})
                    : 'passenger_singular'.trParams({'s': trip.passengers.toString()}),
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(width: 15),
              Icon(Icons.luggage, size: 16, color: theme.colorScheme.primary),
              const SizedBox(width: 5),
              Text(
                trip.bags > 1
                    ? 'bag_plural'.trParams({'s': trip.bags.toString()})
                    : 'bag_singular'.trParams({'s': trip.bags.toString()}),
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),

          // Notes if available
          if (trip.notes.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.notes, size: 16, color: theme.colorScheme.primary),
                const SizedBox(width: 5),
                Expanded(
                  child: Text(
                    'notes'.trParams({'s': trip.notes}),
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ],

          // Distance and duration if available
          if (trip.distanceText != null && trip.durationText != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.route, size: 16, color: theme.colorScheme.primary),
                const SizedBox(width: 5),
                Text(
                  'distance_duration'.trParams({'s': trip.distanceText!, 's1': trip.durationText!}),
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ],

          const Divider(height: 16),

          // Pickup location
          Row(
            children: [
              const Icon(Icons.location_on, size: 16, color: Colors.red),
              const SizedBox(width: 5),
              Expanded(
                child: Text(
                  'pickup_location'.trParams({
                    's': trip.pickupLocation.latitude.toStringAsFixed(4),
                    's1': trip.pickupLocation.longitude.toStringAsFixed(4)
                  }),
                  style: theme.textTheme.bodySmall,
                ),
              ),
            ],
          ),
          const SizedBox(height: 5),

          // Dropoff location
          Row(
            children: [
              const Icon(Icons.flag, size: 16, color: Colors.green),
              const SizedBox(width: 5),
              Expanded(
                child: Text(
                  'dropoff'.trParams({
                    's': '${trip.dropoffLocation.latitude.toStringAsFixed(4)}, ${trip.dropoffLocation.longitude.toStringAsFixed(4)}'
                  }),
                  style: theme.textTheme.bodySmall,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // --- Utility: Error Snackbar ---
  Widget _showErrorSnackbar(String message, ThemeData theme) {
    // Uses WidgetsBinding to show snackbar safely after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (Get.isSnackbarOpen)
        Get.closeCurrentSnackbar(); // Close previous snackbar first
      Get.snackbar(
        'Error',
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: theme.colorScheme.errorContainer,
        colorText: theme.colorScheme.onErrorContainer,
        margin: const EdgeInsets.all(12),
        borderRadius: 10,
        icon: Icon(Icons.error_outline_rounded, color: theme.colorScheme.error),
      );
      // Clear error message in controller after showing
      Future.delayed(const Duration(milliseconds: 50),
          () => controller.errorMessage.value = null);
    });
    return const SizedBox.shrink(); // Returns an empty widget
  }
} // End of DriverHomeView