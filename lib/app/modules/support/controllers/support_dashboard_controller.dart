import 'package:ai_delivery_app/app/data/models/support_chat_model.dart';
import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class SupportDashboardController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  final RxList<SupportTicket> tickets = <SupportTicket>[].obs;
  final RxList<SupportTicket> allTickets = <SupportTicket>[].obs;
  final RxString filterOption = 'new'.obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;

  // Support agent info
  final Rx<UserModel?> currentAgent = Rx<UserModel?>(null);

  // Statistics
  final RxInt newTickets = 0.obs;
  final RxInt openTickets = 0.obs;
  final RxInt pendingTickets = 0.obs;
  final RxInt resolvedTickets = 0.obs;
  final RxInt myAssignedTickets = 0.obs;

  @override
  void onInit() {
    super.onInit();
    loadAgentData();
    loadTickets();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadAgentData() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
          currentAgent.value = UserModel.fromFirestore(userDoc);
        }
      }
    } catch (e) {
      print('Error loading agent data: $e');
    }
  }

  Future<void> loadTickets() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      // Get all tickets
      final snapshot = await _firestore
          .collection('supportTickets')
          .orderBy('createdAt', descending: true)
          .get();

      final List<SupportTicket> ticketList = [];
      
      for (final doc in snapshot.docs) {
        final ticket = SupportTicket.fromFirestore(doc);
        ticketList.add(ticket);
      }

      // Store all tickets
      allTickets.value = ticketList;
      
      // Update statistics
      _updateStatistics(ticketList);
      
      // Apply filter
      _applyFilter();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل التذاكر: $e';
    } finally {
      isLoading.value = false;
    }
  }

  void _updateStatistics(List<SupportTicket> ticketList) {
    newTickets.value = ticketList.where((t) => t.status == 'new').length;
    openTickets.value = ticketList.where((t) => t.status == 'open').length;
    pendingTickets.value = ticketList.where((t) => t.status == 'pending').length;
    resolvedTickets.value = ticketList.where((t) => t.status == 'resolved').length;
    
    // Count tickets assigned to current agent
    if (currentAgent.value != null) {
      myAssignedTickets.value = ticketList
          .where((t) => t.assignedTo == currentAgent.value!.uid)
          .length;
    }
  }

  void _applyFilter() {
    List<SupportTicket> filteredList = List.from(allTickets);
    
    // Apply search filter if query exists
    if (searchQuery.value.isNotEmpty) {
      filteredList = filteredList
          .where((ticket) => ticket.subject.toLowerCase().contains(searchQuery.value.toLowerCase()))
          .toList();
    }
    
    // Apply status filter
    switch (filterOption.value) {
      case 'new':
        filteredList = filteredList.where((t) => t.status == 'new').toList();
        break;
      case 'open':
        filteredList = filteredList.where((t) => t.status == 'open').toList();
        break;
      case 'pending':
        filteredList = filteredList.where((t) => t.status == 'pending').toList();
        break;
      case 'resolved':
        filteredList = filteredList.where((t) => t.status == 'resolved').toList();
        break;
      case 'mine':
        if (currentAgent.value != null) {
          filteredList = filteredList
              .where((t) => t.assignedTo == currentAgent.value!.uid)
              .toList();
        }
        break;
      // 'all' case doesn't need filtering
    }
    
    tickets.value = filteredList;
  }

  void setFilter(String filter) {
    filterOption.value = filter;
    _applyFilter();
  }

  void search(String query) {
    searchQuery.value = query;
    _applyFilter();
  }

  Future<void> refreshData() async {
    await loadTickets();
  }

  void navigateToTicketDetail(String ticketId) {
    Get.toNamed(Routes.SUPPORT_TICKET_DETAIL, arguments: {'ticketId': ticketId});
  }

  Future<void> assignTicket(String ticketId) async {
    if (currentAgent.value == null) return;
    
    isLoading.value = true;
    
    try {
      await _firestore.collection('supportTickets').doc(ticketId).update({
        'assignedTo': currentAgent.value!.uid,
        'status': 'open',
        'lastUpdated': FieldValue.serverTimestamp(),
      });
      
      // Add system message about assignment
      await _addSystemMessage(
        ticketId,
        'تم تعيين التذكرة إلى ${currentAgent.value!.name}',
      );
      
      Get.snackbar(
        'تم التعيين',
        'تم تعيين التذكرة إليك بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      await loadTickets();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تعيين التذكرة: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تعيين التذكرة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _addSystemMessage(String ticketId, String message) async {
    try {
      await _firestore.collection('supportMessages').add({
        'ticketId': ticketId,
        'userId': 'system',
        'message': message,
        'sender': 'support',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      print('Error adding system message: $e');
    }
  }

  String formatDate(Timestamp timestamp) {
    final date = timestamp.toDate();
    return intl.DateFormat('yyyy/MM/dd HH:mm').format(date);
  }

  Color getStatusColor(String status) {
    switch (status) {
      case 'new':
        return Colors.blue;
      case 'open':
        return Colors.orange;
      case 'pending':
        return Colors.purple;
      case 'resolved':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String getStatusText(String status) {
    switch (status) {
      case 'new':
        return 'جديدة';
      case 'open':
        return 'مفتوحة';
      case 'pending':
        return 'قيد الانتظار';
      case 'resolved':
        return 'تم الحل';
      case 'closed':
        return 'مغلقة';
      default:
        return status;
    }
  }

  String getPriorityText(int priority) {
    switch (priority) {
      case 1:
        return 'منخفضة';
      case 2:
        return 'متوسطة';
      case 3:
        return 'عالية';
      default:
        return 'متوسطة';
    }
  }

  Color getPriorityColor(int priority) {
    switch (priority) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.red;
      default:
        return Colors.orange;
    }
  }
}
