import 'dart:async';

import 'package:ai_delivery_app/app/data/models/support_chat_model.dart';
import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class SupportTicketDetailController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  final TextEditingController messageController = TextEditingController();
  final RxList<SupportChatMessage> messages = <SupportChatMessage>[].obs;
  final Rx<SupportTicket?> ticket = Rx<SupportTicket?>(null);
  final Rx<UserModel?> ticketUser = Rx<UserModel?>(null);
  final Rx<UserModel?> currentAgent = Rx<UserModel?>(null);
  final RxBool isLoading = false.obs;
  final RxBool isSending = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  String? ticketId;
  StreamSubscription<DocumentSnapshot>? _ticketSubscription;
  StreamSubscription<QuerySnapshot>? _messagesSubscription;

  @override
  void onInit() {
    super.onInit();
    
    // Get ticketId from arguments
    if (Get.arguments != null && Get.arguments['ticketId'] != null) {
      ticketId = Get.arguments['ticketId'];
      loadTicket();
    } else {
      errorMessage.value = 'معرف التذكرة غير متوفر';
    }
    
    loadAgentData();
  }

  @override
  void onClose() {
    messageController.dispose();
    _ticketSubscription?.cancel();
    _messagesSubscription?.cancel();
    super.onClose();
  }

  Future<void> loadAgentData() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
          currentAgent.value = UserModel.fromFirestore(userDoc);
        }
      }
    } catch (e) {
      print('Error loading agent data: $e');
    }
  }

  Future<void> loadTicket() async {
    if (ticketId == null) return;
    
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      // Start listening to ticket changes
      _ticketSubscription?.cancel();
      _ticketSubscription = _firestore
          .collection('supportTickets')
          .doc(ticketId)
          .snapshots()
          .listen((snapshot) async {
        if (snapshot.exists) {
          ticket.value = SupportTicket.fromFirestore(snapshot);
          
          // Load user data
          if (ticket.value != null) {
            await loadUserData(ticket.value!.userId);
          }
          
          // Load messages
          loadMessages();
        } else {
          errorMessage.value = 'التذكرة غير موجودة';
        }
      }, onError: (e) {
        errorMessage.value = 'حدث خطأ أثناء تحميل التذكرة: $e';
      });
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل التذكرة: $e';
      isLoading.value = false;
    }
  }

  Future<void> loadUserData(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        ticketUser.value = UserModel.fromFirestore(userDoc);
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> loadMessages() async {
    if (ticketId == null) return;
    
    try {
      _messagesSubscription?.cancel();
      _messagesSubscription = _firestore
          .collection('supportMessages')
          .where('ticketId', isEqualTo: ticketId)
          .orderBy('timestamp', descending: true)
          .snapshots()
          .listen((snapshot) {
        messages.value = snapshot.docs
            .map((doc) => SupportChatMessage.fromFirestore(doc))
            .toList();
        isLoading.value = false;
      }, onError: (e) {
        errorMessage.value = 'حدث خطأ أثناء تحميل الرسائل: $e';
        isLoading.value = false;
      });
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الرسائل: $e';
      isLoading.value = false;
    }
  }

  Future<void> sendMessage() async {
    final content = messageController.text.trim();
    if (content.isEmpty || ticket.value == null || currentAgent.value == null) {
      return;
    }

    isSending.value = true;

    try {
      // Create message
      final message = SupportChatMessage(
        id: '',
        userId: currentAgent.value!.uid,
        message: content,
        sender: SupportMessageSender.support,
        timestamp: Timestamp.now(),
        isRead: false,
        ticketId: ticketId,
      );

      // Add to Firestore
      await _firestore.collection('supportMessages').add(
            message.toFirestore(),
          );

      // Update ticket status if needed
      if (ticket.value!.status == 'new' || ticket.value!.status == 'resolved' || ticket.value!.status == 'closed') {
        await updateTicketStatus('open');
      }

      // Update last updated timestamp
      await _firestore
          .collection('supportTickets')
          .doc(ticketId)
          .update({
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Clear message input
      messageController.clear();
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إرسال الرسالة: $e';
    } finally {
      isSending.value = false;
    }
  }

  Future<void> updateTicketStatus(String status) async {
    if (ticketId == null || currentAgent.value == null) return;
    
    try {
      await _firestore
          .collection('supportTickets')
          .doc(ticketId)
          .update({
        'status': status,
        'lastUpdated': FieldValue.serverTimestamp(),
        // Assign to current agent if not already assigned
        'assignedTo': FieldValue.serverTimestamp(),
      });
      
      // Add system message about status change
      await _addSystemMessage(
        'تم تغيير حالة التذكرة إلى ${getStatusText(status)}',
      );
      
      Get.snackbar(
        'تم التحديث',
        'تم تحديث حالة التذكرة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث حالة التذكرة: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة التذكرة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _addSystemMessage(String message) async {
    try {
      await _firestore.collection('supportMessages').add({
        'ticketId': ticketId,
        'userId': 'system',
        'message': message,
        'sender': 'support',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      print('Error adding system message: $e');
    }
  }

  String formatDate(Timestamp timestamp) {
    final date = timestamp.toDate();
    return intl.DateFormat('yyyy/MM/dd HH:mm').format(date);
  }

  String getStatusText(String status) {
    switch (status) {
      case 'new':
        return 'جديدة';
      case 'open':
        return 'مفتوحة';
      case 'pending':
        return 'قيد الانتظار';
      case 'resolved':
        return 'تم الحل';
      case 'closed':
        return 'مغلقة';
      default:
        return status;
    }
  }

  Color getStatusColor(String status) {
    switch (status) {
      case 'new':
        return Colors.blue;
      case 'open':
        return Colors.orange;
      case 'pending':
        return Colors.purple;
      case 'resolved':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String getPriorityText(int priority) {
    switch (priority) {
      case 1:
        return 'منخفضة';
      case 2:
        return 'متوسطة';
      case 3:
        return 'عالية';
      default:
        return 'متوسطة';
    }
  }

  Color getPriorityColor(int priority) {
    switch (priority) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.red;
      default:
        return Colors.orange;
    }
  }
}
