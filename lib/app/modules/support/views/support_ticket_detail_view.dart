import 'package:ai_delivery_app/app/data/models/support_chat_model.dart';
import 'package:ai_delivery_app/app/modules/support/controllers/support_ticket_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class SupportTicketDetailView extends GetView<SupportTicketDetailController> {
  const SupportTicketDetailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.ticket.value?.subject ?? 'تفاصيل التذكرة')),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) => controller.updateTicketStatus(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'open',
                child: Text('تحديث إلى: مفتوحة'),
              ),
              const PopupMenuItem(
                value: 'pending',
                child: Text('تحديث إلى: قيد الانتظار'),
              ),
              const PopupMenuItem(
                value: 'resolved',
                child: Text('تحديث إلى: تم الحل'),
              ),
              const PopupMenuItem(
                value: 'closed',
                child: Text('تحديث إلى: مغلقة'),
              ),
            ],
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  _buildTicketInfo(context),
                  Expanded(
                    child: _buildChatMessages(context),
                  ),
                  _buildMessageInput(context),
                ],
              ),
      ),
    );
  }

  Widget _buildTicketInfo(BuildContext context) {
    final theme = Theme.of(context);
    
    return Obx(
      () {
        if (controller.ticket.value == null) {
          return const SizedBox.shrink();
        }
        
        final ticket = controller.ticket.value!;
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      ticket.subject,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: controller.getStatusColor(ticket.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      controller.getStatusText(ticket.status),
                      style: TextStyle(
                        color: controller.getStatusColor(ticket.status),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.person, size: 16),
                  const SizedBox(width: 4),
                  Obx(
                    () => Text(
                      controller.ticketUser.value?.name ?? 'مستخدم',
                      style: theme.textTheme.bodySmall,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.email, size: 16),
                  const SizedBox(width: 4),
                  Obx(
                    () => Text(
                      controller.ticketUser.value?.email ?? '',
                      style: theme.textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.access_time, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    'تم الإنشاء: ${controller.formatDate(ticket.createdAt)}',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
              if (ticket.lastUpdated != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.update, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'آخر تحديث: ${controller.formatDate(ticket.lastUpdated!)}',
                      style: theme.textTheme.bodySmall,
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.priority_high,
                    size: 16,
                    color: controller.getPriorityColor(ticket.priority),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'الأولوية: ${controller.getPriorityText(ticket.priority)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: controller.getPriorityColor(ticket.priority),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildChatMessages(BuildContext context) {
    final theme = Theme.of(context);
    
    return Obx(
      () => controller.messages.isEmpty
          ? Center(
              child: Text(
                'لا توجد رسائل',
                style: theme.textTheme.bodyLarge,
              ),
            )
          : ListView.builder(
              reverse: true,
              padding: const EdgeInsets.all(16),
              itemCount: controller.messages.length,
              itemBuilder: (context, index) {
                final message = controller.messages[index];
                return _buildMessageBubble(context, message);
              },
            ),
    );
  }

  Widget _buildMessageBubble(BuildContext context, SupportChatMessage message) {
    final theme = Theme.of(context);
    final isUser = message.sender == SupportMessageSender.user;
    final isSystem = message.userId == 'system';
    final time = intl.DateFormat('HH:mm').format(message.timestamp.toDate());
    
    if (isSystem) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            message.message,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade700,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.start : MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) const Spacer(),
          
          // Avatar for user
          if (isUser)
            Container(
              margin: const EdgeInsets.only(right: 12),
              child: CircleAvatar(
                radius: 16,
                backgroundColor: Colors.orange,
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          
          // Message bubble
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isUser
                    ? Colors.grey.shade200
                    : theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(18),
                border: Border.all(
                  color: isUser
                      ? Colors.grey.shade300
                      : theme.colorScheme.primary.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message.attachmentUrl != null) ...[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        message.attachmentUrl!,
                        height: 150,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            height: 150,
                            width: double.infinity,
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 150,
                            width: double.infinity,
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: Icon(Icons.error),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                  Text(
                    message.message,
                    style: TextStyle(
                      color: isUser
                          ? Colors.black87
                          : theme.colorScheme.primary.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        time,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      if (!isUser && message.isRead)
                        const Icon(
                          Icons.done_all,
                          size: 12,
                          color: Colors.blue,
                        )
                      else if (!isUser)
                        Icon(
                          Icons.done,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Avatar for support agent
          if (!isUser)
            Container(
              margin: const EdgeInsets.only(left: 12),
              child: CircleAvatar(
                radius: 16,
                backgroundColor: theme.colorScheme.primary,
                child: const Icon(
                  Icons.support_agent,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          
          if (isUser) const Spacer(),
        ],
      ),
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.attach_file),
            onPressed: () {
              // TODO: Implement file attachment
            },
          ),
          Expanded(
            child: TextField(
              controller: controller.messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => controller.sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          Obx(
            () => controller.isSending.value
                ? const CircularProgressIndicator()
                : IconButton(
                    icon: const Icon(Icons.send),
                    color: theme.colorScheme.primary,
                    onPressed: controller.sendMessage,
                  ),
          ),
        ],
      ),
    );
  }
}
