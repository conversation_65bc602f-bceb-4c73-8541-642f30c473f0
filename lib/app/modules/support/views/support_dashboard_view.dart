import 'package:ai_delivery_app/app/data/models/support_chat_model.dart';
import 'package:ai_delivery_app/app/modules/support/controllers/support_dashboard_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SupportDashboardView extends GetView<SupportDashboardController> {
  const SupportDashboardView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم الدعم الفني'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
          ),
        ],
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  _buildStatsCards(context),
                  _buildFilterChips(context),
                  Expanded(
                    child: controller.tickets.isEmpty
                        ? Center(
                            child: Text(
                              'لا توجد تذاكر متاحة',
                              style: theme.textTheme.titleMedium,
                            ),
                          )
                        : ListView.builder(
                            itemCount: controller.tickets.length,
                            itemBuilder: (context, index) {
                              final ticket = controller.tickets[index];
                              return _buildTicketItem(context, ticket);
                            },
                          ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context) {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildStatCard(
            context,
            'جديدة',
            controller.newTickets.value.toString(),
            Colors.blue,
            Icons.fiber_new,
            () => controller.setFilter('new'),
          ),
          _buildStatCard(
            context,
            'مفتوحة',
            controller.openTickets.value.toString(),
            Colors.orange,
            Icons.lock_open,
            () => controller.setFilter('open'),
          ),
          _buildStatCard(
            context,
            'قيد الانتظار',
            controller.pendingTickets.value.toString(),
            Colors.purple,
            Icons.hourglass_empty,
            () => controller.setFilter('pending'),
          ),
          _buildStatCard(
            context,
            'تم حلها',
            controller.resolvedTickets.value.toString(),
            Colors.green,
            Icons.check_circle,
            () => controller.setFilter('resolved'),
          ),
          _buildStatCard(
            context,
            'المعينة لي',
            controller.myAssignedTickets.value.toString(),
            Colors.teal,
            Icons.person,
            () => controller.setFilter('mine'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String count,
    Color color,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 4,
        margin: const EdgeInsets.only(right: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withOpacity(0.3), width: 1),
        ),
        child: Container(
          width: 110,
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color),
              const SizedBox(height: 8),
              Text(
                count,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: color.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChips(BuildContext context) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(context, 'الكل', 'all'),
          _buildFilterChip(context, 'جديدة', 'new'),
          _buildFilterChip(context, 'مفتوحة', 'open'),
          _buildFilterChip(context, 'قيد الانتظار', 'pending'),
          _buildFilterChip(context, 'تم حلها', 'resolved'),
          _buildFilterChip(context, 'المعينة لي', 'mine'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context, String label, String value) {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.only(right: 8),
        child: FilterChip(
          label: Text(label),
          selected: controller.filterOption.value == value,
          onSelected: (selected) {
            if (selected) {
              controller.setFilter(value);
            }
          },
          backgroundColor: Colors.grey.shade200,
          selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
          checkmarkColor: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildTicketItem(BuildContext context, SupportTicket ticket) {
    final theme = Theme.of(context);
    final isAssigned = ticket.assignedTo != null;
    final isAssignedToMe = ticket.assignedTo == controller.currentAgent.value?.uid;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () => controller.navigateToTicketDetail(ticket.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      ticket.subject,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: controller.getStatusColor(ticket.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      controller.getStatusText(ticket.status),
                      style: TextStyle(
                        color: controller.getStatusColor(ticket.status),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    controller.formatDate(ticket.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.priority_high,
                    size: 16,
                    color: controller.getPriorityColor(ticket.priority),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    controller.getPriorityText(ticket.priority),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: controller.getPriorityColor(ticket.priority),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (isAssigned)
                    Row(
                      children: [
                        const Icon(Icons.person, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          isAssignedToMe ? 'معينة لك' : 'معينة لمسؤول آخر',
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    )
                  else
                    const SizedBox.shrink(),
                  if (!isAssigned)
                    ElevatedButton.icon(
                      onPressed: () => controller.assignTicket(ticket.id),
                      icon: const Icon(Icons.assignment_ind, size: 16),
                      label: const Text('تعيين لي'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                    )
                  else if (isAssignedToMe)
                    OutlinedButton.icon(
                      onPressed: () => controller.navigateToTicketDetail(ticket.id),
                      icon: const Icon(Icons.chat, size: 16),
                      label: const Text('الرد'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                    )
                  else
                    const SizedBox.shrink(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('بحث عن تذكرة'),
        content: TextField(
          controller: controller.searchController,
          decoration: const InputDecoration(
            hintText: 'أدخل موضوع التذكرة',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) => controller.search(value),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.searchController.clear();
              controller.search('');
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.search(controller.searchController.text);
              Navigator.pop(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }
}
