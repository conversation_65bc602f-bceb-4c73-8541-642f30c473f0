import 'package:ai_delivery_app/app/modules/profile/controllers/edit_profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditProfileView extends GetView<EditProfileController> {
  const EditProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('تعديل الملف الشخصي'),
          centerTitle: true,
          actions: [
            Obx(() => TextButton.icon(
              onPressed: controller.isLoading.value ? null : controller.updateProfile,
              icon: controller.isLoading.value 
                ? const SizedBox(
                    width: 16, 
                    height: 16, 
                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white)
                  ) 
                : const Icon(Icons.save),
              label: const Text('حفظ'),
            )),
          ],
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.errorMessage.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline,
                      size: 48, color: theme.colorScheme.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.loadUserData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Profile Image
                Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    Obx(() {
                      final hasSelectedImage =
                          controller.selectedImage.value != null;
                      final hasProfileImage =
                          controller.userModel.value?.profileImageUrl != null;

                      return CircleAvatar(
                        radius: 60,
                        backgroundColor:
                            theme.colorScheme.primary.withOpacity(0.1),
                        backgroundImage: hasSelectedImage
                            ? FileImage(controller.selectedImage.value!)
                            : (hasProfileImage
                                ? NetworkImage(controller
                                    .userModel.value!.profileImageUrl!)
                                : null),
                        child: (!hasSelectedImage && !hasProfileImage)
                            ? Icon(
                                Icons.person,
                                size: 60,
                                color: theme.colorScheme.primary,
                              )
                            : null,
                      );
                    }),

                    // Edit Image Button
                    Container(
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: Obx(() => controller.isImageUploading.value
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.camera_alt,
                                color: Colors.white)),
                        onPressed: controller.isImageUploading.value
                            ? null
                            : controller.pickImage,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Name Field
                TextFormField(
                  controller: controller.nameController,
                  decoration: InputDecoration(
                    labelText: 'الاسم',
                    hintText: 'أدخل اسمك الكامل',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.person),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: controller.nameController.text.isEmpty 
                        ? null 
                        : () => controller.nameController.clear(),
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال الاسم';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Phone Field
                TextFormField(
                  controller: controller.phoneController,
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف',
                    hintText: '05XXXXXXXX',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.phone),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: controller.phoneController.text.isEmpty 
                        ? null 
                        : () => controller.phoneController.clear(),
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                    helperText: 'مثال: 0512345678',
                  ),
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty && !controller.isValidPhoneNumber(value.trim())) {
                      return 'رقم الهاتف غير صحيح';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Address Field
                TextFormField(
                  controller: controller.addressController,
                  decoration: InputDecoration(
                    labelText: 'العنوان',
                    hintText: 'أدخل عنوانك',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.location_on),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: controller.addressController.text.isEmpty 
                        ? null 
                        : () => controller.addressController.clear(),
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                  maxLines: 2,
                  textInputAction: TextInputAction.next,
                ),

                const SizedBox(height: 16),

                // Driver-specific fields
                Obx(() => controller.isDriver.value
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Vehicle Information Title
                          Text(
                            'معلومات السيارة',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Car Make Field
                          TextFormField(
                            controller: controller.carMakeController,
                            decoration: InputDecoration(
                              labelText: 'ماركة السيارة',
                              hintText: 'مثال: تويوتا',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.directions_car),
                              filled: true,
                              fillColor: theme.colorScheme.surface,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.carMakeController.text.isEmpty 
                                  ? null 
                                  : () => controller.carMakeController.clear(),
                              ),
                            ),
                            textInputAction: TextInputAction.next,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال ماركة السيارة';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Car Model Field
                          TextFormField(
                            controller: controller.carModelController,
                            decoration: InputDecoration(
                              labelText: 'موديل السيارة',
                              hintText: 'مثال: كامري',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.car_rental),
                              filled: true,
                              fillColor: theme.colorScheme.surface,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.carModelController.text.isEmpty 
                                  ? null 
                                  : () => controller.carModelController.clear(),
                              ),
                            ),
                            textInputAction: TextInputAction.next,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال موديل السيارة';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Car Year Field
                          TextFormField(
                            controller: controller.carYearController,
                            decoration: InputDecoration(
                              labelText: 'سنة الصنع',
                              hintText: 'مثال: 2020',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.date_range),
                              filled: true,
                              fillColor: theme.colorScheme.surface,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.carYearController.text.isEmpty 
                                  ? null 
                                  : () => controller.carYearController.clear(),
                              ),
                              helperText: 'السنة من 1950 إلى ${DateTime.now().year}',
                            ),
                            keyboardType: TextInputType.number,
                            textInputAction: TextInputAction.next,
                            validator: (value) {
                              if (value != null && value.trim().isNotEmpty && !controller.isValidYear(value.trim())) {
                                return 'سنة الصنع غير صحيحة';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Car Color Field
                          TextFormField(
                            controller: controller.carColorController,
                            decoration: InputDecoration(
                              labelText: 'لون السيارة',
                              hintText: 'مثال: أبيض',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.color_lens),
                              filled: true,
                              fillColor: theme.colorScheme.surface,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.carColorController.text.isEmpty 
                                  ? null 
                                  : () => controller.carColorController.clear(),
                              ),
                            ),
                            textInputAction: TextInputAction.next,
                          ),

                          const SizedBox(height: 16),

                          // Car Plate Field
                          TextFormField(
                            controller: controller.carPlateController,
                            decoration: InputDecoration(
                              labelText: 'رقم اللوحة',
                              hintText: 'أدخل رقم لوحة السيارة',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.credit_card),
                              filled: true,
                              fillColor: theme.colorScheme.surface,
                              suffixIcon: IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: controller.carPlateController.text.isEmpty 
                                  ? null 
                                  : () => controller.carPlateController.clear(),
                              ),
                            ),
                            textInputAction: TextInputAction.done,
                            textCapitalization: TextCapitalization.characters,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال رقم لوحة السيارة';
                              }
                              return null;
                            },
                          ),
                        ],
                      )
                    : const SizedBox.shrink()),

                const SizedBox(height: 32),

                // Divider
                const Divider(height: 32),
                
                // Update Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    onPressed: controller.isLoading.value ? null : controller.updateProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    icon: Obx(() => controller.isLoading.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.save)),
                    label: const Text(
                      'حفظ التغييرات',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
              ],
            ),
          );
        }),
      ),
    );
  }
}
