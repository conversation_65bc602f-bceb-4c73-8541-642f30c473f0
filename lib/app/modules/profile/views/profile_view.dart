import 'package:ai_delivery_app/app/modules/profile/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الملف الشخصي'),
          centerTitle: true,
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (controller.errorMessage.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value!,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.loadUserData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }
          
          final user = controller.userModel.value;
          
          if (user == null) {
            return const Center(child: Text('لا توجد بيانات للمستخدم'));
          }
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile Header
                Center(
                  child: Column(
                    children: [
                      // Profile Image
                      CircleAvatar(
                        radius: 60,
                        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                        backgroundImage: user.profileImageUrl != null
                            ? NetworkImage(user.profileImageUrl!)
                            : null,
                        child: user.profileImageUrl == null
                            ? Icon(
                                Icons.person,
                                size: 60,
                                color: theme.colorScheme.primary,
                              )
                            : null,
                      ),
                      const SizedBox(height: 16),
                      
                      // User Name
                      Text(
                        user.name,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // User Email
                      Text(
                        controller.userEmail,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      
                      // User Rating
                      if (user.rating > 0) ...[
                        const SizedBox(height: 16),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.star, color: Colors.amber, size: 24),
                            const SizedBox(width: 8),
                            Text(
                              user.rating.toStringAsFixed(1),
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              ' (${user.ratingCount} تقييم)',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ],
                      
                      const SizedBox(height: 24),
                      
                      // Edit Profile Button
                      ElevatedButton.icon(
                        onPressed: () => controller.navigateToEditProfile(),
                        icon: const Icon(Icons.edit),
                        label: const Text('تعديل الملف الشخصي'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                const Divider(),
                const SizedBox(height: 16),
                
                // User Stats
                Text(
                  'إحصائيات المستخدم',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Stats Cards
                Row(
                  children: [
                    _buildStatCard(
                      context,
                      icon: Icons.directions_car,
                      title: 'الرحلات المكتملة',
                      value: user.completedTrips.toString(),
                    ),
                    const SizedBox(width: 16),
                    _buildStatCard(
                      context,
                      icon: Icons.account_balance_wallet,
                      title: 'رصيد المحفظة',
                      value: '${user.walletBalance.toStringAsFixed(2)} ر.س',
                    ),
                  ],
                ),
                
                const SizedBox(height: 32),
                const Divider(),
                const SizedBox(height: 16),
                
                // Account Information
                Text(
                  'معلومات الحساب',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Phone Number
                _buildInfoItem(
                  context,
                  icon: Icons.phone,
                  title: 'رقم الهاتف',
                  value: user.phoneNumber ?? 'غير متوفر',
                ),
                
                // Address
                _buildInfoItem(
                  context,
                  icon: Icons.location_on,
                  title: 'العنوان',
                  value: user.address ?? 'غير متوفر',
                ),
                
                // Join Date
                _buildInfoItem(
                  context,
                  icon: Icons.calendar_today,
                  title: 'تاريخ الانضمام',
                  value: controller.formatDate(user.createdAt?.toDate()),
                ),
                
                // Last Login
                _buildInfoItem(
                  context,
                  icon: Icons.access_time,
                  title: 'آخر تسجيل دخول',
                  value: controller.formatDate(user.lastLoginAt?.toDate()),
                ),
                
                const SizedBox(height: 32),
                const Divider(),
                const SizedBox(height: 16),
                
                // Preferences
                Text(
                  'التفضيلات',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Language
                _buildPreferenceItem(
                  context,
                  icon: Icons.language,
                  title: 'اللغة',
                  value: 'العربية',
                  onTap: () => controller.showLanguageSelector(),
                ),
                
                // Notifications
                _buildPreferenceItem(
                  context,
                  icon: Icons.notifications,
                  title: 'الإشعارات',
                  value: user.notificationsEnabled ? 'مفعلة' : 'معطلة',
                  onTap: () => controller.toggleNotifications(),
                ),
                
                const SizedBox(height: 32),
                
                // Vehicle Information (for drivers only)
                if (user.role == 'driver' && user.vehicleInfo != null) ...[
                  const Divider(),
                  const SizedBox(height: 16),
                  
                  Text(
                    'معلومات السيارة',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Car Make and Model
                  _buildInfoItem(
                    context,
                    icon: Icons.directions_car,
                    title: 'السيارة',
                    value: '${user.vehicleInfo!['make'] ?? ''} ${user.vehicleInfo!['model'] ?? ''}',
                  ),
                  
                  // Car Year and Color
                  _buildInfoItem(
                    context,
                    icon: Icons.color_lens,
                    title: 'الموديل واللون',
                    value: '${user.vehicleInfo!['year'] ?? ''} - ${user.vehicleInfo!['color'] ?? ''}',
                  ),
                  
                  // License Plate
                  _buildInfoItem(
                    context,
                    icon: Icons.confirmation_number,
                    title: 'رقم اللوحة',
                    value: '${user.vehicleInfo!['plateNumber'] ?? ''}',
                  ),
                ],
                
                const SizedBox(height: 32),
              ],
            ),
          );
        }),
      ),
    );
  }
  
  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: theme.colorScheme.primary),
            const SizedBox(height: 12),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: theme.colorScheme.primary),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  value,
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPreferenceItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: theme.colorScheme.primary),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium,
                  ),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.chevron_left),
          ],
        ),
      ),
    );
  }
}
