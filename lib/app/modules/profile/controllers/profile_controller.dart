import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class ProfileController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final Rx<UserModel?> userModel = Rx<UserModel?>(null);
  final RxBool isLoading = false.obs;
  final RxString errorMessage = RxString('');
  
  User? get currentUser => _auth.currentUser;
  String get userEmail => currentUser?.email ?? 'No Email';
  
  @override
  void onInit() {
    super.onInit();
    loadUserData();
  }
  
  Future<void> loadUserData() async {
    if (currentUser == null) {
      errorMessage.value = 'User not logged in';
      return;
    }
    
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final userDoc = await _firestore.collection('users').doc(currentUser!.uid).get();
      
      if (userDoc.exists) {
        userModel.value = UserModel.fromFirestore(userDoc);
      } else {
        errorMessage.value = 'User profile not found';
      }
    } catch (e) {
      errorMessage.value = 'Error loading user data: $e';
    } finally {
      isLoading.value = false;
    }
  }
  
  void navigateToEditProfile() {
    Get.toNamed(Routes.EDIT_PROFILE);
  }
  
  String formatDate(DateTime? date) {
    if (date == null) return 'غير متوفر';
    return intl.DateFormat('yyyy/MM/dd').format(date);
  }
  
  void showLanguageSelector() {
    Get.bottomSheet(
      Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'اختر اللغة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildLanguageOption('العربية', true),
              _buildLanguageOption('English', false),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildLanguageOption(String language, bool isSelected) {
    return InkWell(
      onTap: () {
        // TODO: Implement language change
        Get.back();
        Get.snackbar(
          'تغيير اللغة',
          'تم اختيار اللغة: $language',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Get.theme.colorScheme.primary : Colors.grey,
            ),
            const SizedBox(width: 16),
            Text(
              language,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> toggleNotifications() async {
    if (userModel.value == null) return;
    
    final currentValue = userModel.value!.notificationsEnabled;
    final newValue = !currentValue;
    
    isLoading.value = true;
    
    try {
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'notificationsEnabled': newValue,
      });
      
      // Update local model
      userModel.value = userModel.value!.copyWith(notificationsEnabled: newValue);
      
      Get.snackbar(
        'الإشعارات',
        newValue ? 'تم تفعيل الإشعارات' : 'تم تعطيل الإشعارات',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الإعدادات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
