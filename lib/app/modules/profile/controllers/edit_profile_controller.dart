import 'dart:io';
import 'package:ai_delivery_app/app/data/models/user_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class EditProfileController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  final Rx<UserModel?> userModel = Rx<UserModel?>(null);
  final RxBool isLoading = false.obs;
  final RxString errorMessage = RxString('');
  final RxBool isImageUploading = false.obs;
  final RxBool isDriver = false.obs;

  // Form controllers
  late TextEditingController nameController;
  late TextEditingController phoneController;
  late TextEditingController addressController;

  // Driver-specific form controllers
  late TextEditingController carMakeController;
  late TextEditingController carModelController;
  late TextEditingController carYearController;
  late TextEditingController carColorController;
  late TextEditingController carPlateController;

  // For image picking
  final ImagePicker _picker = ImagePicker();
  final Rx<File?> selectedImage = Rx<File?>(null);

  User? get currentUser => _auth.currentUser;

  @override
  void onInit() {
    super.onInit();
    nameController = TextEditingController();
    phoneController = TextEditingController();
    addressController = TextEditingController();

    // Initialize driver-specific controllers
    carMakeController = TextEditingController();
    carModelController = TextEditingController();
    carYearController = TextEditingController();
    carColorController = TextEditingController();
    carPlateController = TextEditingController();

    loadUserData();
  }

  @override
  void onClose() {
    nameController.dispose();
    phoneController.dispose();
    addressController.dispose();

    // Dispose driver-specific controllers
    carMakeController.dispose();
    carModelController.dispose();
    carYearController.dispose();
    carColorController.dispose();
    carPlateController.dispose();

    super.onClose();
  }

  Future<void> loadUserData() async {
    if (currentUser == null) {
      errorMessage.value = 'User not logged in';
      return;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      final userDoc =
          await _firestore.collection('users').doc(currentUser!.uid).get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        userModel.value = UserModel.fromFirestore(userDoc);

        // Check if user is a driver
        isDriver.value = userData['role'] == 'driver';

        // Set initial values for form controllers
        nameController.text = userModel.value?.name ?? '';
        phoneController.text = userData['phoneNumber'] ?? '';
        addressController.text = userData['address'] ?? '';

        // Set driver-specific fields if user is a driver
        if (isDriver.value) {
          final vehicleInfo =
              userData['vehicleInfo'] as Map<String, dynamic>? ?? {};
          carMakeController.text = vehicleInfo['make'] ?? '';
          carModelController.text = vehicleInfo['model'] ?? '';
          carYearController.text = vehicleInfo['year']?.toString() ?? '';
          carColorController.text = vehicleInfo['color'] ?? '';
          carPlateController.text = vehicleInfo['plateNumber'] ?? '';
        }
      } else {
        errorMessage.value = 'User profile not found';
      }
    } catch (e) {
      errorMessage.value = 'Error loading user data: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> pickImage() async {
    try {
      // Show a dialog to choose between camera and gallery
      await Get.dialog(
        Directionality(
          textDirection: TextDirection.rtl,
          child: AlertDialog(
            title: const Text('اختر مصدر الصورة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('الكاميرا'),
                  onTap: () async {
                    Get.back();
                    final XFile? image = await _picker.pickImage(
                      source: ImageSource.camera,
                      maxWidth: 800,
                      maxHeight: 800,
                      imageQuality: 85,
                    );
                    if (image != null) {
                      selectedImage.value = File(image.path);
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('معرض الصور'),
                  onTap: () async {
                    Get.back();
                    final XFile? image = await _picker.pickImage(
                      source: ImageSource.gallery,
                      maxWidth: 800,
                      maxHeight: 800,
                      imageQuality: 85,
                    );
                    if (image != null) {
                      selectedImage.value = File(image.path);
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الصورة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<String?> uploadImage() async {
    if (selectedImage.value == null) return null;

    isImageUploading.value = true;

    try {
      final storageRef =
          _storage.ref().child('profile_images/${currentUser!.uid}');
      final uploadTask = storageRef.putFile(selectedImage.value!);

      await uploadTask.whenComplete(() {});

      final downloadUrl = await storageRef.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء رفع الصورة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    } finally {
      isImageUploading.value = false;
    }
  }

  // Validate phone number format
  bool isValidPhoneNumber(String phone) {
    // Basic validation for Saudi phone numbers
    // Allows formats like: 05xxxxxxxx or 5xxxxxxxx
    final RegExp phoneRegex = RegExp(r'^(05|5)\d{8}$');
    return phoneRegex.hasMatch(phone);
  }

  // Validate email format
  bool isValidEmail(String email) {

    final RegExp emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  // Validate year format
  bool isValidYear(String year) {
    if (year.isEmpty) return true; // Optional field
    final RegExp yearRegex = RegExp(r'^\d{4}$');
    if (!yearRegex.hasMatch(year)) return false;
    
    final int? yearInt = int.tryParse(year);
    if (yearInt == null) return false;
    
    final int currentYear = DateTime.now().year;
    return yearInt >= 1950 && yearInt <= currentYear;
  }
  
  Future<void> updateProfile() async {
    if (currentUser == null) {
      errorMessage.value = 'User not logged in';
      return;
    }

    // Validate form
    String validationError = '';
    
    if (nameController.text.trim().isEmpty) {
      validationError = 'يرجى إدخال الاسم';
    } else if (phoneController.text.trim().isNotEmpty && !isValidPhoneNumber(phoneController.text.trim())) {
      validationError = 'رقم الهاتف غير صحيح';
    } else if (isDriver.value) {
      if (carMakeController.text.trim().isEmpty) {
        validationError = 'يرجى إدخال ماركة السيارة';
      } else if (carModelController.text.trim().isEmpty) {
        validationError = 'يرجى إدخال موديل السيارة';
      } else if (carPlateController.text.trim().isEmpty) {
        validationError = 'يرجى إدخال رقم لوحة السيارة';
      } else if (carYearController.text.trim().isNotEmpty && !isValidYear(carYearController.text.trim())) {
        validationError = 'سنة الصنع غير صحيحة';
      }
    }
    
    if (validationError.isNotEmpty) {
      Get.snackbar(
        'خطأ',
        validationError,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;

    try {
      // Upload image if selected
      String? imageUrl;
      if (selectedImage.value != null) {
        imageUrl = await uploadImage();
      }

      // Prepare data to update
      final Map<String, dynamic> updateData = {
        'name': nameController.text.trim(),
        'phoneNumber': phoneController.text.trim(),
        'address': addressController.text.trim(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Add image URL if available
      if (imageUrl != null) {
        updateData['profileImageUrl'] = imageUrl;
      }

      // Add driver-specific data if user is a driver
      if (isDriver.value) {
        // Add vehicle info - validation already done above
        updateData['vehicleInfo'] = {
          'make': carMakeController.text.trim(),
          'model': carModelController.text.trim(),
          'year': carYearController.text.trim().isNotEmpty
              ? int.tryParse(carYearController.text.trim())
              : null,
          'color': carColorController.text.trim(),
          'plateNumber': carPlateController.text.trim().toUpperCase(), // Convert plate to uppercase for consistency
        };
      }

      // Update Firestore
      await _firestore
          .collection('users')
          .doc(currentUser!.uid)
          .update(updateData);

      // Update display name in Firebase Auth
      await currentUser!.updateDisplayName(nameController.text.trim());

      Get.snackbar(
        'تم بنجاح',
        'تم تحديث الملف الشخصي بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Go back to profile page
      Get.back();
    } catch (e) {
      errorMessage.value = 'Error updating profile: $e';
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الملف الشخصي: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
