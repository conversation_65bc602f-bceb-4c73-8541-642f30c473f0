import 'package:ai_delivery_app/app/data/models/wallet_transaction_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum PaymentMethod {
  creditCard,
  applePay,
  bankTransfer,
}

class AddFundsController extends GetxController {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  final TextEditingController amountController = TextEditingController();
  final RxDouble currentBalance = 0.0.obs;
  final RxBool isLoading = false.obs;
  final Rx<PaymentMethod> selectedPaymentMethod = PaymentMethod.creditCard.obs;
  
  User? get currentUser => _auth.currentUser;
  
  @override
  void onInit() {
    super.onInit();
    loadCurrentBalance();
  }
  
  @override
  void onClose() {
    amountController.dispose();
    super.onClose();
  }
  
  Future<void> loadCurrentBalance() async {
    if (currentUser == null) {
      Get.snackbar(
        'خطأ',
        'يرجى تسجيل الدخول أولاً',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    isLoading.value = true;
    
    try {
      final userDoc = await _firestore.collection('users').doc(currentUser!.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        currentBalance.value = (userData['walletBalance'] ?? 0.0).toDouble();
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل بيانات المحفظة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void setAmount(double amount) {
    amountController.text = amount.toString();
  }
  
  void selectPaymentMethod(PaymentMethod method) {
    selectedPaymentMethod.value = method;
  }
  
  void validateAndProceed() {
    final amountText = amountController.text.trim();
    if (amountText.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال المبلغ',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال مبلغ صحيح',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    // Minimum amount check
    if (amount < 10) {
      Get.snackbar(
        'خطأ',
        'الحد الأدنى للإيداع هو 10 ر.س',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    // Maximum amount check
    if (amount > 5000) {
      Get.snackbar(
        'خطأ',
        'الحد الأقصى للإيداع هو 5000 ر.س',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    // Proceed based on payment method
    switch (selectedPaymentMethod.value) {
      case PaymentMethod.creditCard:
        _processCreditCardPayment(amount);
        break;
      case PaymentMethod.applePay:
        _processApplePayPayment(amount);
        break;
      case PaymentMethod.bankTransfer:
        _processBankTransferPayment(amount);
        break;
    }
  }
  
  void _processCreditCardPayment(double amount) {
    // In a real app, this would integrate with a payment gateway
    // For now, we'll simulate a successful payment
    _showCreditCardDialog(amount);
  }
  
  void _processApplePayPayment(double amount) {
    // In a real app, this would integrate with Apple Pay
    // For now, we'll simulate a successful payment
    _simulateSuccessfulPayment(amount, 'Apple Pay');
  }
  
  void _processBankTransferPayment(double amount) {
    // Show bank account details for manual transfer
    _showBankTransferDialog(amount);
  }
  
  void _showCreditCardDialog(double amount) {
    final TextEditingController cardNumberController = TextEditingController();
    final TextEditingController expiryController = TextEditingController();
    final TextEditingController cvvController = TextEditingController();
    final TextEditingController nameController = TextEditingController();
    
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('بيانات البطاقة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: cardNumberController,
                  decoration: const InputDecoration(
                    labelText: 'رقم البطاقة',
                    hintText: 'XXXX XXXX XXXX XXXX',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: expiryController,
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الانتهاء',
                          hintText: 'MM/YY',
                        ),
                        keyboardType: TextInputType.datetime,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextField(
                        controller: cvvController,
                        decoration: const InputDecoration(
                          labelText: 'CVV',
                          hintText: 'XXX',
                        ),
                        keyboardType: TextInputType.number,
                        obscureText: true,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'الاسم على البطاقة',
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                // Validate card details (simplified)
                if (cardNumberController.text.isEmpty ||
                    expiryController.text.isEmpty ||
                    cvvController.text.isEmpty ||
                    nameController.text.isEmpty) {
                  Get.snackbar(
                    'خطأ',
                    'يرجى إدخال جميع بيانات البطاقة',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                  return;
                }
                
                Get.back();
                // Simulate processing
                _simulateSuccessfulPayment(amount, 'بطاقة ائتمان');
              },
              child: const Text('تأكيد الدفع'),
            ),
          ],
        ),
      ),
    );
  }
  
  void _showBankTransferDialog(double amount) {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('تحويل بنكي'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'يرجى تحويل المبلغ إلى الحساب البنكي التالي:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              _buildBankDetail('اسم البنك', 'البنك الأهلي السعودي'),
              _buildBankDetail('اسم الحساب', 'شركة أركاب للنقل البري'),
              _buildBankDetail('رقم الحساب', '**********'),
              _buildBankDetail('رقم الآيبان', 'SA********************12'),
              const SizedBox(height: 16),
              const Text(
                'ملاحظات هامة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• يرجى كتابة رقم الهاتف في تفاصيل التحويل'),
              const Text('• سيتم إضافة الرصيد خلال 24 ساعة من التحويل'),
              const Text('• يرجى الاحتفاظ بإيصال التحويل'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                // Create a pending transaction
                _createPendingTransaction(amount, 'تحويل بنكي');
                
                Get.snackbar(
                  'تم إرسال الطلب',
                  'سيتم إضافة الرصيد بعد التأكد من التحويل',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              },
              child: const Text('تم التحويل'),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBankDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
  
  Future<void> _simulateSuccessfulPayment(double amount, String method) async {
    isLoading.value = true;
    
    // Show loading dialog
    Get.dialog(
      const Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري معالجة الدفع...'),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
    
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 2));
    
    try {
      // Create transaction
      final transaction = WalletTransaction(
        id: '', // Will be set by Firestore
        userId: currentUser!.uid,
        amount: amount,
        type: TransactionType.deposit,
        description: 'إيداع عبر $method',
        timestamp: Timestamp.now(),
        status: 'completed',
      );
      
      // Add transaction to Firestore
      await _firestore.collection('walletTransactions').add(
        transaction.toFirestore(),
      );
      
      // Update user's wallet balance
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'walletBalance': FieldValue.increment(amount),
      });
      
      // Update local balance
      currentBalance.value += amount;
      
      // Close loading dialog
      Get.back();
      
      // Show success message
      Get.snackbar(
        'تم بنجاح',
        'تمت إضافة $amount ر.س إلى رصيدك',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
      // Clear amount field
      amountController.clear();
      
      // Navigate back after a short delay
      await Future.delayed(const Duration(seconds: 1));
      Get.back();
    } catch (e) {
      // Close loading dialog
      Get.back();
      
      // Show error message
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء معالجة الدفع: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> _createPendingTransaction(double amount, String method) async {
    try {
      // Create transaction
      final transaction = WalletTransaction(
        id: '', // Will be set by Firestore
        userId: currentUser!.uid,
        amount: amount,
        type: TransactionType.deposit,
        description: 'إيداع عبر $method',
        timestamp: Timestamp.now(),
        status: 'pending',
      );
      
      // Add transaction to Firestore
      await _firestore.collection('walletTransactions').add(
        transaction.toFirestore(),
      );
      
      // Clear amount field
      amountController.clear();
      
      // Navigate back
      Get.back();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء المعاملة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
