import 'package:ai_delivery_app/app/modules/add_funds/controllers/add_funds_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddFundsView extends GetView<AddFundsController> {
  const AddFundsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إضافة رصيد'),
          centerTitle: true,
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Balance
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: theme.colorScheme.primary,
                        size: 32,
                      ),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الرصيد الحالي',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Text(
                            '${controller.currentBalance.value.toStringAsFixed(2)} ر.س',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Amount Input
                Text(
                  'المبلغ',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: controller.amountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'أدخل المبلغ',
                    prefixIcon: const Icon(Icons.attach_money),
                    suffixText: 'ر.س',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Quick Amount Buttons
                Text(
                  'إضافة سريعة',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildQuickAmountButton(context, 50),
                    const SizedBox(width: 8),
                    _buildQuickAmountButton(context, 100),
                    const SizedBox(width: 8),
                    _buildQuickAmountButton(context, 200),
                    const SizedBox(width: 8),
                    _buildQuickAmountButton(context, 500),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // Payment Method
                Text(
                  'طريقة الدفع',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Credit Card Option
                _buildPaymentMethodOption(
                  context,
                  icon: Icons.credit_card,
                  title: 'بطاقة ائتمان',
                  subtitle: 'Visa, Mastercard, Mada',
                  isSelected: controller.selectedPaymentMethod.value == PaymentMethod.creditCard,
                  onTap: () => controller.selectPaymentMethod(PaymentMethod.creditCard),
                ),
                
                // Apple Pay Option
                _buildPaymentMethodOption(
                  context,
                  icon: Icons.apple,
                  title: 'Apple Pay',
                  subtitle: 'الدفع باستخدام Apple Pay',
                  isSelected: controller.selectedPaymentMethod.value == PaymentMethod.applePay,
                  onTap: () => controller.selectPaymentMethod(PaymentMethod.applePay),
                ),
                
                // Bank Transfer Option
                _buildPaymentMethodOption(
                  context,
                  icon: Icons.account_balance,
                  title: 'تحويل بنكي',
                  subtitle: 'التحويل المباشر إلى حسابنا البنكي',
                  isSelected: controller.selectedPaymentMethod.value == PaymentMethod.bankTransfer,
                  onTap: () => controller.selectPaymentMethod(PaymentMethod.bankTransfer),
                ),
                
                const SizedBox(height: 32),
                
                // Add Funds Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: controller.validateAndProceed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'إضافة رصيد',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
  
  Widget _buildQuickAmountButton(BuildContext context, int amount) {
    final theme = Theme.of(context);
    
    return Expanded(
      child: InkWell(
        onTap: () => controller.setAmount(amount.toDouble()),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.primary),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '$amount ر.س',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
  
  Widget _buildPaymentMethodOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primary.withOpacity(0.1) : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? theme.colorScheme.primary : Colors.grey,
              size: 28,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? theme.colorScheme.primary : Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
}
