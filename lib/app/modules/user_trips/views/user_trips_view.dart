import 'package:ai_delivery_app/app/modules/user_trips/controllers/user_trips_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;

class UserTripsView extends GetView<UserTripsController> {
  const UserTripsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('رحلاتي'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => controller.loadTrips(),
              tooltip: 'تحديث',
            ),
          ],
        ),
        body: Column(
          children: [
            // Tab bar for filtering trips
            Container(
              color: theme.colorScheme.surface,
              child: TabBar(
                controller: controller.tabController,
                labelColor: theme.colorScheme.primary,
                unselectedLabelColor: Colors.grey,
                indicatorColor: theme.colorScheme.primary,
                tabs: const [
                  Tab(text: 'النشطة'),
                  Tab(text: 'المجدولة'),
                  Tab(text: 'السابقة'),
                ],
                onTap: (index) {
                  controller.currentTabIndex.value = index;
                },
              ),
            ),

            // Trip list
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.errorMessage.value != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          controller.errorMessage.value!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => controller.loadTrips(),
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                final trips = controller.currentTabIndex.value == 0
                    ? controller.activeTrips
                    : controller.currentTabIndex.value == 1
                        ? controller.scheduledTrips
                        : controller.pastTrips;

                if (trips.isEmpty) {
                  return _buildEmptyState(
                      context, controller.currentTabIndex.value);
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: trips.length,
                  itemBuilder: (context, index) {
                    return _buildTripCard(context, trips[index]);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, int tabIndex) {
    final theme = Theme.of(context);
    String message;
    IconData icon;

    switch (tabIndex) {
      case 0:
        message = 'لا توجد رحلات نشطة حالياً';
        icon = Icons.directions_car_outlined;
        break;
      case 1:
        message = 'لا توجد رحلات مجدولة';
        icon = Icons.schedule_outlined;
        break;
      case 2:
        message = 'لا توجد رحلات سابقة';
        icon = Icons.history_outlined;
        break;
      default:
        message = 'لا توجد رحلات';
        icon = Icons.directions_car_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          if (tabIndex == 0 || tabIndex == 1)
            ElevatedButton.icon(
              onPressed: () => Get.back(),
              icon: const Icon(Icons.add),
              label: const Text('طلب رحلة جديدة'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTripCard(BuildContext context, Map<String, dynamic> trip) {
    final theme = Theme.of(context);
    final isActive = trip['status'] == 'searching' ||
        trip['status'] == 'assigned' ||
        trip['status'] == 'enRouteToPickup' ||
        trip['status'] == 'arrivedAtPickup' ||
        trip['status'] == 'ongoing';
    final isScheduled = trip['isScheduled'] == true && !isActive;

    // Format date and time
    String dateStr = '';
    String timeStr = '';

    if (trip['createdAt'] != null) {
      final date = trip['createdAt'].toDate();
      dateStr = intl.DateFormat('yyyy/MM/dd').format(date);
      timeStr = intl.DateFormat('HH:mm').format(date);
    }

    // Format scheduled date and time
    String scheduledDateStr = '';
    String scheduledTimeStr = '';

    if (trip['scheduledTime'] != null) {
      final scheduledDate = trip['scheduledTime'].toDate();
      scheduledDateStr = intl.DateFormat('yyyy/MM/dd').format(scheduledDate);
      scheduledTimeStr = intl.DateFormat('HH:mm').format(scheduledDate);
    }

    // Determine status color and text
    Color statusColor;
    String statusText;

    switch (trip['status']) {
      case 'searching':
        statusColor = Colors.orange;
        statusText = 'جاري البحث عن سائق';
        break;
      case 'assigned':
        statusColor = Colors.blue;
        statusText = 'تم تعيين سائق';
        break;
      case 'enRouteToPickup':
        statusColor = Colors.blue;
        statusText = 'السائق في الطريق إليك';
        break;
      case 'arrivedAtPickup':
        statusColor = Colors.green;
        statusText = 'السائق وصل إلى موقعك';
        break;
      case 'ongoing':
        statusColor = Colors.green;
        statusText = 'الرحلة جارية';
        break;
      case 'completed':
        statusColor = Colors.green.shade800;
        statusText = 'مكتملة';
        break;
      case 'cancelledByUser':
        statusColor = Colors.red;
        statusText = 'ملغية من قبلك';
        break;
      case 'cancelledByDriver':
        statusColor = Colors.red;
        statusText = 'ملغية من قبل السائق';
        break;
      case 'noDriversAvailable':
        statusColor = Colors.grey;
        statusText = 'لا يوجد سائقين متاحين';
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير معروف';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => controller.viewTripDetails(trip['id']),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trip header with status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      trip['directionTitle'] ?? 'رحلة',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: statusColor.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      statusText,
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Trip details
              Row(
                children: [
                  // Date and time column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailRow(
                          context,
                          Icons.calendar_today_outlined,
                          isScheduled
                              ? 'تاريخ الطلب: $dateStr'
                              : 'التاريخ: $dateStr',
                        ),
                        const SizedBox(height: 8),
                        _buildDetailRow(
                          context,
                          Icons.access_time_outlined,
                          isScheduled
                              ? 'وقت الطلب: $timeStr'
                              : 'الوقت: $timeStr',
                        ),
                        if (isScheduled) ...[
                          const SizedBox(height: 8),
                          _buildDetailRow(
                            context,
                            Icons.event,
                            'موعد الرحلة: $scheduledDateStr',
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(height: 8),
                          _buildDetailRow(
                            context,
                            Icons.schedule,
                            'وقت الرحلة: $scheduledTimeStr',
                            color: theme.colorScheme.primary,
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Price and details column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailRow(
                          context,
                          Icons.attach_money,
                          'السعر: ${trip['totalPrice']?.toStringAsFixed(2) ?? trip['price']?.toStringAsFixed(2) ?? '0.00'} ريال',
                        ),
                        const SizedBox(height: 8),
                        _buildDetailRow(
                          context,
                          Icons.people_outline,
                          'الركاب: ${trip['passengers'] ?? 1}',
                        ),
                        const SizedBox(height: 8),
                        _buildDetailRow(
                          context,
                          Icons.work_outline,
                          'الحقائب: ${trip['bags'] ?? 0}',
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Driver info if assigned
              if (trip['driverId'] != null && trip['driverName'] != null) ...[
                const Divider(height: 24),
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor:
                          theme.colorScheme.primary.withOpacity(0.1),
                      child: Icon(
                        Icons.person,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'السائق: ${trip['driverName']}',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (trip['driverVehicle'] != null)
                            Text(
                              'المركبة: ${trip['driverVehicle']}',
                              style: theme.textTheme.bodySmall,
                            ),
                        ],
                      ),
                    ),
                    if (trip['driverRating'] != null)
                      Row(
                        children: [
                          Text(
                            trip['driverRating'].toStringAsFixed(1),
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 18,
                          ),
                        ],
                      ),
                  ],
                ),
              ],

              // Action buttons
              if (isActive || isScheduled) ...[
                const Divider(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (isActive)
                      TextButton.icon(
                        onPressed: () => controller.navigateToChat(trip['id']),
                        icon: const Icon(Icons.chat_outlined),
                        label: const Text('محادثة'),
                        style: TextButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                        ),
                      ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed: () => controller.cancelTrip(trip['id']),
                      icon: const Icon(Icons.cancel_outlined),
                      label: const Text('إلغاء'),
                      style: TextButton.styleFrom(
                        foregroundColor: theme.colorScheme.error,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    IconData icon,
    String text, {
    Color? color,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color ?? Colors.grey.shade700,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              color: color ?? Colors.grey.shade700,
              fontSize: 13,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
