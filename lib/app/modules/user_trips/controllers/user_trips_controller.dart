import 'package:ai_delivery_app/app/data/models/trip_request_model.dart';
import 'package:ai_delivery_app/app/modules/home/<USER>/trip_controller.dart';
import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserTripsController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  late TabController tabController;
  final RxInt currentTabIndex = 0.obs;

  final RxList<Map<String, dynamic>> activeTrips = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> scheduledTrips =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> pastTrips = <Map<String, dynamic>>[].obs;

  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);

  late TripController tripController;

  @override
  void onInit() {
    super.onInit();

    // Initialize tab controller
    tabController = TabController(length: 3, vsync: this);
    tabController.addListener(() {
      currentTabIndex.value = tabController.index;
    });

    // Get trip controller
    tripController = Get.find<TripController>();

    // Load trips
    loadTrips();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  Future<void> loadTrips() async {
    if (_auth.currentUser == null) {
      errorMessage.value = 'يرجى تسجيل الدخول لعرض رحلاتك';
      return;
    }

    isLoading.value = true;
    errorMessage.value = null;

    try {
      final userId = _auth.currentUser!.uid;

      // Get all trips for the current user
      final snapshot = await _firestore
          .collection('tripRequests')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      // Clear existing lists
      activeTrips.clear();
      scheduledTrips.clear();
      pastTrips.clear();

      // Process trips
      for (final doc in snapshot.docs) {
        final trip = TripRequest.fromFirestore(doc);

        // Convert to a map for easier handling in the UI
        final tripMap = {
          'id': trip.id,
          'userId': trip.userId,
          'directionId': trip.directionId,
          'directionTitle': trip.directionTitle,
          'pickupLocation': trip.pickupLocation,
          'dropoffLocation': trip.dropoffLocation,
          'passengers': trip.passengers,
          'bags': trip.bags,
          'notes': trip.notes,
          'basePrice': trip.basePrice,
          'appFee': trip.appFee,
          'taxAmount': trip.taxAmount,
          'discountAmount': trip.discountAmount,
          'totalPrice': trip.totalPrice,
          'status': TripRequest.statusToString(trip.status),
          'driverId': trip.driverId,
          'createdAt': trip.createdAt,
          'assignedAt': trip.assignedAt,
          'completedAt': trip.completedAt,
          'isScheduled': trip.isScheduled,
          'scheduledTime': trip.scheduledTime,
          'distanceText': trip.distanceText,
          'durationText': trip.durationText,
          'routePolyline': trip.routePolyline,
          'driverName': trip.driverName,
          'driverVehicle': trip.driverVehicle,
          'driverRating': trip.driverRating,
          'userName': trip.userName,
          'userRating': trip.userRating,
          'unreadMessageCount': trip.unreadMessageCount,
          'driverUnreadMessageCount': trip.driverUnreadMessageCount,
        };

        // Categorize trips
        if (trip.status == TripStatus.searching ||
            trip.status == TripStatus.assigned ||
            trip.status == TripStatus.enRouteToPickup ||
            trip.status == TripStatus.arrivedAtPickup ||
            trip.status == TripStatus.ongoing) {
          activeTrips.add(tripMap);
        } else if (trip.isScheduled &&
            trip.scheduledTime != null &&
            trip.scheduledTime!.toDate().isAfter(DateTime.now()) &&
            trip.status != TripStatus.cancelledByUser &&
            trip.status != TripStatus.cancelledByDriver &&
            trip.status != TripStatus.completed) {
          scheduledTrips.add(tripMap);
        } else {
          pastTrips.add(tripMap);
        }
      }

      // Sort scheduled trips by scheduled time
      scheduledTrips.sort((a, b) {
        final aTime = a['scheduledTime'] as Timestamp;
        final bTime = b['scheduledTime'] as Timestamp;
        return aTime.compareTo(bTime);
      });
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الرحلات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  void viewTripDetails(String tripId) {
    // Find the trip in our lists
    final trip = _findTripById(tripId);

    if (trip != null) {
      // If it's an active trip, navigate to the tracking view
      if (trip['status'] == 'searching' ||
          trip['status'] == 'assigned' ||
          trip['status'] == 'enRouteToPickup' ||
          trip['status'] == 'arrivedAtPickup' ||
          trip['status'] == 'ongoing') {
        // Set the current trip in the trip controller
        tripController.currentTrip.value = TripRequest.fromMap(trip);

        // Start listening to trip updates
        tripController.listenToTrip(tripId);

        // Navigate to the home view with tracking state
        Get.toNamed(Routes.HOME,
            arguments: {'resumeTrip': true, 'tripId': tripId});
      } else {
        // For scheduled or past trips, show a trip details dialog
        _showTripDetailsDialog(trip);
      }
    }
  }

  void navigateToChat(String tripId) {
    // Set the current trip in the trip controller
    final trip = _findTripById(tripId);
    if (trip != null) {
      tripController.currentTrip.value = TripRequest.fromMap(trip);
      Get.toNamed(Routes.CHAT, arguments: {'tripId': tripId});
    }
  }

  void cancelTrip(String tripId) {
    // Set the current trip in the trip controller
    final trip = _findTripById(tripId);
    if (trip != null) {
      tripController.currentTrip.value = TripRequest.fromMap(trip);
      tripController.showCancellationDialog();

      // Reload trips after a short delay to reflect the cancellation
      Future.delayed(const Duration(seconds: 2), () {
        loadTrips();
      });
    }
  }

  Map<String, dynamic>? _findTripById(String tripId) {
    // Look in active trips
    for (final trip in activeTrips) {
      if (trip['id'] == tripId) {
        return trip;
      }
    }

    // Look in scheduled trips
    for (final trip in scheduledTrips) {
      if (trip['id'] == tripId) {
        return trip;
      }
    }

    // Look in past trips
    for (final trip in pastTrips) {
      if (trip['id'] == tripId) {
        return trip;
      }
    }

    return null;
  }

  void _showTripDetailsDialog(Map<String, dynamic> trip) {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: Text('تفاصيل الرحلة: ${trip['directionTitle']}'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDialogDetailRow('الحالة', _getStatusText(trip['status'])),
                const SizedBox(height: 8),

                // Price section
                const Text(
                  'تفاصيل السعر',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDialogDetailRow('السعر الأساسي',
                    '${trip['basePrice']?.toStringAsFixed(2) ?? '0.00'} ريال'),
                const SizedBox(height: 4),
                _buildDialogDetailRow('رسوم التطبيق',
                    '${trip['appFee']?.toStringAsFixed(2) ?? '0.00'} ريال'),
                const SizedBox(height: 4),
                _buildDialogDetailRow('الضريبة',
                    '${trip['taxAmount']?.toStringAsFixed(2) ?? '0.00'} ريال'),
                if (trip['discountAmount'] != null &&
                    trip['discountAmount'] > 0) ...[
                  const SizedBox(height: 4),
                  _buildDialogDetailRow('الخصم',
                      '- ${trip['discountAmount']?.toStringAsFixed(2) ?? '0.00'} ريال',
                      textColor: Colors.green),
                ],
                const SizedBox(height: 4),
                _buildDialogDetailRow('الإجمالي',
                    '${trip['totalPrice']?.toStringAsFixed(2) ?? trip['price']?.toStringAsFixed(2) ?? '0.00'} ريال',
                    textColor: Colors.blue.shade800, isBold: true),

                const SizedBox(height: 16),
                _buildDialogDetailRow(
                    'عدد الركاب', '${trip['passengers'] ?? 1}'),
                const SizedBox(height: 8),
                _buildDialogDetailRow('عدد الحقائب', '${trip['bags'] ?? 0}'),
                if (trip['notes'] != null && trip['notes'].isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildDialogDetailRow('ملاحظات', trip['notes']),
                ],
                if (trip['isScheduled'] == true &&
                    trip['scheduledTime'] != null) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'معلومات الموعد',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildDialogDetailRow(
                    'تاريخ الرحلة',
                    _formatTimestamp(trip['scheduledTime'], 'yyyy/MM/dd'),
                  ),
                  const SizedBox(height: 8),
                  _buildDialogDetailRow(
                    'وقت الرحلة',
                    _formatTimestamp(trip['scheduledTime'], 'HH:mm'),
                  ),
                ],
                if (trip['driverId'] != null && trip['driverName'] != null) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'معلومات السائق',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildDialogDetailRow('اسم السائق', trip['driverName']),
                  if (trip['driverVehicle'] != null) ...[
                    const SizedBox(height: 8),
                    _buildDialogDetailRow('المركبة', trip['driverVehicle']),
                  ],
                  if (trip['driverRating'] != null) ...[
                    const SizedBox(height: 8),
                    _buildDialogDetailRow(
                      'تقييم السائق',
                      '${trip['driverRating'].toStringAsFixed(1)} ★',
                    ),
                  ],
                ],
                const SizedBox(height: 16),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDialogDetailRow(
                  'تاريخ الطلب',
                  _formatTimestamp(trip['createdAt'], 'yyyy/MM/dd HH:mm'),
                ),
                if (trip['completedAt'] != null) ...[
                  const SizedBox(height: 8),
                  _buildDialogDetailRow(
                    'تاريخ الإكمال',
                    _formatTimestamp(trip['completedAt'], 'yyyy/MM/dd HH:mm'),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
            if (trip['isScheduled'] == true &&
                trip['scheduledTime'] != null &&
                trip['scheduledTime'].toDate().isAfter(DateTime.now()) &&
                trip['status'] != 'cancelledByUser' &&
                trip['status'] != 'cancelledByDriver')
              TextButton(
                onPressed: () {
                  Get.back();
                  cancelTrip(trip['id']);
                },
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
                child: const Text('إلغاء الرحلة'),
              ),
          ],
        ),
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'searching':
        return 'جاري البحث عن سائق';
      case 'assigned':
        return 'تم تعيين سائق';
      case 'enRouteToPickup':
        return 'السائق في الطريق إليك';
      case 'arrivedAtPickup':
        return 'السائق وصل إلى موقعك';
      case 'ongoing':
        return 'الرحلة جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelledByUser':
        return 'ملغية من قبلك';
      case 'cancelledByDriver':
        return 'ملغية من قبل السائق';
      case 'noDriversAvailable':
        return 'لا يوجد سائقين متاحين';
      default:
        return 'غير معروف';
    }
  }

  String _formatTimestamp(Timestamp timestamp, String format) {
    final dateTime = timestamp.toDate();
    return format.contains('HH:mm')
        ? '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}'
        : '${dateTime.year}/${dateTime.month}/${dateTime.day}';
  }

  Widget _buildDialogDetailRow(String label, String value,
      {Color? textColor, bool isBold = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: textColor,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }
}
