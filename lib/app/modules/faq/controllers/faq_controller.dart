import 'package:ai_delivery_app/app/routes/app_pages.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class FAQController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final TextEditingController searchController = TextEditingController();
  final RxList<Map<String, dynamic>> faqs = <Map<String, dynamic>>[].obs;
  final RxList<String> faqCategories = <String>[].obs;
  final RxList<Map<String, dynamic>> searchResults =
      <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;
  final Rx<String?> errorMessage = Rx<String?>(null);
  final RxString searchQuery = ''.obs;
  final RxString selectedCategory = 'all'.obs;

  @override
  void onInit() {
    super.onInit();
    loadFAQs();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadFAQs() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final snapshot =
          await _firestore.collection('faqs').orderBy('order').get();

      final List<Map<String, dynamic>> loadedFaqs = [];
      final Set<String> categories = {};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        data['id'] = doc.id;
        loadedFaqs.add(data);

        if (data['category'] != null) {
          categories.add(data['category']);
        }
      }

      faqs.value = loadedFaqs;
      faqCategories.value = categories.toList()..sort();

      // If no FAQs found in Firestore, use default ones
      if (faqs.isEmpty) {
        _loadDefaultFAQs();
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الأسئلة الشائعة: $e';
      _loadDefaultFAQs(); // Fallback to default FAQs
    } finally {
      isLoading.value = false;
    }
  }

  void _loadDefaultFAQs() {
    final defaultFaqs = [
      {
        'id': '1',
        'question': 'كيف يمكنني طلب رحلة؟',
        'answer':
            'يمكنك طلب رحلة من خلال فتح التطبيق واختيار وجهتك، ثم النقر على زر "طلب رحلة". سيتم توصيلك بأقرب سائق متاح.',
        'category': 'الرحلات',
        'order': 1,
        'links': [],
      },
      {
        'id': '2',
        'question': 'كيف يمكنني إضافة طريقة دفع؟',
        'answer':
            'يمكنك إضافة طريقة دفع من خلال الانتقال إلى قسم "المحفظة" في القائمة الجانبية، ثم النقر على "إضافة طريقة دفع" واتباع التعليمات.',
        'category': 'الدفع',
        'order': 2,
        'links': [],
      },
      {
        'id': '3',
        'question': 'كيف يمكنني تغيير معلومات حسابي؟',
        'answer':
            'يمكنك تعديل معلومات حسابك من خلال الانتقال إلى "الملف الشخصي" في القائمة الجانبية، ثم النقر على "تعديل الملف الشخصي".',
        'category': 'الحساب',
        'order': 3,
        'links': [],
      },
      {
        'id': '4',
        'question': 'ماذا أفعل إذا نسيت كلمة المرور؟',
        'answer':
            'يمكنك إعادة تعيين كلمة المرور من خلال النقر على "نسيت كلمة المرور" في شاشة تسجيل الدخول، ثم إدخال بريدك الإلكتروني لتلقي رابط إعادة التعيين.',
        'category': 'الحساب',
        'order': 4,
        'links': [],
      },
      {
        'id': '5',
        'question': 'كيف يمكنني إلغاء رحلة؟',
        'answer':
            'يمكنك إلغاء الرحلة قبل وصول السائق من خلال النقر على زر "إلغاء الرحلة" في شاشة تتبع الرحلة. يرجى ملاحظة أنه قد يتم تطبيق رسوم إلغاء إذا تم الإلغاء بعد قبول السائق للرحلة.',
        'category': 'الرحلات',
        'order': 5,
        'links': [],
      },
      {
        'id': '6',
        'question': 'كيف يمكنني التواصل مع السائق؟',
        'answer':
            'بعد قبول الرحلة، يمكنك التواصل مع السائق من خلال النقر على زر "الاتصال" أو "الدردشة" في شاشة تتبع الرحلة.',
        'category': 'الرحلات',
        'order': 6,
        'links': [],
      },
      {
        'id': '7',
        'question': 'كيف يمكنني تقييم السائق؟',
        'answer':
            'بعد انتهاء الرحلة، ستظهر لك شاشة لتقييم السائق. يمكنك تقييم السائق من 1 إلى 5 نجوم وإضافة تعليق إذا رغبت في ذلك.',
        'category': 'الرحلات',
        'order': 7,
        'links': [],
      },
      {
        'id': '8',
        'question': 'ما هي طرق الدفع المتاحة؟',
        'answer':
            'يمكنك الدفع باستخدام البطاقات الائتمانية (فيزا، ماستركارد)، أو من خلال محفظة التطبيق، أو نقدًا للسائق مباشرة.',
        'category': 'الدفع',
        'order': 8,
        'links': [],
      },
      {
        'id': '9',
        'question': 'كيف يمكنني الحصول على إيصال للرحلة؟',
        'answer':
            'يتم إرسال إيصال الرحلة تلقائيًا إلى بريدك الإلكتروني بعد انتهاء الرحلة. يمكنك أيضًا الاطلاع على جميع الإيصالات من خلال قسم "سجل الرحلات" في التطبيق.',
        'category': 'الرحلات',
        'order': 9,
        'links': [],
      },
      {
        'id': '10',
        'question': 'ماذا أفعل إذا نسيت شيئًا في سيارة السائق؟',
        'answer':
            'إذا نسيت شيئًا في سيارة السائق، يمكنك التواصل معنا من خلال قسم "الدعم" في التطبيق. سنساعدك في التواصل مع السائق لاسترداد أغراضك.',
        'category': 'عام',
        'order': 10,
        'links': [],
      },
    ];

    faqs.value = defaultFaqs;

    final Set<String> categories = {};
    for (final faq in defaultFaqs) {
      if (faq['category'] != null) {
        categories.add(faq['category'].toString());
      }
    }

    faqCategories.value = categories.toList()..sort();
  }

  List<Map<String, dynamic>> getFAQsByCategory(String category) {
    return faqs.where((faq) => faq['category'] == category).toList();
  }

  void setCategory(String category) {
    selectedCategory.value = category;
  }

  void onSearchChanged(String query) {
    searchQuery.value = query.trim();

    if (searchQuery.isEmpty) {
      searchResults.clear();
      return;
    }

    final results = faqs.where((faq) {
      final question = faq['question'].toString().toLowerCase();
      final answer = faq['answer'].toString().toLowerCase();
      final searchLower = searchQuery.toLowerCase();

      return question.contains(searchLower) || answer.contains(searchLower);
    }).toList();

    searchResults.value = results;
  }

  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    searchResults.clear();
  }

  void showSearchDialog() {
    Get.dialog(
      Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('البحث في الأسئلة الشائعة'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'اكتب كلمات البحث هنا',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: onSearchChanged,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
                clearSearch();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
                // Search is already handled by onSearchChanged
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }

  void navigateToSupportChat() {
    Get.toNamed(Routes.SUPPORT_CHAT);
  }

  Future<void> openLink(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      Get.snackbar(
        'خطأ',
        'لا يمكن فتح الرابط: $url',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
