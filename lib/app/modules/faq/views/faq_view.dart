import 'package:ai_delivery_app/app/modules/faq/controllers/faq_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FAQView extends GetView<FAQController> {
  const FAQView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الأسئلة الشائعة'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => controller.showSearchDialog(),
            ),
          ],
        ),
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (controller.errorMessage.value != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
                  const SizedBox(height: 16),
                  Text(
                    controller.errorMessage.value!,
                    style: theme.textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: controller.loadFAQs,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }
          
          if (controller.faqCategories.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.question_answer,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد أسئلة شائعة متاحة حاليًا',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            );
          }
          
          return Column(
            children: [
              // Search Bar
              if (controller.searchQuery.value.isNotEmpty)
                _buildSearchBar(context),
              
              // FAQ Categories and Questions
              Expanded(
                child: controller.searchQuery.value.isNotEmpty
                    ? _buildSearchResults(context)
                    : _buildCategoriesList(context),
              ),
            ],
          );
        }),
        floatingActionButton: FloatingActionButton(
          onPressed: () => controller.navigateToSupportChat(),
          backgroundColor: theme.colorScheme.primary,
          child: const Icon(Icons.support_agent),
          tooltip: 'تحدث مع الدعم',
        ),
      ),
    );
  }
  
  Widget _buildSearchBar(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      color: theme.colorScheme.surface,
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'البحث في الأسئلة الشائعة',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: controller.clearSearch,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              onChanged: controller.onSearchChanged,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchResults(BuildContext context) {
    final theme = Theme.of(context);
    
    if (controller.searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج للبحث',
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'حاول استخدام كلمات مختلفة أو تواصل مع الدعم',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.searchResults.length,
      itemBuilder: (context, index) {
        final faq = controller.searchResults[index];
        return _buildFAQItem(context, faq);
      },
    );
  }
  
  Widget _buildCategoriesList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.faqCategories.length,
      itemBuilder: (context, index) {
        final category = controller.faqCategories[index];
        final faqs = controller.getFAQsByCategory(category);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCategoryHeader(context, category),
            const SizedBox(height: 8),
            ...faqs.map((faq) => _buildFAQItem(context, faq)).toList(),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
  
  Widget _buildCategoryHeader(BuildContext context, String category) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getCategoryIcon(category),
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            category,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFAQItem(BuildContext context, Map<String, dynamic> faq) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        title: Text(
          faq['question'],
          style: theme.textTheme.titleMedium,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  faq['answer'],
                  style: theme.textTheme.bodyMedium,
                ),
                if (faq['links'] != null && (faq['links'] as List).isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'روابط مفيدة:',
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...(faq['links'] as List).map((link) => _buildLinkItem(context, link)),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildLinkItem(BuildContext context, Map<String, dynamic> link) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: () => controller.openLink(link['url']),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Icon(
              Icons.link,
              color: theme.colorScheme.primary,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                link['title'],
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'عام':
        return Icons.info_outline;
      case 'الحساب':
        return Icons.person_outline;
      case 'الدفع':
        return Icons.payment;
      case 'الرحلات':
        return Icons.directions_car_outlined;
      case 'التطبيق':
        return Icons.smartphone;
      case 'السائقين':
        return Icons.drive_eta_outlined;
      default:
        return Icons.help_outline;
    }
  }
}
