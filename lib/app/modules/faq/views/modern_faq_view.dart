import 'package:ai_delivery_app/app/modules/faq/controllers/faq_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ModernFAQView extends GetView<FAQController> {
  const ModernFAQView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverAppBar(
                expandedHeight: 150.0,
                floating: false,
                pinned: true,
                title: const Text('الأسئلة الشائعة'),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.search),
                    onPressed: () => controller.showSearchDialog(),
                    tooltip: 'بحث',
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Gradient background
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topRight,
                            end: Alignment.bottomLeft,
                            colors: [
                              theme.colorScheme.primary,
                              theme.colorScheme.primary.withOpacity(0.7),
                            ],
                          ),
                        ),
                      ),
                      // Pattern overlay
                      Opacity(
                        opacity: 0.1,
                        child: Container(
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/pattern.png'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      // FAQ icon
                      Positioned(
                        right: 20,
                        bottom: 20,
                        child: Icon(
                          Icons.question_answer_rounded,
                          size: 40,
                          color: theme.colorScheme.onPrimary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverPersistentHeader(
                delegate: _SliverCategoryTabBarDelegate(
                  controller: controller,
                  theme: theme,
                ),
                pinned: true,
              ),
            ];
          },
          body: Obx(() {
            if (controller.isLoading.value) {
              return const Center(child: CircularProgressIndicator());
            }
            
            if (controller.errorMessage.value != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: theme.colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      controller.errorMessage.value!,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () => controller.loadFAQs(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }
            
            if (controller.faqCategories.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.question_answer,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد أسئلة شائعة متاحة حاليًا',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              );
            }
            
            return controller.searchQuery.value.isNotEmpty
                ? _buildSearchResults(context)
                : _buildCategoriesList(context);
          }),
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => controller.navigateToSupportChat(),
          icon: const Icon(Icons.support_agent),
          label: const Text('تحدث مع الدعم'),
          backgroundColor: theme.colorScheme.primary,
        ),
      ),
    );
  }
  
  Widget _buildSearchResults(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Search bar
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                Icons.search,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'نتائج البحث عن: ${controller.searchQuery.value}',
                  style: theme.textTheme.bodyMedium,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => controller.clearSearch(),
                color: Colors.grey.shade600,
              ),
            ],
          ),
        ),
        
        // Results
        Expanded(
          child: controller.searchResults.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد نتائج للبحث',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: controller.searchResults.length,
                  itemBuilder: (context, index) {
                    final faq = controller.searchResults[index];
                    return _buildFAQItem(context, faq);
                  },
                ),
        ),
      ],
    );
  }
  
  Widget _buildCategoriesList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.faqCategories.length,
      itemBuilder: (context, index) {
        final category = controller.faqCategories[index];
        final faqs = controller.getFAQsByCategory(category);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCategoryHeader(context, category),
            const SizedBox(height: 8),
            ...faqs.map((faq) => _buildFAQItem(context, faq)).toList(),
            const SizedBox(height: 24),
          ],
        );
      },
    );
  }
  
  Widget _buildCategoryHeader(BuildContext context, String category) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            _getCategoryIcon(category),
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Text(
            category,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFAQItem(BuildContext context, Map<String, dynamic> faq) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          colorScheme: theme.colorScheme.copyWith(
            background: Colors.white,
          ),
        ),
        child: ExpansionTile(
          title: Text(
            faq['question'],
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          childrenPadding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          expandedCrossAxisAlignment: CrossAxisAlignment.start,
          iconColor: theme.colorScheme.primary,
          collapsedIconColor: Colors.grey.shade600,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          children: [
            const Divider(),
            const SizedBox(height: 8),
            Text(
              faq['answer'],
              style: theme.textTheme.bodyMedium?.copyWith(
                height: 1.5,
              ),
            ),
            if (faq['links'] != null && (faq['links'] as List).isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'روابط مفيدة:',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...(faq['links'] as List).map((link) => _buildLinkItem(context, link)),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildLinkItem(BuildContext context, dynamic link) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: () {
        // Handle link tap
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Icon(
              Icons.link,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                link['title'] ?? link['url'] ?? '',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'عام':
        return Icons.info_outline;
      case 'الرحلات':
        return Icons.directions_car_outlined;
      case 'الدفع':
        return Icons.payment_outlined;
      case 'الحساب':
        return Icons.person_outline;
      case 'السائقين':
        return Icons.drive_eta_outlined;
      case 'تقني':
        return Icons.devices_outlined;
      default:
        return Icons.help_outline;
    }
  }
}

class _SliverCategoryTabBarDelegate extends SliverPersistentHeaderDelegate {
  final FAQController controller;
  final ThemeData theme;
  
  _SliverCategoryTabBarDelegate({
    required this.controller,
    required this.theme,
  });
  
  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: 60,
      color: Colors.white,
      child: Obx(() {
        if (controller.faqCategories.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return ListView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          children: [
            _buildCategoryChip(
              context,
              'الكل',
              icon: Icons.all_inclusive,
              isSelected: controller.selectedCategory.value == 'all',
              onTap: () => controller.setCategory('all'),
            ),
            ...controller.faqCategories.map((category) {
              return _buildCategoryChip(
                context,
                category,
                icon: _getCategoryIcon(category),
                isSelected: controller.selectedCategory.value == category,
                onTap: () => controller.setCategory(category),
              );
            }).toList(),
          ],
        );
      }),
    );
  }
  
  Widget _buildCategoryChip(
    BuildContext context,
    String label, {
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      child: Material(
        color: isSelected ? theme.colorScheme.primary : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(30),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(30),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 18,
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'عام':
        return Icons.info_outline;
      case 'الرحلات':
        return Icons.directions_car_outlined;
      case 'الدفع':
        return Icons.payment_outlined;
      case 'الحساب':
        return Icons.person_outline;
      case 'السائقين':
        return Icons.drive_eta_outlined;
      case 'تقني':
        return Icons.devices_outlined;
      default:
        return Icons.help_outline;
    }
  }
  
  @override
  double get maxExtent => 60;
  
  @override
  double get minExtent => 60;
  
  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
