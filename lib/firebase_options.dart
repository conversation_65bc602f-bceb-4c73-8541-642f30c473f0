// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBjUs9d4CJoPLzy7x1-Gupn8IhsPFFfTxk',
    appId: '1:495125321645:web:80b9b79a99b540e3c3f324',
    messagingSenderId: '495125321645',
    projectId: 'malathdb-27d30',
    authDomain: 'malathdb-27d30.firebaseapp.com',
    storageBucket: 'malathdb-27d30.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDMVeGJ_5oLWokqjjrSn0OD50856RPCG9g',
    appId: '1:605719768914:android:31365b55d8a0a921b0d48b',
    messagingSenderId: '605719768914',
    projectId: 'erkap-593c1',
    storageBucket: 'erkap-593c1.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBBbmbA-rtlq5S7_k5sp-d16q4d_LCvI3I',
    appId: '1:605719768914:ios:7bf0b38ce9a58f84b0d48b',
    messagingSenderId: '605719768914',
    projectId: 'erkap-593c1',
    storageBucket: 'erkap-593c1.firebasestorage.app',
    androidClientId: '605719768914-8j55vs61a2u9kb0v8icd07tsnqlv76tn.apps.googleusercontent.com',
    iosClientId: '605719768914-95mnfndpd629pnacoak486svjr55oahr.apps.googleusercontent.com',
    iosBundleId: 'com.example.aiDeliveryApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyD8o1_BfZwulsPBEhKFT5_aqgD-FsW1iL4',
    appId: '1:495125321645:ios:8e7c8258cd2c032dc3f324',
    messagingSenderId: '495125321645',
    projectId: 'malathdb-27d30',
    storageBucket: 'malathdb-27d30.appspot.com',
    iosClientId: '495125321645-e6bf4284glquno1gunqbiql1jo3l8vhf.apps.googleusercontent.com',
    iosBundleId: 'com.example.aiDeliveryApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBjUs9d4CJoPLzy7x1-Gupn8IhsPFFfTxk',
    appId: '1:495125321645:web:87637dc1d7f18e89c3f324',
    messagingSenderId: '495125321645',
    projectId: 'malathdb-27d30',
    authDomain: 'malathdb-27d30.firebaseapp.com',
    storageBucket: 'malathdb-27d30.appspot.com',
  );
}