import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'app/data/seeders/data_seeder.dart';
import 'app/data/services/direction_seeder.dart';
import 'app/data/services/app_settings_seeder.dart';
import 'app/data/services/coupon_seeder.dart';
import 'app/data/services/translation_service.dart';
import 'app/routes/app_pages.dart';
import 'app/services/notification_service.dart';
import 'app/services/payment_service.dart';
import 'app/services/app_settings_service.dart';
import 'app/theme/app_theme.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize GetStorage for language preferences
  await GetStorage.init();

  // Initialize services
  await Get.putAsync(() => NotificationService().init());
  await Get.putAsync(() => AppSettingsService().init());
  await Get.putAsync(() => PaymentService().init());

  // Seed initial data to Firestore
  // await DataSeeder().seedAllData();

  // Seed directions data
  try {
    await DirectionSeeder().seedDirections();
  } catch (e) {
    print('Error seeding directions: $e');
  }

  // Seed app settings
  try {
    await AppSettingsSeeder().seedAppSettings();
  } catch (e) {
    print('Error seeding app settings: $e');
  }

  // Seed coupons
  try {
    await CouponSeeder().seedCoupons();
  } catch (e) {
    print('Error seeding coupons: $e');
  }

  // Determine initial route based on auth state
  // final String initialRoute = FirebaseAuth.instance.currentUser == null
  //     ? Routes.LOGIN // If not logged in, go to Login
  //     : Routes.HOME;   // If logged in, go to Home
  String initialRoute = Routes.LOGIN;
  User? firebaseUser = FirebaseAuth.instance.currentUser;
  if (firebaseUser != null) {
    DocumentSnapshot userDoc = await FirebaseFirestore.instance
        .collection('users')
        .doc(firebaseUser.uid)
        .get();

    if (userDoc.exists) {
      String userRole = userDoc.get('role') ?? 'user';
      if (userRole == 'driver') {
        initialRoute = Routes.DRIVER_HOME;
      } else if (userRole == 'support') {
        initialRoute = Routes.SUPPORT_DASHBOARD;
      } else {
        initialRoute = Routes.HOME; // Use our new home route for testing
      }
    } else {
      initialRoute = Routes.HOME; // Default if Firestore data missing
    }
  }
  runApp(
    GetMaterialApp(
      title: "Delivery App",
      // Use the determined initial route
      initialRoute: initialRoute,
      getPages: AppPages.routes,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.lightTheme,
      themeMode: ThemeMode.light,
      debugShowCheckedModeBanner: false,
      // Internationalization settings
      translations: TranslationService(),
      locale: TranslationService.defaultLocale,
      fallbackLocale: TranslationService.fallbackLocale,
      // Apply text direction based on locale
      builder: (context, child) {
        return Directionality(
          textDirection: TranslationService.getTextDirection(),
          child: child!,
        );
      },
    ),
  );
}
