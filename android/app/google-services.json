{"project_info": {"project_number": "605719768914", "project_id": "erkap-593c1", "storage_bucket": "erkap-593c1.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:605719768914:android:e9e5dff242489ddab0d48b", "android_client_info": {"package_name": "com.erkap.erkap_driver"}}, "oauth_client": [{"client_id": "605719768914-iqmr2iqvg9hlt2labics7r7q7aaolpjc.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.erkap.erkap_driver", "certificate_hash": "a827411a45fd590b1058fcdfd01389566e5e6a0d"}}, {"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDMVeGJ_5oLWokqjjrSn0OD50856RPCG9g"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}, {"client_id": "605719768914-51s9qndhjluksdano1jc4v5horh5ojqh.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.erkap.erkapDriver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:605719768914:android:31365b55d8a0a921b0d48b", "android_client_info": {"package_name": "com.example.ai_delivery_app"}}, "oauth_client": [{"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDMVeGJ_5oLWokqjjrSn0OD50856RPCG9g"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}, {"client_id": "605719768914-51s9qndhjluksdano1jc4v5horh5ojqh.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.erkap.erkapDriver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:605719768914:android:8a7cb786baa8cd7bb0d48b", "android_client_info": {"package_name": "com.example.erkap_user"}}, "oauth_client": [{"client_id": "605719768914-8j55vs61a2u9kb0v8icd07tsnqlv76tn.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.erkap_user", "certificate_hash": "a827411a45fd590b1058fcdfd01389566e5e6a0d"}}, {"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDMVeGJ_5oLWokqjjrSn0OD50856RPCG9g"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}, {"client_id": "605719768914-51s9qndhjluksdano1jc4v5horh5ojqh.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.erkap.erkapDriver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:605719768914:android:64a3e97e961d0dcfb0d48b", "android_client_info": {"package_name": "com.ksa.iu_app_undrestood"}}, "oauth_client": [{"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDMVeGJ_5oLWokqjjrSn0OD50856RPCG9g"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "605719768914-44dkfqirf6r3563birkjdbpc4nna4u0j.apps.googleusercontent.com", "client_type": 3}, {"client_id": "605719768914-51s9qndhjluksdano1jc4v5horh5ojqh.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.erkap.erkapDriver"}}]}}}], "configuration_version": "1"}